{"name": "sinomobile", "version": "0.0.1", "private": true, "main": "node_modules/expo/AppEntry.js", "scripts": {"android:env:uat": "ENV_MODE=uat npx react-native run-android", "android:env:prod": "ENV_MODE=production npx react-native run-android", "android:uat": "ENVFILE=.env.uat npx react-native run-android --variant=uatdebug --appId com.sinomobile.uat", "android:prod": "ENVFILE=.env.production npx react-native run-android --variant=productiondebug --appId com.sinomobile.mobile", "android:prod2": "react-native bundle --dev false --platform android --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && rm -rf android/app/src/main/res/drawable-* android/app/src/main/res/raw && cd android && ./gradlew assembleDebug && cd .. && cp -f android/app/build/outputs/apk/debug/app-debug.apk \"G:/我的雲端硬碟/projects/sino\"", "android:prod3": "react-native bundle --dev false --platform android --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && rm -rf android/app/src/main/res/drawable-* android/app/src/main/res/raw && cd android && ./gradlew assembleRelease && cd .. && cp -f android/app/build/outputs/apk/release/app-release.apk \"G:/我的雲端硬碟/projects/sino/sino.apk\"", "cp": "cp -f \"G:/我的雲端硬碟/projects/sino/sino.apk\" \"I:/其他電腦/My Computer/freelance/biz_forest/20250224 Sino/app-release.apk\"", "android:bundle:uat": "ENVFILE=.env.uat npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "android:uat:build": "cd android && ENVFILE=.env.uat ./gradlew assembleUatDebug && cd -", "ios:uat": "ENVFILE=.env.uat react-native run-ios --variant=uatdebug", "ios:prod": "ENVFILE=.env.production react-native run-ios --variant=productiondebug", "start": "react-native start --reset-cache", "ios": "react-native run-ios", "android": "react-native run-android --active-arch-only", "compile": "tsc --noEmit -p . --pretty", "format": "prettier --write \"app/**/*.{js,jsx,json,md,ts,tsx}\"", "lint": "eslint index.js App.js app test --fix --ext .js,.ts,.tsx && npm run format", "patch": "patch-package", "test": "jest", "test:watch": "jest --watch", "test:maestro": "maestro test .maestro/FavoritePodcast.yaml", "adb": "adb reverse tcp:9090 tcp:9090 && adb reverse tcp:3000 tcp:3000 && adb reverse tcp:9001 tcp:9001 && adb reverse tcp:8081 tcp:8081", "postinstall": "node ./bin/postInstall && chmod +x android/gradlew", "bundle:ios": "react-native bundle --entry-file index.js --platform ios --dev false --bundle-output ios/main.jsbundle --assets-dest ios", "bundle:android": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "release:ios": "echo 'Not implemented yet: release:ios. Use Xcode. More info: https://reactnative.dev/docs/next/publishing-to-app-store'", "release:android": "cd android && rm -rf app/src/main/res/drawable-* && ./gradlew assembleRelease && cd - && echo 'APK generated in ./android/app/build/outputs/apk/release/app-release.apk'", "clean": "npx react-native-clean-project", "clean-all": "npx react-native clean-project-auto", "expo:start": "expo start", "expo:android": "expo start --android", "expo:ios": "expo start --ios", "expo:web": "expo start --web"}, "overrides": {"react-error-overlay": "6.0.9"}, "dependencies": {"@expo-google-fonts/noto-sans": "^0.2.3", "@expo/metro-config": "^0.7.1", "@expo/webpack-config": "^18.0.1", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-native-fontawesome": "^0.3.2", "@kolking/react-native-rating": "^1.3.0", "@react-native-async-storage/async-storage": "1.17.11", "@react-native-community/checkbox": "^0.5.17", "@react-native-community/netinfo": "^11.3.2", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/material-top-tabs": "^6.6.3", "@react-navigation/native": "~6.0.1", "@react-navigation/native-stack": "^6.0.2", "@react-navigation/stack": "~6.0.1", "abortcontroller-polyfill": "^1.7.8", "add": "^2.0.6", "aliyun-react-native-push": "^0.9.0", "apisauce": "2.1.5", "axios": "^1.6.8", "babel-plugin-inline-import": "^3.0.0", "date-fns": "^2.29.2", "expo": "^48.0.15", "expo-application": "~5.1.1", "expo-constants": "~14.2.1", "expo-device": "~5.2.1", "expo-file-system": "~15.2.1", "expo-font": "~11.1.1", "expo-linking": "~4.0.1", "expo-localization": "~14.1.1", "expo-modules-core": "~1.2.3", "expo-splash-screen": "~0.18.1", "expo-status-bar": "~1.4.4", "i18n-js": "3.9.2", "jetifier": "^2.0.0", "mobx": "6.6.0", "mobx-react-lite": "3.4.0", "mobx-state-tree": "5.1.5", "moment": "^2.30.1", "react": "18.2.0", "react-native": "0.71.13", "react-native-asset": "^2.1.1", "react-native-autoheight-webview": "^1.6.5", "react-native-bootsplash": "4.5.0", "react-native-calendars": "^1.1304.1", "react-native-device-info": "^11.1.0", "react-native-element-dropdown": "^2.10.4", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "2.9.0", "react-native-image-picker": "7.1.2", "react-native-keep-awake": "^4.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-pager-view": "6.4.1", "react-native-reanimated": "~2.14.4", "react-native-reanimated-carousel": "^3.5.1", "react-native-rename": "^3.2.16", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "4.5.0", "react-native-screens": "3.27.0", "react-native-simple-dialogs": "^2.0.3", "react-native-svg": "15.3.0", "react-native-tab-view": "^3.5.2", "react-native-toast-message": "^2.2.0", "react-native-video": "^5.2.1", "react-native-webview": "13.6.4", "reactotron-mst": "3.1.4", "reactotron-react-js": "^3.3.7", "reactotron-react-native": "5.0.3", "reconnecting-websocket": "^4.4.0", "rn-dynamic-app-icon": "^0.6.1", "yarn": "^1.22.22"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/cli-platform-ios": "^8.0.2", "@rnx-kit/metro-config": "^1.3.5", "@rnx-kit/metro-resolver-symlinks": "^0.1.26", "@types/i18n-js": "3.8.2", "@types/jest": "^29.2.1", "@types/react": "~18.0.27", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "babel-jest": "^29.2.1", "babel-loader": "8.2.5", "babel-plugin-root-import": "^6.6.0", "eslint": "8.17.0", "eslint-config-prettier": "8.5.0", "eslint-config-standard": "17.0.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-n": "^15.0.0", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "6.0.0", "eslint-plugin-react": "7.30.0", "eslint-plugin-react-native": "4.0.0", "expo-modules-autolinking": "~1.1.0 || ~1.2.0", "fbjs-scripts": "3.0.1", "jest": "^29.2.1", "jest-circus": "29", "jest-environment-node": "29", "jest-expo": "^48.0.0", "metro-config": "0.75.1", "metro-react-native-babel-preset": "0.75.1", "metro-source-map": "0.75.1", "mocha": "6", "patch-package": "6.4.7", "postinstall-prepare": "1.0.1", "prettier": "2.8.8", "query-string": "^7.0.1", "react-devtools-core": "4.24.7", "react-dom": "18.2.0", "react-native-web": "~0.18.7", "react-test-renderer": "18.2.0", "reactotron-core-client": "^2.8.10", "regenerator-runtime": "^0.13.4", "ts-jest": "29", "typescript": "^5.0.4"}, "resolutions": {"@types/react": "18.2.0", "@types/react-dom": "^18", "@types/react-native": "~0.69.1"}, "engines": {"node": ">=18"}, "prettier": {"printWidth": 100, "semi": false, "singleQuote": false, "trailingComma": "all"}, "eslintConfig": {"root": true, "parser": "@typescript-eslint/parser", "extends": ["plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-native/all", "standard", "prettier"], "plugins": ["@typescript-eslint", "react", "react-native"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "project": "./tsconfig.json"}, "settings": {"react": {"pragma": "React", "version": "detect"}}, "globals": {"__DEV__": false, "jasmine": false, "beforeAll": false, "afterAll": false, "beforeEach": false, "afterEach": false, "test": false, "expect": false, "describe": false, "jest": false, "it": false}, "rules": {"@typescript-eslint/ban-ts-ignore": 0, "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/explicit-function-return-type": 0, "@typescript-eslint/explicit-member-accessibility": 0, "@typescript-eslint/explicit-module-boundary-types": 0, "@typescript-eslint/indent": 0, "@typescript-eslint/member-delimiter-style": 0, "@typescript-eslint/no-empty-interface": 0, "@typescript-eslint/no-explicit-any": 0, "@typescript-eslint/no-object-literal-type-assertion": 0, "@typescript-eslint/no-var-requires": 0, "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "comma-dangle": 0, "multiline-ternary": 0, "no-undef": 0, "no-unused-vars": 0, "no-use-before-define": 0, "no-global-assign": 0, "quotes": 0, "react-native/no-raw-text": 0, "react/no-unescaped-entities": 0, "react/prop-types": 0, "space-before-function-paren": 0}}}