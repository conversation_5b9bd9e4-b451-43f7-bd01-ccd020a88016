{"name": "React-Codegen", "version": "0.71.13", "summary": "Temp pod for generated files for React Native", "homepage": "https://facebook.com/", "license": "Unlicense", "authors": "Facebook", "compiler_flags": "-DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -Wno-comma -Wno-shorten-64-to-32 -Wno-documentation -Wno-nullability-completeness -std=c++17", "source": {"git": ""}, "header_mappings_dir": "./", "platforms": {"ios": "12.4"}, "source_files": "**/*.{h,mm,cpp}", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/RCT-Folly\" \"${PODS_ROOT}/Headers/Public/React-Codegen/react/renderer/components\" \"$(PODS_ROOT)/Headers/Private/React-Fabric\" \"$(PODS_ROOT)/Headers/Private/React-RCTFabric\""}, "dependencies": {"FBReactNativeSpec": [], "React-jsiexecutor": [], "RCT-Folly": [], "RCTRequired": [], "RCTTypeSafety": [], "React-Core": [], "React-jsi": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "hermes-engine": []}}