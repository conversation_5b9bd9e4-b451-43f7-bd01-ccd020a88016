import moment from "moment"
import { load, remove, save } from "./storage"
import { Api } from "app/api/api"


interface IScreenSetTime {
  screenName: string
  setAt: string
}

interface IRefreshStorage {
  screenSetTimes: [IScreenSetTime]
}

class RefreshStorageImpl {  
  refreshStorage: IRefreshStorage = {
    screenSetTimes: [],
  }

  // constructor() {
  //   this.refreshStorage = {
  //     screenSetTimes: [],    
  //   }
  // }

  shouldRefreshScreen (mScreenName: string) {
    let result = false
    this.refreshStorage.screenSetTimes.forEach((item: any) => {
      if (item.screenName == mScreenName) {
        const now = moment()
        const setAt = moment(item.setAt)
        const diff = now.diff(setAt, "seconds")
        if (diff > Api.reloadTime) {
          result = true
        }
      }
    })
    return result
  }

  getDiffSeconds (mScreenName: string) {
    let result = 0
    this.refreshStorage.screenSetTimes.forEach((item: any) => {
      if (item.screenName == mScreenName) {
        const now = moment()
        const setAt = moment(item.setAt)
        result = now.diff(setAt, "seconds")
      }
    })
    return result
  }

  async updateScreenSetTime (mScreenName: string) {
    if (this.screenNameAdded(mScreenName)) {
      this.refreshStorage.screenSetTimes.forEach((item: any) => {
        if (item.screenName == mScreenName) {
          item.setAt = moment().format("YYYY-MM-DD HH:mm:ss")
        }
      })
    } else {
      this.refreshStorage.screenSetTimes.push({
        screenName: mScreenName,
        setAt: moment().format("YYYY-MM-DD HH:mm:ss")
      })
    }
    await this.saveToStorage()
  }

  screenNameAdded (mScreenName: string) {
    let result = false
    this.refreshStorage.screenSetTimes.forEach((item: any) => {
      if (item.screenName == mScreenName) {
        result = true
      }
    })
    return result
  }
  
  async initializeFromAsyncStorage() {
    const result = await load("refreshStorage")
    if (result) {      
      this.refreshStorage = result as IRefreshStorage      
    }
  }

  async saveToStorage () {
    await save("refreshStorage", this.refreshStorage)
    console.log("save refreshStorage successfully")
  }

  async saveToStorageWithCallback (callback: any) {
    save("refreshStorage", this.refreshStorage).then(() => {
      console.log("save refreshStorage successfully")
      callback()
    })
  }

  reset(callback: any = null) {    
    remove("refreshStorage").then(() => {
      this.refreshStorage = {
        screenSetTimes: []
      }
      if (callback) {
        callback()
      }
    })
  }
}

const RefreshStorage = new RefreshStorageImpl()
RefreshStorage.initializeFromAsyncStorage()
export { RefreshStorage }
