import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef } from "react"
import { Screen } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { ViewStyle } from "react-native"
import { Dimen } from "../../theme/dimen"
import WebView from "react-native-webview"
import { MIcon } from "../../components/MIcon"
import { Helper } from "app/utils/helper"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"
import { AppStorage } from '../../utils/appStorage'

interface WebviewScreenProps extends AppStackScreenProps<"Webview"> {}


const WebviewStyle: ViewStyle = {
  width: Dimen.screenWidth,
  height: Dimen.screenHeight*0.8,
  marginTop: 20
}


export const WebviewScreen: FC<WebviewScreenProps> = observer(function WebviewScreen(_props) {
  
  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  const webviewRef = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        if (webviewRef.current) {
          webviewRef.current.reload()
        }
      }
    }
  }, [netInfo.isConnected])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ flex: 1 }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }}  />     
      <WebView
        ref={webviewRef}
        style={WebviewStyle}
        source={{ uri: AppStorage.getGraphURL() }}
      />      
    </Screen>
  )
})
