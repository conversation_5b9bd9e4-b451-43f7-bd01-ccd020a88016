import { Helper } from "app/utils/helper"
import { Text } from "../../components"
import { translate } from "../../i18n"
import { BTN_IN_CARD, BTN_IN_CARD_GREY, HIDE_PRELOAD_CONTAINER, HIDE_PRELOAD_HAS_M_TOP_CONTAINER, TEXT_NEWS_TITLE, TEXT_SMALL } from "../../theme/baseStyle"
import { Dimen } from "../../theme/dimen"
import { AI_CENTER, COLUMN, FLEX, MARGIN_S_DF_LEFT, ROW, SHADOW, MARGIN_S_DF_TOP, W_100P, MARGIN_S_DF_BOTTOM } from "../../theme/mStyle"
import * as React from "react"
import { Image, ImageStyle, TouchableOpacity, View, ViewStyle, StyleSheet } from "react-native"
import WebView from "react-native-webview"
import Video from 'react-native-video';
import LinearGradient from 'react-native-linear-gradient';
import { AppStorage } from "app/utils/appStorage"
import { MIcon } from "app/components/MIcon"
import { useState } from "react"
import { Api } from 'app/api/api'
import { useSafeAreaInsets } from "react-native-safe-area-context"

// Navigation component heights
const BackNavComponentHeight = Dimen.padding.base * 2 + Dimen.iconSize.base + Dimen.padding.ssm * 2 // 60px
const BottomMenuHeight = Dimen.padding.sm * 2 + Dimen.iconSize.md + 16 + 25 // 85px base height

const CardNewsContainer: ViewStyle = {
    ...SHADOW,
    ...COLUMN,
    width: Dimen.screenWidth - 2 * Dimen.padding.base,
    minHeight: Dimen.screenWidth * 0.3,
    backgroundColor: 'white',
    borderRadius: Dimen.borderRadiusLarge,
    overflow: 'hidden',
    marginHorizontal: Dimen.padding.base,
    marginVertical: Dimen.padding.ssm,
}

const NewsImageContainer: ViewStyle = {
    width: Dimen.screenWidth * 0.3,
    height: Dimen.screenWidth * 0.26,
    borderRadius: Dimen.borderRadiusLarge,
    overflow: 'hidden',
    marginTop: Dimen.padding.sm,
}

const NewsImage: ImageStyle = {
    width: "100%",
    height: "100%",
    borderRadius: Dimen.borderRadiusLarge,
    overflow: 'hidden'
}

const ShowMoreContainer: ViewStyle = {
    alignItems: 'center',
    justifyContent: 'center',
    height: 30,
    width: 60,
    borderTopStartRadius: Dimen.borderRadiusLarge,
    borderTopEndRadius: Dimen.borderRadiusLarge,
    backgroundColor: "#bbbbbb"
}

const styles = StyleSheet.create({
    video: {
        aspectRatio: 1280 / 720,
        width: '100%',
    },
    videoContainer: {
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center',
        marginTop: Dimen.padding.ssm,
    },
});

export function CardNews(props: any) {
    const {
        containerStyle = {},
        showDetails = false,
        data = {
            id: 1,
            type: 1
        },
        onPress = () => {
            console.log("CardNews pressed")
        },
        // showFullContent = false,
        onFullContentUpdate = (value: boolean) => {
            console.log("onFullContentUpdate", value)
        }
    } = props
    const insets = useSafeAreaInsets()
    const [contentViewHeight, setcontentViewHeight] = useState(0)
    const [cardHeight, setCardHeight] = useState(0)
    const [displayShowMoreButton, setDisplayShowMoreButton] = useState(false)
    const [showDetailsContent, setShowDetailsContent] = useState(!showDetails)
    const limitHeight = Dimen.screenHeight - BackNavComponentHeight - BottomMenuHeight - insets.bottom - 30
    const [showFullContent, setShowFullContent] = useState(false)

    const getImageUrl = () => {
        if (data.medium != null && data.medium.s3ObjKey != null && data.medium.s3ObjKey.Location != null) {
            return data.medium.s3ObjKey.Location
        } else {
            return ""
        }
    }

    const getNewsDetailsUrl = (postItem) => {
        const slugType = Helper.getValue(postItem, "postTypeSlug")
        const slug = Helper.getValue(postItem, "slug")
        const url = Api.getWebPortalUrl() + "/" + AppStorage.appStorage.setting.language.toLocaleLowerCase() + slugType + slug + "?domain=" + Api.getUrl() + "/files/"
        console.log("getNewsDetailsUrl", url)
        return url.replace(" ", "")
    }

    const getVideoUrl = () => {
        return Helper.getValue(data, "video")
    }

    const getButtonTypeName = () => {
        if (data.postTypeId == 5) {
            return translate("homePage.todayTopics")
        } else if (data.postTypeId == 6) {
            return translate("homePage.latestNews")
        } else if (data.postTypeId == 4) {
            return translate("homePage.latestNotification")
        } else {
            return translate("homePage.latestNews")
        }
    }

    const getButtonTagName = () => {
        return Helper.getValue(data, "category")
    }

    const getCardStyleForHeightAdjust = () => {
        if (showDetails) {
            console.log("getCardStyleForHeightAdjust", contentViewHeight)
            if (contentViewHeight == 0) {
                return { height: Dimen.screenHeight * 0.4 }
            } else {
                return { height: contentViewHeight + 150 }
            }
        } else {
            return {}
        }
    }

    const getCardRootStyle = () => {
        if (showDetails) {
            if (showFullContent) {
                return {}
            } else {
                return { maxHeight: limitHeight }
            }
        } else {
            return {}
        }
    }

    React.useEffect(() => {
        if (cardHeight > limitHeight) {
            setDisplayShowMoreButton(true)
        }
    }, [contentViewHeight, cardHeight])

    React.useEffect(() => {
        if (showDetails && data.videoOrHtml != 'video') {
            AppStorage.showLoading()
            setShowDetailsContent(false)
        } else {
            AppStorage.hideLoading()
            setShowDetailsContent(true)
        }
    }, [])

    //     const injectedJavaScript = `
    //     (function() {
    //       const meta = document.createElement('meta');
    //       meta.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=2.0');
    //       meta.setAttribute('name', 'viewport');
    //       document.getElementsByTagName('head')[0].appendChild(meta);      
    //       setTimeout(() => {        
    //         const height = document.body.scrollHeight;
    //         window.ReactNativeWebView.postMessage(height + "");
    //       }, 500);
    //     })();
    //     true;
    //   `;

    const injectedJavaScriptNotScroll = `
    document.body.style.overflow = 'hidden';
    (function() {
      const meta = document.createElement('meta');
      meta.setAttribute('content', 'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=2.0');
      meta.setAttribute('name', 'viewport');
      document.getElementsByTagName('head')[0].appendChild(meta);      
      setTimeout(() => {        
        const height = document.body.scrollHeight;
        window.ReactNativeWebView.postMessage(height + "");
      }, 100);
    })();
    true;
  `;

    return (
        <TouchableOpacity
            onPress={onPress}
            disabled={showDetails}
            style={[CardNewsContainer, containerStyle, getCardRootStyle(), { opacity: showDetailsContent ? 1 : 0, marginBottom: showFullContent ? Dimen.padding.xxl : Dimen.padding.sm }]}>
            <View style={[COLUMN, W_100P, getCardStyleForHeightAdjust(), { padding: Dimen.padding.sm }]}>
                <View style={[ROW, AI_CENTER]}>
                    <Text style={BTN_IN_CARD} text={getButtonTypeName()} />
                    {
                        getButtonTagName() != "" &&
                        <Text style={[BTN_IN_CARD_GREY, MARGIN_S_DF_LEFT]} text={getButtonTagName()} />
                    }
                    <View style={FLEX} />
                    <Text style={TEXT_SMALL} text={Helper.formatDate(data.createdAt ? data.createdAt : data.updatedAt)} />
                </View>
                {
                    (!showDetails ||
                        (showDetails && data.videoOrHtml != 'video')) &&
                    <View style={[ROW, AI_CENTER, MARGIN_S_DF_TOP, MARGIN_S_DF_BOTTOM]}>
                        {
                            getImageUrl() != "" &&
                            <View style={NewsImageContainer}>
                                <Image resizeMode="cover" style={NewsImage} source={{ uri: getImageUrl() }} />
                            </View>
                        }
                        <View style={[COLUMN, FLEX, { marginStart: getImageUrl() != "" ? Dimen.padding.sm : 0 }]}>
                            <Text numberOfLines={showDetails ? undefined : 1} style={TEXT_NEWS_TITLE} text={Helper.getValue(data, "title")} />
                            {/* {
                                Helper.getValue(data, "subTitle") != "" &&
                                <Text numberOfLines={showDetails ? undefined : 1} style={TEXT_SMALL} text={Helper.getValue(data, "subTitle")} />
                            } */}
                        </View>
                    </View>
                }
                {
                    showDetails && data.videoOrHtml == 'video' &&
                    <View style={[MARGIN_S_DF_TOP, FLEX]}>
                        <Text numberOfLines={showDetails ? undefined : 1} style={TEXT_NEWS_TITLE} text={Helper.getValue(data, "title")} />
                        {/* {
                            Helper.getValue(data, "subTitle") != "" &&
                            <Text numberOfLines={showDetails ? undefined : 1} style={TEXT_SMALL} text={Helper.getValue(data, "subTitle")} />
                        } */}
                    </View>
                }
                {
                    showDetails && getVideoUrl() != "" && data.videoOrHtml == 'video' &&
                    <View style={[FLEX, styles.videoContainer]}>
                        <Video
                            // Can be a URL or a local file.
                            source={{ uri: getVideoUrl() }}
                            fullscreen={true}
                            fullscreenOrientation="landscape"
                            resizeMode="contain"
                            style={styles.video}
                        />
                    </View>
                }
                {
                    showDetails && data.videoOrHtml != 'video' &&
                    // <Text style={[W_100P, TEXT_SMALL]} text={Helper.getValue(data, "content")} />
                    <WebView
                        originWhitelist={['*']}
                        scalesPageToFit={true}
                        allowsJavaScript={true}
                        javaScriptEnabled={true}
                        showsVerticalScrollIndicator={false}
                        showsHorizontalScrollIndicator={false}
                        onMessage={(e) => {
                            console.log("onMessage height", e.nativeEvent.data)
                            if (parseInt(e.nativeEvent.data + "") && parseInt(e.nativeEvent.data + "") > 0) {
                                setcontentViewHeight(parseInt(e.nativeEvent.data + ""))
                                setCardHeight(parseInt(e.nativeEvent.data + "") + 150)
                            }
                            setShowDetailsContent(true)
                            AppStorage.hideLoading()
                        }}
                        onError={(e) => {
                            console.log("onError", e)
                            setShowDetailsContent(true)
                            AppStorage.hideLoading()
                        }}
                        injectedJavaScript={injectedJavaScriptNotScroll}
                        source={{ uri: getNewsDetailsUrl(data) }}
                    />
                }
            </View>
            {
                showDetails && displayShowMoreButton && !showFullContent && <LinearGradient
                    colors={['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 1)']}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 0, y: 1 }}
                    style={{ height: 60, width: "100%", alignItems: "center", justifyContent: "center", position: "absolute", bottom: -20 }}
                >
                    <TouchableOpacity
                        style={ShowMoreContainer}
                        onPress={() => {
                            setShowFullContent(!showFullContent)
                            // onFullContentUpdate(!showFullContent)
                        }}>
                        <MIcon name="downWhite" size={25} />
                    </TouchableOpacity>
                </LinearGradient>
            }
        </TouchableOpacity>
    )
}