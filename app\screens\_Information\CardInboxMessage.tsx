import { Helper } from "app/utils/helper"
import { Text } from "../../components"
import { translate } from "../../i18n"
import { BTN_IN_CARD, TEXT_B2, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { Dimen } from "../../theme/dimen"
import { AI_CENTER, COLUMN, FLEX, MARGIN_DF_RIGHT, MARGIN_S_DF_TOP, ROW } from "../../theme/mStyle"
import * as React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { colors } from "app/theme"
import { updateInboxRead } from "app/api/model"
import { useNavigation } from "@react-navigation/native"
import { AppLinkStorage } from "app/utils/appLinkStorage"
import { useEffect } from "react"
import { AppStorage } from "app/utils/appStorage"


const CardNewsContainer: ViewStyle = {
    ...COLUMN,
    width: Dimen.screenWidth,
    // minHeight: Dimen.screenWidth * 0.3,
    // marginTop: Dimen.padding.base,
    paddingHorizontal: Dimen.padding.base,
    paddingTop: Dimen.padding.base,
}

const BottomLine: ViewStyle = {
    width: Dimen.screenWidth - Dimen.padding.base * 2,
    height: 1,
    backgroundColor: colors.mine.border,
    marginTop: Dimen.padding.base
}

export function CardInboxMessage (props: any) {
    const {
        containerStyle = {},    
        data = {
            id: 1,
            type: 1
        },
        onPress = () => {
            console.log("CardNews pressed")
        }
    } = props
    const navigation = useNavigation()

    const getButtonTypeName = () => {
        if (data.pushType == 1) {
            return translate("homePage.todayTopics")
        } else if (data.pushType == 2) {
            return translate("information.eventNotificaiont")
        } else if (data.pushType == 3) {
            return translate("information.transactionNotice")
        } else if (data.pushType == 4) {
            return translate("information.systemNotification")
        } else {
            return translate("information.title")
        }
    }

    const updateReadInformation = () => {
        updateInboxRead(
            data.id,
            (data) => {
                console.log("updateReadInformation data: ", data)
            },
            (error) => {
                console.log("updateReadInformation error: ", error)
            },
            () => {
                navigation.navigate("Logout")
            }
        )
    }

    const btnClicked = () => {
        if (!AppStorage.isNetworkConnected()) {
            return
        }
        updateReadInformation()
        if (onPress) {
            onPress(data)
        } else {
            if (data.postId) {                
                navigation.navigate("NewsDetails", {postId: data.postId})
            } else if (data.deepLink) {
                console.log("data.deepLink: ", data.deepLink)
                AppLinkStorage.handleDeepLinkInsideApp(data.deepLink, navigation)
                // navigation.navigate("NewsDetail", {postId: data.deepLink})
            }
        }

    }

    useEffect(() => {
        console.log("CardInboxMessage useEffect " + data.isRead)
    }, [])


    return (
        <TouchableOpacity 
            onPress={btnClicked}
            style={[CardNewsContainer, containerStyle, {backgroundColor: data.isRead == "N" ? "#fffbf0" : "#ffffff"}]}>
            <View style={ROW}>
                <Text numberOfLines={2} style={[TEXT_B2, FLEX, MARGIN_DF_RIGHT]} text={Helper.removeHtmlTag(data.pushMessage)} />                
                <Text onPress={() => btnClicked()} style={[BTN_IN_CARD, {height: 30}]} text={getButtonTypeName()} />
            </View>
            <View style={[ROW, AI_CENTER, MARGIN_S_DF_TOP]}>
                <Text style={TEXT_SMALL_0} text={Helper.formatDate(data.createdAt, "YYYY-MM-DD   HH:mm")} />
            </View>
            <View style={BottomLine} />
        </TouchableOpacity>
    )
}