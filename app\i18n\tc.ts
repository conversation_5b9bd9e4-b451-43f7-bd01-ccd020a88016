const tc = {
  common: {
    ok: "確定",
    cancel: "取消",
    confirm: "確認",
    back: "退出",
    logOut: "登出",
    jan: "一月",
    feb: "二月",
    mar: "三月",
    apr: "四月",
    may: "五月",
    jun: "六月",
    jul: "七月",
    aug: "八月",
    sep: "九月",
    oct: "十月",
    nov: "十一月",
    dec: "十二月",
    resend: "重發",
    submit: "提交",
    yes: "是",
    no: "否",
    year: "年",
    month: "月",
    day: "日",
    delete: "刪除",
    sun: "日",
    mon: "一",
    tue: "二",
    wed: "三",
    thu: "四",
    fri: "五",
    sat: "六",
    upload: "上載",
    return: "返回",
    male: "男",
    female: "女",
    china: "中國",
    hongkongAndOthers: "香港及其他",
    hkchina: "中國香港",
    macauchina: "中國澳門",
    twchina: "中國台灣",
    malaysia: "馬來西亞",
    vietnam: "越南",
    success: "成功",
    today: "今天",
    janShort: "一月",
    febShort: "二月",
    marShort: "三月",
    aprShort: "四月",
    mayShort: "五月",
    junShort: "六月",
    julShort: "七月",
    augShort: "八月",
    sepShort: "九月",
    octShort: "十月",
    novShort: "十一月",
    decShort: "十二月"
  },
  errors: {
    invalidEmail: "電子郵件地址無效。",
    invalidReminderId: "到價提示ID無效。",
    standard: "請稍後再試。",
    enterPhone: "請輸入您的電話號碼。",
    enterEmail: "請輸入您的電子郵件地址。",
    enterName: "請輸入您的姓名。",
    phoneExits: "電話號碼已存在。",
    otpWrong: "你輸的驗證碼錯誤,請重新輸入。",
    phoneVerificationIncorrect: "手機驗證碼不正確",
    unAuthorize: "未授權，需要重新登入。",
    addPriceReminder: "請新增到價提示。",
    noRelatedData: "無相關數據",
    inputAllRequiredValues: "請輸入所有必填欄位。",
    watchInvestRelatedVideo: "請觀看投資相關影片。",
    selectCountry: "請選擇一個國家或地區。",
    phoneFormatWrong: "手機號碼無效，請更正您的電話號碼。",
    demoRgCode10017: "手機驗證碼不正確",
    demoRgCode10018: "電郵驗證碼不正確",
    demoRgCode200: "你已成功登記模擬賬戶",
    realRgCode10029: "手機驗證碼不正確",
    realRgCode10033: "電郵驗證碼不正確",
    realRgCode10032: "手機號碼已被註冊",
    realRgCode10031: "郵箱已被註冊",
    realRgCode200: "你已成功登記真實賬戶",
    networkDelay: "操作逾時，請再試一次",
    networkTimeout: "操作逾時，請再試一次",
    canTypeSymbolForPhone: "電話號碼不能輸入符號",
    noInternet: "網路不好，請稍後重試",
    accountDisabled: "用戶無效",
    wrongToken: "驗證碼錯誤，請重新登入"
  },
  menu: {
    home: "首頁",
    trend: "動向",
    trade: "行情",
    information: "信息",
    account: "我的",
  },
  homePage: {
    updateApp: "請更新至最新版本",
    createAccount: "立即開戶",
    trend: "動向",
    latestNotification: "最新公告",
    newsNotification: "消息公告",
    todayTopics: "金價動態",
    morningNews: "早訊",
    latestNews: "最新活動",
    financialNews: "財經快訊",
    financialCalendar: "財經日曆",
    information: "個資訊",
    applyDemoAccount: "申請模擬交易賬號",
    applyLiveAccount: "申請真實交易賬號",
    investNow: "立即注資",
    contactCustomerService: "聯繫客服",
  },
  trendPage: {
    // todayTopics: "今日話題",
    // latestNews: "最新活動",
    // latestNotification: "最新公告",
    eveningNews: "晚訊",
    news: "快訊",
    calendar: "日曆",
    readMore: "了解更多",
    readLess: "收起",
    events: "事件",
    financialEvents: "財經事件",
    holidayClosure: "假期休市",
    formerValue: "前值",
    predictiveValue: "預測值",
    publishedValue: "公布值",
    affect: "影響",
    affectL: "利多 金銀",
    affectD: "利空 金銀",
    affectLApi: "利多",
    affectDApi: "利空",
  },
  searchPage: {
    todayNewsTitle: "搜索金價動態",
    latestNewsTitle: "搜索最新活動",
    noSearchFound: "沒有找到任何搜索結果。",
    searchPlaceholder: "輸入搜索關鍵字",
  },
  tradePage: {
    priceReminder: "到價提示",
    addPriceReminder: "新增到價提示",
    becomeMember: "成為會員，使用到價提示",
    createRealAccount: "建立真實賬號",
    createDemoAccount: "建立模擬賬號",
    priceReminderInfo: "到價提示資訊",
    priceReminderInfoContent: "1. 新用戶有15天免費使用期。\n2. 成功申請模擬交易賬號，有15天免費使用期。\n3. 成功申請真實交易賬號，可永久使用。\n4. 同時可以建立12個活躍的提示。\n5.在免費使用期內建立的提示,在過期後仍然生效運作。\n6.在免費使用期過期後，不能再建立新提示。",
    priceReminderInfoContent1: "新用戶有15天免費使用期。",
    priceReminderInfoContent2: "成功申請模擬交易賬號，有15天免費使用期。",
    priceReminderInfoContent3: "成功申請真實交易賬號，可永久使用。",
    priceReminderInfoContent4: "同時可以建立12個活躍的提示。",
    priceReminderInfoContent5: "在免費使用期內建立的提示,在過期後仍然生效運作。",
    priceReminderInfoContent6: "在免費使用期過期後，不能再建立新提示。",
    priceReminderCreated: "已建立到價提示。",
    priceReminderExpired: "到價提示的試用期已經過期。",
    priceReminderLoginNeeded: "請在登錄後使用這個功能。",
    timeDemoExpire: "試用期至",
    upgradeToRealAccount: "升級真實賬號",
    goToMT4: "前往 MT4",
    promptRecord: "提示記錄",
    priceReach: "到價",
    priceUpDown: "漲跌",
    riseTo: "升至",
    riseToInNew: "漲至",
    dropTo: "降至",
    validUntil: "有效期至",
    cancelled: "已取消",
    inactive: "已提示",
    expired: "已過期",
    showPromtRecordTo: "顯示提示記錄至",
  },
  priceModal: {
    londonGold: "倫敦金",
    londonSilver: "倫敦銀",
    dollarIndex: "美元指数",
    priceReach: "到價",
    priceUpDown: "漲跌",
    riseTo: "升至",
    riseToInNew: "漲至",
    dropTo: "降至",
    notificationDate: "通知日期",
    askNotification: "是否接收推送通知？",
    enableNotification: "接受",
    disableNotification: "拒絕",
  },
  boarding: {
    setting: "設定",
    login: "登入",
    register: "註冊",
    screenAlwaysOn: "屏幕常亮",
    on: "常亮",
    on2: "開啟",
    off: "關閉",
    displaySetting: "顯示設置",
    language: "語言",
    clearCache: "清除緩存",
    displayModeRed: "紅升綠降",
    displayModeGreen: "綠升紅降",
    traditionChinese: "繁體中文",
    simplifiedChinese: "簡體中文",
    clear: "清除",
    aboutHan: "關於漢聲",
    companyProfile: "公司介紹",
    majorEventOfHan: "漢聲大事記",
    hansJournal: "漢聲期刊",
    contactCustomerService: "聯絡客服",
    selectCountry: "選擇國家/地區",
    enterVerifyCode: "輸入認證碼",
    sentTo: "已經發送到",    
    newCodeSent: " 新的驗證碼已經送出。",
    inputYourName: "填寫你的名字",
    submitRegister: "提交",
    registerSuccess: "你已成功登記。",
    returnHome: "返回首頁"
  },
  account: {
    acountInfo: "賬號資訊",
    personalInfo: "個人資訊",
    emailSubscription: "短信訂閱",
    deleteAccount: "删除賬號",
    userTypeDemo: "模擬賬號",
    userTypeLive: "真實賬號",
    applyForDemoAccount: "申請模擬賬號",
    applyRealTradingAccount: "升級真實交易賬號",
    enabled: "有效",
    disabled: "無效",
    status: "狀態",
    clubPoint: "漢聲薈積分",
    accountBalance: "真實賬號資金",
    eject: "注資",
    withdraw: "取款",
    bankInfo: "銀行資訊",
    agentCenter: "代理中心",
    fundAcount: "注資真實賬戶",
    transactionHistory: "交易記錄",
    todayExchangeRate: "今日滙率",
    live: "真實",
    currentAccountCurrency: "US$",
    tradingAccountNumber: "交易賬號",
    name: "姓名",
    email: "電郵",
    gender: "性別",
    male: "男",
    female: "女",
    bankCardNo: "銀行卡號碼",
    bankBranch: "開戶支行",
    bankName: "銀行名稱",
    bankBranchName: "開戶支行",
    accountHolderName: "開戶人名稱",
    bankCardNumber: "銀行卡號碼",
    balance: "余額",
    netWorth: "淨值",
    availableMargin: "可用保證金",
    fundDetails: "資金明細",
    transAmount: "申請金額",
    handleFee: "手續費",
    currency: "貨幣",
    exchangeRate: "匯率",
    finish: "完成",
    processing: "處理中",
    canceled: "取消",
    today: "當天",
    lastWeek: "最近1周",
    lastMonth: "最近1個月",
    last3Month: "最近3個月",
    exchangeRateDeposite: "存",
    exchangeRatePick: "取",
    transactionDeposite: "入金申請",
    transactionWithdraw: "出金申請",
    transactionProcessing: "處理中",
    transactionFinish: "完成",
    transactionCancel: "取消",
    subscriptionConfirm: "要開始訂閱嗎?",
    unsubscribeConfirm: "要取消訂閱嗎?",
    displayRedConfirm: "要設定為紅升綠降嗎?",
    displayGreenConfirm: "要設定為綠升紅降嗎?",
    langTraditionChinese: "繁體中文",
    langSimplifiedChinese: "简体中文",
    langEnglish: "English",
    clearCacheConfirm: "確認要清除緩存嗎?",
    deleteAccountConfirm: "確定要删除賬號?",
    logoutSuccess: "登出成功",
    confirmLogout: "確認登出?",
    noRecord: "沒有記錄",
    ib_balance: "賬戶餘額",
  },
  deleteAccount: {
    title: "删除賬號",
    content: "親愛的用戶,\n\n\n在您決定刪除您的手機應用程式賬號之前,我們有以下重要提示:\n\n\n資料移除:\n\n一旦您刪除賬號,我們將不會保留您在應用程式上輸入的任何個人資料和喜好設定。這些資訊將被完全刪除,無法恢復。\n\n\n設定丟失:\n\n您在應用程式上的所有個人設定和偏好,例如語言、到價提示等,都將隨著賬號刪除而一併被移除。\n\n\n服務中斷:\n\n刪除賬號後,您將無法再使用該應用程式提供的任何服務。如果您未來仍需使用該服務,請慎重考慮是否要刪除賬號。\n\n\n客服支援:\n\n如果您在刪除賬號後仍有任何問題或疑問,歡迎隨時聯繫我們的客戶服務部門。我們將竭盡全力提供協助。\n\n\n再次提醒您在刪除賬號前,請仔細考慮並做好充分準備。如果您有任何疑問,歡迎隨時與我們聯繫。祝您使用愉快!",
    confirmDelete: "確認要删除賬號"
  },
  accountLogout: {
    title: "賬號已被登出",
    message: "你的賬號已在其他裝置上登入了。"
  },
  demoAccountSignUp: {
    title: "申請模擬賬號",
    name: "真實姓名*",
    namePlaceholder: "輸入真實姓名",
    phoneNo: "電話號碼*",
    phoneNoPlaceholder: "輸入電話號碼",
    verifyCodePlaceholder: "填寫認證碼",
    email: "電子郵箱*",
    emailPlaceholder: "輸入電子郵箱",    
    getVerifyCode: "獲取驗證碼",
    resendVerifyCode: "重發驗證碼",
    verifycodeSent: "驗證碼已經送出。",
  },
  liveAccountSignUp: {
    title: "申請真實賬號",
    agentCode: "代理人及推薦碼",
    auth: "身份認證",
    name: "真實姓名*",
    namePlaceholder: "輸入真實姓名",
    gender: "性別*",
    genderPlaceholder: "選擇性別",
    idDocumentType: "證明文件*",
    idDocumentTypePlaceholder: "選擇文件類型",
    idCardFrontPhoto: "身份證正面照*",
    idCardFrontPhotoPlaceholder: "上載身份到正面照",
    idCardBackPhoto: "身份證反面照*",
    idCardBackPhotoPlaceholder: "上載身份證反面照",
    holdingCardPhoto: "手持證件照*",
    holdingCardPhotoPlaceholder: "上載手持證件照",
    bankName: "銀行名稱*",
    bankNamePlaceholder: "輸入銀行名稱",
    branchOpening: "開戶支行*",
    branchOpeningPlaceholder: "輸入開戶支行",
    bankCardNo: "銀行卡號碼*",
    bankCardNoPlaceholder: "輸入銀行卡號碼",
    bankCardFacePhoto: "銀行卡正面照*",
    bankCardFacePhotoPlaceholder: "上載銀行卡正面照",
    agent: "代理人",
    agentPlaceholder: "輸入代理人名稱",
    referralCode: "推薦碼",
    referralCodePlaceholder: "輸入推薦碼",
    phoneNo: "電話號碼*",
    phoneNoPlaceholder: "輸入電話號碼",
    verifyCodePlaceholder: "填寫認證碼",
    email: "電子郵箱*",
    emailPlaceholder: "輸入電子郵箱",
    watchInvestRelatedVideo: "觀看投資相關影片",
    getVerifyCode: "獲取驗證碼",
    selectImageFrom: "從哪裡選擇圖片",
    camera: "相機",
    gallery: "圖庫",
    idNumber: "証件號碼*",
    idNumberPlaceholder: "輸入証件號碼",
    bankAccountName: "銀行戶口名稱*",
    bankAccountNamePlaceholder: "輸入銀行戶口名稱",
  },
  newPriceReminderScreen: {
    title: "到價提示設定",
    goods: "商品",
    londonGold: "倫敦金",
    londonSilver: "倫敦銀",
    dollarIndex: "美元指数",
    dollar: "美元",
    promptMode: "提示方式",
    priceReminder: "到價提示",
    priceAlert: "漲跌提示",
    promptCondition: "提示條件",
    valueGridIncrease: "價格升至",
    valueGridDecrease: "價格降至",
    latestPrice: "最新價",
    riseTo: "漲至",
    dropTo: "降至",
    riseToPlaceholder: "高於最新價2-50美元。",
    dropToPlaceholder: "低於最新價2-50美元。",
    validPeriod: "有效期",
    validToday: "當天有效",
    validThisWeek: "當周有效",
    validThisMonth: "當月有效",
    validThisYear: "當年有效",
    establish: "建立",
    cancelConfirm: "確定要取消嗎?"
  },
  information: {
    title: "信息中心",
    all: "全部",
    notification: "消息通知",
    eventNotificaiont: "活動通知",
    transactionNotice: "出入金通知",
    systemNotification: "系統通知",
  },
  // from this line, below texts from template screen of project. Just leave it here.
  welcomeScreen: {
    postscript:
      "psst  — This probably isn't what your app looks like. (Unless your designer handed you these screens, and in that case, ship it!)",
    readyForLaunch: "Your app, almost ready for launch!",
    exciting: "(ohh, this is exciting!)",
    letsGo: "Let's go!",
  },
  errorScreen: {
    title: "Something went wrong!",
    errorTitle: "Error title",
    errorContent: "Error content",
    reset: "RESET APP",
  },
  emptyStateComponent: {
    generic: {
      heading: "So empty... so sad",
      content: "No data found yet. Try clicking the button to refresh or reload the app.",
      button: "Let's try this again",
    },
  },

  loginScreen: {
    signIn: "Sign In",
    enterDetails:
      "Enter your details below to unlock top secret info. You'll never guess what we've got waiting. Or maybe you will; it's not rocket science here.",
    emailFieldLabel: "Email",
    passwordFieldLabel: "Password",
    emailFieldPlaceholder: "Enter your email address",
    passwordFieldPlaceholder: "Super secret password here",
    tapToSignIn: "Tap to sign in!",
    hint: "Hint: you can use any email address and your favorite password :)",
  },
  demoNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
  },
  demoCommunityScreen: {
    title: "Connect with the community",
    tagLine:
      "Plug in to Infinite Red's community of React Native engineers and level up your app development with us!",
    joinUsOnSlackTitle: "Join us on Slack",
    joinUsOnSlack:
      "Wish there was a place to connect with React Native engineers around the world? Join the conversation in the Infinite Red Community Slack! Our growing community is a safe space to ask questions, learn from others, and grow your network.",
    joinSlackLink: "Join the Slack Community",
    makeIgniteEvenBetterTitle: "Make Ignite even better",
    makeIgniteEvenBetter:
      "Have an idea to make Ignite even better? We're happy to hear that! We're always looking for others who want to help us build the best React Native tooling out there. Join us over on GitHub to join us in building the future of Ignite.",
    contributeToIgniteLink: "Contribute to Ignite",
    theLatestInReactNativeTitle: "The latest in React Native",
    theLatestInReactNative: "We're here to keep you current on all React Native has to offer.",
    reactNativeRadioLink: "React Native Radio",
    reactNativeNewsletterLink: "React Native Newsletter",
    reactNativeLiveLink: "React Native Live",
    chainReactConferenceLink: "Chain React Conference",
    hireUsTitle: "Hire Infinite Red for your next project",
    hireUs:
      "Whether it's running a full project or getting teams up to speed with our hands-on training, Infinite Red can help with just about any React Native project.",
    hireUsLink: "Send us a message",
  },
  demoShowroomScreen: {
    jumpStart: "Components to jump start your project!",
    lorem2Sentences:
      "Nulla cupidatat deserunt amet quis aliquip nostrud do adipisicing. Adipisicing excepteur elit laborum Lorem adipisicing do duis.",
    demoHeaderTxExample: "Yay",
    demoViaTxProp: "Via `tx` Prop",
    demoViaSpecifiedTxProp: "Via `{{prop}}Tx` Prop",
  },
  demoDebugScreen: {
    howTo: "HOW TO",
    title: "Debug",
    tagLine:
      "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!",
    reactotron: "Send to Reactotron",
    reportBugs: "Report Bugs",
    demoList: "Demo List",
    demoPodcastList: "Demo Podcast List",
    androidReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.",
    iosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    macosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    webReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    windowsReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
  },
  demoPodcastListScreen: {
    title: "React Native Radio episodes",
    onlyFavorites: "Only Show Favorites",
    favoriteButton: "Favorite",
    unfavoriteButton: "Unfavorite",
    accessibility: {
      cardHint:
        "Double tap to listen to the episode. Double tap and hold to {{action}} this episode.",
      switch: "Switch on to only show favorites",
      favoriteAction: "Toggle Favorite",
      favoriteIcon: "Episode not favorited",
      unfavoriteIcon: "Episode favorited",
      publishLabel: "Published {{date}}",
      durationLabel: "Duration: {{hours}} hours {{minutes}} minutes {{seconds}} seconds",
    },
    noFavoritesEmptyState: {
      heading: "This looks a bit empty",
      content:
        "No favorites have been added yet. Tap the heart on an episode to add it to your favorites!",
    },
  },
}

export default tc
export type Translations = typeof tc