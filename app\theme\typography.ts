// TODO: write documentation about fonts and typography along with guides on how to add custom fonts in own
// markdown file and add links from here

import { Platform } from "react-native"
import {
  /* useFonts,
  NotoSans_100Thin,
  NotoSans_100Thin_Italic,
  NotoSans_200ExtraLight,
  NotoSans_200ExtraLight_Italic,
  NotoSans_300Light,
  NotoSans_300Light_Italic, */
  NotoSans_400Regular as NotoSans400Regular,
  /* NotoSans_400Regular_Italic , */
  NotoSans_500Medium as NotoSans500Medium,
  /* NotoSans_500Medium_Italic, */
  NotoSans_600SemiBold as NotoSans600SemiBold,
  /* NotoSans_600SemiBold_Italic,  */
  NotoSans_700Bold as NotoSans700Bold,
  /* NotoSans_700Bold_Italic,
   NotoSans_800ExtraBold,
  NotoSans_800ExtraBold_Italic,
  NotoSans_900Black,
  NotoSans_900Black_Italic */
} from "@expo-google-fonts/noto-sans"

export const customFontsToLoad = {
  NotoSans400Regular,
  NotoSans500Medium,
  NotoSans600SemiBold,
  NotoSans700Bold
}

const fonts = {
  notoSans:{
    thin: "NotoSans400Regular",
    light: "NotoSans400Regular",
    normal: "NotoSans400Regular",
    medium: "NotoSans500Medium",
    semiBold: "NotoSans600SemiBold",
    bold: "NotoSans700Bold",
  },
  helveticaNeue: {
    // iOS only font.
    thin: "HelveticaNeue-Thin",
    light: "HelveticaNeue-Light",
    normal: "Helvetica Neue",
    medium: "HelveticaNeue-Medium",
  },
  courier: {
    // iOS only font.
    normal: "Courier",
  },
  sansSerif: {
    // Android only font.
    thin: "sans-serif-thin",
    light: "sans-serif-light",
    normal: "sans-serif",
    medium: "sans-serif-medium",
  },
  monospace: {
    // Android only font.
    normal: "monospace",
  },
}

export const typography = {
  /**
   * The fonts are available to use, but prefer using the semantic name.
   */
  fonts,
  /**
   * The primary font. Used in most places.
   */
  primary: fonts.notoSans,
  /**
   * An alternate font used for perhaps titles and stuff.
   */
  // secondary: Platform.select({ ios: fonts.helveticaNeue, android: fonts.sansSerif }),
  secondary: Platform.select({ ios: fonts.notoSans, android: fonts.notoSans }),
  /**
   * Lets get fancy with a monospace font!
   */
  code: Platform.select({ ios: fonts.courier, android: fonts.monospace }),
}
