import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { ROW_INFO, TEXT_CONTENT, TEXT_SCREEN_TITLE } from "../../theme/baseStyle"
import { FLEX, MARGIN_DF_TOP, MARGIN_S_DF_RIGHT, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_GRAY, W_100P } from "../../theme/mStyle"
import { View } from "react-native"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

interface PersonalInformationScreenProps extends AppStackScreenProps<"PersonalInformation"> {}
export const PersonalInformationScreen: FC<PersonalInformationScreenProps> = observer(function PersonalInformationScreen(_props) {
  

  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
  }

  const getContent = (key: string) => {
    if (!AppStorage.appStorage.userDevice) return ""
    if (key == "accountId") {
      if (Helper.isLiveAccount()) {
        return AppStorage.appStorage.userOtherInfo.data.trade_account
      } else if (Helper.isDemoUser()) {
        return AppStorage.appStorage.userDevice.demoClientId
      } else {
        return AppStorage.appStorage.userDevice.clientId
      }
    } else if (key == "name") {
      if (Helper.isLiveAccount()) {
        return AppStorage.appStorage.userOtherInfo.data.name
      } else {
        return AppStorage.appStorage.userDevice.name
      }      
    } else if (key == "email") {
      if (AppStorage.appStorage.userOtherInfo) {
        if (Helper.isLiveAccount()) {
          return AppStorage.appStorage.userOtherInfo.data.email
        } else {
          return AppStorage.appStorage.userOtherInfo.data.account_email
        }
      }
    } else if (key == "gender") {
      if (AppStorage.appStorage.userOtherInfo) {
        if (Helper.isLiveAccount()) {
          if (AppStorage.appStorage.userOtherInfo.data.gender == 1) {
            return translate("account.male")
          } else if (AppStorage.appStorage.userOtherInfo.data.gender == 2) {
            return translate("account.female")
          }
          return "-"
        } else {
          return "-"
        }
      }
    } else {
      return "-"
    }
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    prepareData()
  }, [])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false}
        title={translate("account.personalInfo")} />
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.tradingAccountNumber")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("accountId")} />
      </View>
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.name")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("name")} />
      </View>
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.email")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("email")} />
      </View>
      {
        Helper.isLiveAccount() && 
        <View style={ROW_INFO}>
          <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.gender")} />
          <View style={FLEX}/>
          <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("gender")} />
        </View>
      }
    </Screen>
  )
})