package com.sinosound.mobile2;

import android.content.Context;
import android.util.Log;

import com.aliyun.ams.push.AliyunPushEventSender;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableNativeMap;

public class MessageReceiver extends com.aliyun.ams.push.AliyunPushMessageReceiver {

    /**
     * 从通知栏打开通知的扩展处理
     *
     * @param context
     * @param title
     * @param summary
     * @param extraMap
     */
    @Override
    public void onNotificationOpened(Context context, String title, String summary,
                                     String extraMap) {
        WritableMap writableMap = new WritableNativeMap();
        writableMap.putString("title", title);
        writableMap.putString("summary", summary);
        writableMap.putString("extra", extraMap);
        AliyunPushExtensionModule.setLatestNotification(writableMap);
        super.onNotificationOpened(context, title, summary, extraMap);
    }

}
