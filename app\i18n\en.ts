const en = {
  common: {
    ok: "OK",
    cancel: "Cancel",
    confirm: "Confirm",
    back: "Back",
    logOut: "Logout",
    jan: "Jan.",
    feb: "Feb.",
    mar: "Mar.",
    apr: "Apr.",
    may: "May",
    jun: "Jun.",
    jul: "Jul.",
    aug: "Aug.",
    sep: "Sep.",
    oct: "Oct.",
    nov: "Nov.",
    dec: "Dec.",
    resend: "Resend",
    submit: "Submit",
    yes: "Yes",
    no: "No",
    year: "Year",
    month: "Month",
    day: "Day",
    delete: "Delete",
    sun: "Sun",
    mon: "Mon",
    tue: "Tue",
    wed: "Wed",
    thu: "Thu",
    fri: "Fri",
    sat: "Sat",
    upload: "Upload",
    return: "Back",
    male: "Male",
    female: "Female",
    china: "China",
    hongkongAndOthers: "Hong Kong and Others",
    hkchina: "Hong Kong China",
    macauchina: "Macau China",
    twchina: "Taiwan China",
    malaysia: "Malaysia",
    vietnam: "Vietnam",
    success: "Success",
    today: "Today",
    jan<PERSON>hort: "Jan",
    febShort: "Feb",
    marShort: "Mar",
    aprShort: "Apr",
    mayShort: "May",
    junShort: "Jun",
    julShort: "Jul",
    augShort: "Aug",
    sepShort: "Sep",
    octShort: "Oct",
    novShort: "Nov",
    decShort: "Dec"
  },
  errors: {
    invalidEmail: "Invalid email address.",
    invalidReminderId: "Price reminder record does not exist.",
    standard: "Please try again later.",
    enterPhone: "Please enter your phone number.",
    enterEmail: "Please enter your email address.",
    enterName: "Please enter your name.",
    phoneExits: "This phone number already registered.",
    otpWrong: "Wrong verification code, please re enter.",
    phoneVerificationIncorrect: "Wrong phone verification code.",
    unAuthorize: "Unauthorized, please login again.",
    addPriceReminder: "Please add price reminder.",
    noRelatedData: "No record.",
    inputAllRequiredValues: "Please enter the mandatory fields.",
    watchInvestRelatedVideo: "Please watch the investment video.",
    selectCountry: "Please select a country or region.",
    phoneFormatWrong: "The phone digit is invalid. Please correct your phone number.",
    demoRgCode10017: "Wrong phone OTP",
    demoRgCode10018: "Wrong email OTP",
    demoRgCode200: "Your Demo Account has been successfully created",
    realRgCode10029: "Wrong phone OTP",
    realRgCode10033: "Wrong email OTP",
    realRgCode10032: "Phone number already registered",
    realRgCode10031: "Email already registered",
    realRgCode200: "Your Live Account has been successfully created",
    networkTimeout: "Execution timeout, Please try again",
    networkDelay: "Execution timeout, Please try again",
    canTypeSymbolForPhone: "Cannot type symbol in phone",
    noInternet: "Bad Network, Please try again later.",
    accountDisabled: "Invalid account",
    wrongToken: "Wrong token, please login again"
  },
  menu: {
    home: "Home",
    trend: "Trends",
    trade: "Quotes",
    information: "Inbox",
    account: "My",
  },
  homePage: {
    updateApp: "Please update to the latest version",
    createAccount: "Create Account",
    trend: "Trends",
    latestNotification: "Announcements",
    newsNotification: "News Announcement",
    todayTopics: "Market Forecast",
    morningNews: "Morning News",
    latestNews: "Latest Events",
    financialNews: "Quick Info",
    financialCalendar: "Calendar",
    information: " Info",
    applyDemoAccount: "Apply Demo Account",
    applyLiveAccount: "Apply Live Account",
    investNow: "Deposit",
    contactCustomerService: "Contact CS",
  },
  trendPage: {
    // todayTopics: "今日话题",
    // latestNews: "最新活动",
    // latestNotification: "最新公告",
    eveningNews: "Evening News",
    news: "News",
    calendar: "Calendar",
    readMore: "Read more",
    readLess: "Collapse",
    events: "Event",
    financialEvents: "Financial Events",
    holidayClosure: "Holiday closure",
    formerValue: "Former Value",
    predictiveValue: "預測值",
    publishedValue: "公布值",
    affect: "影響",
    affectL: "利多 金銀",
    affectD: "利空 金銀",
    affectLApi: "利多",
    affectDApi: "利空",
  },
  searchPage: {
    todayNewsTitle: "Search Market Forecast",
    latestNewsTitle: "Search Latest Events",
    noSearchFound: "No search results found.",
    searchPlaceholder: "Enter search keywords",
  },
  tradePage: {
    priceReminder: "Price Reminder",
    addPriceReminder: "Add a New Price Reminder",
    becomeMember: "Be a member and use the price alerts",
    createRealAccount: "Create Live Account",
    createDemoAccount: "Create Demo Account",
    priceReminderInfo: "Price reminder info.",
    priceReminderInfoContent: "1. New users get 15 days of free use.\n2. Successful apply for a demo trading account, 15 days free use period.\n3. Successfully apply for a live trading account, can be used permanently. \n4. You can create 12 active price reminder at the same time.\n5. Price reminder created during the free use period will continue to work after expiration.\n6. After the free use period expires, you can no longer create new price reminder.",
    priceReminderInfoContent1: "New users get 15 days of free use.",
    priceReminderInfoContent2: "Successful application of demo trading account, 15 days free use period.",
    priceReminderInfoContent3: "Successfully apply for a live trading account, can be used permanently.",
    priceReminderInfoContent4: "You can create 12 active price reminder at the same time.",
    priceReminderInfoContent5: "Price reminder created during the free use period will continue to work after expiration.",
    priceReminderInfoContent6: "After the free use period expires, you can no longer create new price reminder.",
    priceReminderCreated: "Created price reminder.",
    priceReminderExpired: "The trial period for the price reminder feature has expired.",
    priceReminderLoginNeeded: "Please login to use this feature.",
    timeDemoExpire: "Expires in",
    upgradeToRealAccount: "Upgrade to Live Account",
    goToMT4: "Go to MT4",
    promptRecord: "Reminder record",
    priceReach: "Price Reach",
    priceUpDown: "Up and Down",
    riseTo: "Rise to",
    riseToInNew: "Rise to",
    dropTo: "Drop to",
    validUntil: "Valid until",
    cancelled: "Cancelled",
    inactive: "Completed",
    expired: "Expired",
    showPromtRecordTo: "Show reminder record to",
  },
  priceModal: {
    londonGold: "Gold",
    londonSilver: "Silver",
    dollarIndex: "USD Index",
    priceReach: "Price Reach",
    priceUpDown: "Up and Down",
    riseTo: "Rise to",
    riseToInNew: "Rise to",
    dropTo: "Drop to",
    notificationDate: "Notification date",
    askNotification: "Do you allow to receive push notification?",
    enableNotification: "Allow",
    disableNotification: "Don't Allow",
  },
  boarding: {
    setting: "Settings",
    login: "Login",
    register: "Registration",
    screenAlwaysOn: "Screen Always On",
    on: "Always On",
    on2: "On",
    off: "Off",
    displaySetting: "Display Settings",
    language: "Language",
    clearCache: "Clear Cache",
    displayModeRed: "Red rising and green falling",
    displayModeGreen: "Green rising and red falling",
    traditionChinese: "Traditional Chinese",
    simplifiedChinese: "Simplified Chinese",
    clear: "Clear",
    aboutHan: "About Us",
    companyProfile: "Company Profile",
    majorEventOfHan: "Events",
    hansJournal: "Periodicals",
    contactCustomerService: "Customer Service",
    selectCountry: "Select Country / Region",
    enterVerifyCode: "Enter Verification Code",
    sentTo: "Already sent to",
    newCodeSent: "New verification code already sent",
    inputYourName: "Please enter the name",
    submitRegister: "Submit",
    registerSuccess: "You have successfully registered.",
    returnHome: "Back to Home"
  },
  account: {
    acountInfo: "Account Info",
    personalInfo: "Personal Info",
    emailSubscription: "SMS Subscription",
    deleteAccount: "Delete Account",
    userTypeDemo: "Demo",
    userTypeLive: "Live",
    applyForDemoAccount: "Apply Demo Account",
    applyRealTradingAccount: "Upgrade to Live Account",
    enabled: "Enabled",
    disabled: "Disabled",
    status: "Status",
    clubPoint: "SinoSound Club Points",
    accountBalance: "Account balance",
    eject: "Deposit",
    withdraw: "Withdraw",
    bankInfo: "Bank Information",
    agentCenter: "Agent Center",
    fundAcount: "Deposit",
    transactionHistory: "Trade Record",
    todayExchangeRate: "Exchange rate",
    live: "Live",
    currentAccountCurrency: "US$",
    tradingAccountNumber: "Trade account number",
    name: "Name",
    email: "Email",
    gender: "Gender",
    male: "Male",
    female: "Female",
    bankCardNo: "Bank card no",
    bankBranch: "Branch no",
    bankName: "Bank Name",
    bankBranchName: "Branch",
    accountHolderName: "Account Holder Name",
    bankCardNumber: "Bank Card Number",
    balance: "Balance",
    netWorth: "Net",
    availableMargin: "Available Margin",
    fundDetails: "Funds Details",
    transAmount: "Apply Amount",
    handleFee: "Handling Fee",
    currency: "Currency",
    exchangeRate: "Exchange Rate",
    finish: "Completed",
    processing: "In Progress",
    canceled: "Cancel",
    today: "Today",
    lastWeek: "Last Week",
    lastMonth: "Last Month",
    last3Month: "Last 3 Months",
    exchangeRateDeposite: "Deposit",
    exchangeRatePick: "Withdraw",
    transactionDeposite: "Deposit application",
    transactionWithdraw: "Withdraw application",
    transactionProcessing: "In progress",
    transactionFinish: "Completed",
    transactionCancel: "Canceled",
    subscriptionConfirm: "Confirm subscribe?",
    unsubscribeConfirm: "Confirm unsubscribe?",
    displayRedConfirm: "Do you want to set it to red up, green down?",
    displayGreenConfirm: "Do you want to set it to green up, red down?",
    langTraditionChinese: "繁體中文",
    langSimplifiedChinese: "简体中文",
    langEnglish: "English",
    clearCacheConfirm: "Confirm to Clear Cache?",
    deleteAccountConfirm: "Confirm to Delete Your Account?",
    logoutSuccess: "Logout successfully",
    confirmLogout: "Confirm logout?",
    noRecord: "No record",
    ib_balance: "Account balance",
  },
  deleteAccount: {
    title: "Delete account",
    content: "Important Notice Before Deleting Your Mobile App Account\n\nDear user,\n\nBefore you decide to delete your mobile application account, we have the following important notice for you:\n\nData Removal: Once you delete your account, we will not retain any of your personal data and preference settings entered in the application. This information will be completely deleted and unrecoverable.\n\nSetting Loss: All your personal settings and preferences within the app, such as language, price reminder notifications, etc., will be removed along with the account deletion. \n\nService Interruption: After deleting your account, you will no longer be able to use any of the services provided by the application. Please carefully consider whether you still need to use the service before deciding to delete the account.\n\nCustomer Support: If you encounter any issues or have questions after deleting your account, feel free to contact our customer service department. We will do our best to assist you.\n\nWe kindly remind you to carefully consider and prepare before proceeding with the account deletion. If you have any questions, please don't hesitate to reach out to us.\n\nWe wish you a pleasant experience using our services.",
    confirmDelete: "Confirm delete account"
  },
  accountLogout: {
    title: "Account already logout",
    message: "Your account has been login in another device."
  },
  demoAccountSignUp: {
    title: "Apply for a demo account",
    name: "Real Name*",
    namePlaceholder: "Enter real name",
    phoneNo: "Phone*",
    phoneNoPlaceholder: "Enter phone",
    verifyCodePlaceholder: "Enter verification code",
    email: "Email*",
    emailPlaceholder: "Enter Email Address",    
    getVerifyCode: "Get Code",
    resendVerifyCode: "Resend OTP",
    verifycodeSent: "OTP has been sent",
  },
  liveAccountSignUp: {
    title: "Apply for a live account",
    agentCode: "Agent & Referral Code",
    auth: "Authentication",
    name: "Real Name*",
    namePlaceholder: "Enter real name",
    gender: "Gender*",
    genderPlaceholder: "Select gender",
    idDocumentType: "Type of Identity document*",
    idDocumentTypePlaceholder: "Select document type",
    idCardFrontPhoto: "Front photo of ID card*",
    idCardFrontPhotoPlaceholder: "Upload front photo of ID card",
    idCardBackPhoto: "ID Reverse Photo*",
    idCardBackPhotoPlaceholder: "Upload ID reverse photo",
    holdingCardPhoto: "Handheld photo*",
    holdingCardPhotoPlaceholder: "Upload handheld photo",
    bankName: "Bank name*",
    bankNamePlaceholder: "Enter bank name",
    branchOpening: "Branch*",
    branchOpeningPlaceholder: "Enter branch",
    bankCardNo: "Bank card number*",
    bankCardNoPlaceholder: "Enter bank card number",
    bankCardFacePhoto: "Bank Card Front Photo*",
    bankCardFacePhotoPlaceholder: "Upload bank card front photo",
    agent: "Agent (Optional)",
    agentPlaceholder: "Enter the name of the agent",
    referralCode: "Referral Code (Optional)",
    referralCodePlaceholder: "Enter referral code",
    phoneNo: "Phone Number*",
    phoneNoPlaceholder: "Enter phone number",
    verifyCodePlaceholder: "Enter verification code",
    email: "Email*",
    emailPlaceholder: "Enter Email",
    watchInvestRelatedVideo: "Watch Investment Videos",
    getVerifyCode: "Get Code",
    selectImageFrom: "Select image",
    camera: "Camera",
    gallery: "Gallery",
    idNumber: "Identity document number*",
    idNumberPlaceholder: "Enter ID card number",
    bankAccountName: "Bank Account Name*",
    bankAccountNamePlaceholder: "Enter bank account name",
  },
  newPriceReminderScreen: {
    title: "Price Reminder Settings",
    goods: "Products",
    londonGold: "Gold",
    londonSilver: "Silver",
    dollarIndex: "USD Index",
    dollar: "USD",
    promptMode: "Types",
    priceReminder: "Price reach",
    priceAlert: "Up and down",
    promptCondition: "Condition",
    valueGridIncrease: "Grid Up",
    valueGridDecrease: "Grid Down",
    latestPrice: "Latest Price",
    riseTo: "Up to",
    dropTo: "Down to",
    riseToPlaceholder: "Higher than latest price 2-50 us dollars.",
    dropToPlaceholder: "Lower than latest price 2-50 us dollars.",
    riseToPlaceholderPrefix: "Higher than latest price ",
    riseToPlaceholderSuffix: " us dollars.",
    dropToPlaceholderPrefix: "Lower than latest price ",
    dropToPlaceholderSuffix: " us dollars.",
    validPeriod: "Valid period",
    validToday: "This Day",
    validThisWeek: "This Week",
    validThisMonth: "This Month",
    validThisYear: "This Year",
    establish: "Create",
    cancelConfirm: "Confirm Cancel?"
  },
  information: {
    title: "Inbox",
    all: "All",
    notification: "Forecast",
    eventNotificaiont: "Event",
    transactionNotice: "Deposit/Withdraw",
    systemNotification: "System",
  },
  // from this line, below texts from template screen of project. Just leave it here.
  welcomeScreen: {
    postscript:
      "psst  — This probably isn't what your app looks like. (Unless your designer handed you these screens, and in that case, ship it!)",
    readyForLaunch: "Your app, almost ready for launch!",
    exciting: "(ohh, this is exciting!)",
    letsGo: "Let's go!",
  },
  errorScreen: {
    title: "Something went wrong!",
    errorTitle: "Error title",
    errorContent: "Error content",
    reset: "RESET APP",
  },
  emptyStateComponent: {
    generic: {
      heading: "So empty... so sad",
      content: "No data found yet. Try clicking the button to refresh or reload the app.",
      button: "Let's try this again",
    },
  },

  loginScreen: {
    signIn: "Sign In",
    enterDetails:
      "Enter your details below to unlock top secret info. You'll never guess what we've got waiting. Or maybe you will; it's not rocket science here.",
    emailFieldLabel: "Email",
    passwordFieldLabel: "Password",
    emailFieldPlaceholder: "Enter your email address",
    passwordFieldPlaceholder: "Super secret password here",
    tapToSignIn: "Tap to sign in!",
    hint: "Hint: you can use any email address and your favorite password :)",
  },
  demoNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
  },
  demoCommunityScreen: {
    title: "Connect with the community",
    tagLine:
      "Plug in to Infinite Red's community of React Native engineers and level up your app development with us!",
    joinUsOnSlackTitle: "Join us on Slack",
    joinUsOnSlack:
      "Wish there was a place to connect with React Native engineers around the world? Join the conversation in the Infinite Red Community Slack! Our growing community is a safe space to ask questions, learn from others, and grow your network.",
    joinSlackLink: "Join the Slack Community",
    makeIgniteEvenBetterTitle: "Make Ignite even better",
    makeIgniteEvenBetter:
      "Have an idea to make Ignite even better? We're happy to hear that! We're always looking for others who want to help us build the best React Native tooling out there. Join us over on GitHub to join us in building the future of Ignite.",
    contributeToIgniteLink: "Contribute to Ignite",
    theLatestInReactNativeTitle: "The latest in React Native",
    theLatestInReactNative: "We're here to keep you current on all React Native has to offer.",
    reactNativeRadioLink: "React Native Radio",
    reactNativeNewsletterLink: "React Native Newsletter",
    reactNativeLiveLink: "React Native Live",
    chainReactConferenceLink: "Chain React Conference",
    hireUsTitle: "Hire Infinite Red for your next project",
    hireUs:
      "Whether it's running a full project or getting teams up to speed with our hands-on training, Infinite Red can help with just about any React Native project.",
    hireUsLink: "Send us a message",
  },
  demoShowroomScreen: {
    jumpStart: "Components to jump start your project!",
    lorem2Sentences:
      "Nulla cupidatat deserunt amet quis aliquip nostrud do adipisicing. Adipisicing excepteur elit laborum Lorem adipisicing do duis.",
    demoHeaderTxExample: "Yay",
    demoViaTxProp: "Via `tx` Prop",
    demoViaSpecifiedTxProp: "Via `{{prop}}Tx` Prop",
  },
  demoDebugScreen: {
    howTo: "HOW TO",
    title: "Debug",
    tagLine:
      "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!",
    reactotron: "Send to Reactotron",
    reportBugs: "Report Bugs",
    demoList: "Demo List",
    demoPodcastList: "Demo Podcast List",
    androidReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.",
    iosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    macosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    webReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    windowsReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
  },
  demoPodcastListScreen: {
    title: "React Native Radio episodes",
    onlyFavorites: "Only Show Favorites",
    favoriteButton: "Favorite",
    unfavoriteButton: "Unfavorite",
    accessibility: {
      cardHint:
        "Double tap to listen to the episode. Double tap and hold to {{action}} this episode.",
      switch: "Switch on to only show favorites",
      favoriteAction: "Toggle Favorite",
      favoriteIcon: "Episode not favorited",
      unfavoriteIcon: "Episode favorited",
      publishLabel: "Published {{date}}",
      durationLabel: "Duration: {{hours}} hours {{minutes}} minutes {{seconds}} seconds",
    },
    noFavoritesEmptyState: {
      heading: "This looks a bit empty",
      content:
        "No favorites have been added yet. Tap the heart on an episode to add it to your favorites!",
    },
  },
}

export default en
export type Translations = typeof en