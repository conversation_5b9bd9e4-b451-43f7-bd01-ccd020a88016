import { observer } from "mobx-react-lite"
import React, { FC, useState} from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { View } from "react-native"
import { DISABLED, ENABLED, FLEX, FLEX_2, MARGIN_DF_BOTTOM, MARGIN_S_DF_LEFT, MARGIN_S_DF_RIGHT, ROW_CENTER, } from "../../theme/mStyle"
import { BG_GRAY, BTN_CONTAINER} from "../../theme/baseStyle"
import { colors } from "app/theme"
import Video from 'react-native-video';
import { Api } from "app/api/api"
import { AppStorage } from "app/utils/appStorage"

interface InvestRelatedVideoScreenProps extends AppStackScreenProps<"InvestRelatedVideo"> {}
export const InvestRelatedVideoScreen: FC<InvestRelatedVideoScreenProps> = observer(function InvestRelatedVideoScreen(_props) {

  const [canConfirm, setCanConfirm] = useState(false)
  /* const getVideoFile = () => {
    if (AppStorage.appStorage.setting.language == "SC") {
      return require("assets/CGSEInvestEduVideo_sc.mp4")
    } else if (AppStorage.appStorage.setting.language == "EN") {
      return require("assets/CGSEInvestEduVideo_en.mp4")
    } else {
      return require("assets/CGSEInvestEduVideo_ts.mp4")
    }
  } */

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{backgroundColor: colors.mine.black, flex: 1}}
      safeAreaEdges={["top", "bottom"]}
    >
     <View style={FLEX}>
     {/* <Video 
      // Can be a URL or a local file.
      source={getVideoFile()}
      // source={{uri: Api.relatedVideoUrl}}
      // Store reference  
      // ref={videoRef}
      // Callback when remote video is buffering                                      
      // onBuffer={onBuffer}
      // Callback when video cannot be loaded              
      // onError={onError}      
      resizeMode="contain"
      onEnd={() => {
        setCanConfirm(true)
      }}
      style={{width: '100%', height: '100%'}}
    /> */}
     </View>
     <View style={[ROW_CENTER, MARGIN_DF_BOTTOM]}>
      <Text 
        onPress={() => {
          _props.navigation.goBack()
        }}
        style={[BTN_CONTAINER, BG_GRAY, MARGIN_S_DF_RIGHT, FLEX]} tx="common.return" />
      <Text 
        onPress={() => {
          if (canConfirm) {
            _props.navigation.goBack()
            if (_props.route.params.onWatched) {
              _props.route.params.onWatched()
            }
          }        
        }}
        style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX_2, canConfirm ? ENABLED : DISABLED]} tx="common.confirm" />
     </View>
    </Screen>
  )
})
