---
description: Repository Information Overview
alwaysApply: true
---

# SinoMobile Information

## Summary
SinoMobile is a React Native mobile application built using the Ignite boilerplate. It's a cross-platform app targeting both iOS and Android platforms, with separate configurations for UAT (User Acceptance Testing) and production environments.

## Structure
- **app/**: Core application code including components, screens, and business logic
- **android/**: Android-specific configuration and native code
- **ios/**: iOS-specific configuration and native code
- **test/**: Testing configuration and utilities
- **ignite/**: Templates and boilerplate code from Ignite framework
- **assets/**: Images, icons, and other static resources

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js >=18
**Build System**: React Native CLI
**Package Manager**: npm/yarn

## Dependencies
**Main Dependencies**:
- React Native 0.71.13
- React 18.2.0
- React Navigation (Stack, Bottom Tabs, Material Top Tabs)
- MobX State Tree 5.1.5
- Expo 48.0.15 (with various Expo modules)
- i18n-js 3.9.2 (Internationalization)
- <PERSON><PERSON>ush (for notifications)

**Development Dependencies**:
- TypeScript 5.0.4
- Jest (Testing framework)
- ESLint/Prettier (Code quality)
- Babel (JavaScript transpilation)
- Metro (React Native bundler)

## Build & Installation
```bash
# Install dependencies
npm install
# or
yarn install

# Start development server
npm run start
# or
yarn start

# Run on Android (UAT environment)
npm run android:env:uat
# or
yarn android:env:uat

# Run on Android (Production environment)
npm run android:env:prod
# or
yarn android:env:prod

# Run on iOS (UAT environment)
npm run ios:uat
# or
yarn ios:uat

# Run on iOS (Production environment)
npm run ios:prod
# or
yarn ios:prod

# Create release build for Android
npm run release:android
# or
yarn release:android
```

## Testing
**Framework**: Jest with Expo
**Test Location**: `/test` directory
**Configuration**: jest.config.js
**Run Command**:
```bash
npm run test
# or
yarn test
```

## Mobile Platform Support
**iOS**:
- Multiple targets: SinoMobile (Production) and SinoMobileUAT (Testing)
- Native modules integration (AliyunPushExtensionModule)
- Multiple language support (en, zh-Hans, zh-Hant, zh-HK)

**Android**:
- Multiple build variants (production, uat)
- Gradle-based build system
- Custom app icons and splash screens

## Internationalization
**Languages**: 
- English (en)
- Simplified Chinese (sc)
- Traditional Chinese (tc)
**Configuration**: Located in app/i18n directory
**Default**: Simplified Chinese

## State Management
**Library**: MobX State Tree
**Store Structure**: RootStore pattern with models in app/models
**Persistence**: AsyncStorage for local data storage

## Navigation
**Library**: React Navigation
**Structure**: 
- AppNavigator as the main navigator
- Bottom tab navigation for main sections
- Stack navigation for screen flows
- Material top tabs for some sections