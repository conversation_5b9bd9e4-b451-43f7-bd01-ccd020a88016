import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { FlatList, View } from "react-native"
import { getMarketNews } from "app/api/model"
import { useIsFocused } from "@react-navigation/native"
import { RefreshStorage } from "app/utils/RefreshStorage"
import { Api, ApiService } from "app/api/api"
import { CardMarketingNews } from "../_Home/CardMarketingNews"
import { HIDE_PRELOAD_CONTAINER, TEXT_CONTENT } from "app/theme/baseStyle"
import { useNetInfo } from "@react-native-community/netinfo"
import { AppStorage } from "app/utils/appStorage"
import { TopMenuIndex } from "app/navigators/TopMenu"

interface MarketNewsScreenProps extends AppStackScreenProps<"MarketNews"> { }

export const MarketNewsScreen: FC<MarketNewsScreenProps> = observer(function MarketNewsScreen(_props) {

  const [posts, setPosts] = useState([])
  const [refreshing, setRefreshing] = useState(true)
  const currentPage = useRef(1)
  const totalPage = useRef(1)
  const screenIsShown = useRef(false)

  // right value = 5
  // test load more = 0
  const loadPosts = async (isLoadMore = false) => {
    if (!AppStorage.isNetworkConnected()) {      
      AppStorage.hidePreload(TopMenuIndex.marketNews)
      setRefreshing(false)
      return
    }
    if (isLoadMore) {
      if (currentPage.current < totalPage.current) {
        currentPage.current += 1
      } else {
        return
      }
    } else {
      currentPage.current = 1
    }
    getMarketNews(
      currentPage.current,
      null,
      (data) => {
        AppStorage.hidePreload(TopMenuIndex.marketNews)
        setRefreshing(false)
        if (data && data.data && data.data.totalPage) {
          totalPage.current = data.data.totalPage
        }
        if (isLoadMore)  {
          const cPosts = posts
          data.data.record.map((item) => {
            cPosts.push(item)
          })
          setPosts(() => cPosts)
        } else {
          setPosts(() => data.data.record)
        }
      },
      (error) => {
        AppStorage.hidePreload(TopMenuIndex.marketNews)
        setRefreshing(false)
        if (!isLoadMore) {
          setPosts(() => [])
        }
        // console.log("error getMarketNews: " + screenIsShown.current)
        // if (screenIsShown.current && posts.length === 0) {
        //   ApiService.showMessageBox(error)
        // }
        console.log("error: " + error)

      }
    )
  }

  // start of count down to refresh feed
  // remember call reset timeout when user reload list
  const isScreenShown = useIsFocused()
  const timeoutRef = useRef(null)
  const resetTimeout = (delayTime = Api.reloadTime) => {
    console.log("resetTimeout: " + delayTime)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    RefreshStorage.updateScreenSetTime("MarketNews")
    timeoutRef.current = setTimeout(() => {
      console.log("reload posts")
      setRefreshing(true)
      loadPosts()
      resetTimeout()
    }, delayTime * 1000)
  }

  useEffect(() => {
    screenIsShown.current = isScreenShown
    console.log("MarketNewsScreen isScreenShown: ", screenIsShown.current)
    if (screenIsShown.current) {
      if (!AppStorage.isLoadingShow()) {
        AppStorage.hidePreload(TopMenuIndex.marketNews)
      }
      if (RefreshStorage.screenNameAdded("MarketNews")) {
        if (RefreshStorage.shouldRefreshScreen("MarketNews")) {
          loadPosts()
          resetTimeout()
        } else {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          resetTimeout(RefreshStorage.getDiffSeconds("MarketNews"))
        }
      } else {
        resetTimeout()
      }
    }
  }, [isScreenShown])
  // end of count down to refresh feed

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        loadPosts()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    AppStorage.showLoading()
    loadPosts()
  }, [])

  return (
    <Screen
      preset="fixed">
      {
        posts.length <= 0 && !refreshing &&
        <Text style={[TEXT_CONTENT, { textAlign: "center", marginTop: 20 }]} tx="errors.noRelatedData"/> 
      }
      {
        posts.length > 0 &&
        <FlatList
          nestedScrollEnabled={true}
          data={posts}
          onRefresh={() => {
            setRefreshing(true)
            loadPosts()
            resetTimeout()
          }}
          refreshing={refreshing}
          contentContainerStyle={{ paddingBottom: 50 }}
          keyExtractor={item => item.id.toString()}
          onEndReached={() => {
            console.log("onEndReached " + currentPage.current + " - " + totalPage.current)
            if (currentPage.current <= totalPage.current) {
              setRefreshing(true)
              loadPosts(true)
            }
          }}
          onEndReachedThreshold={0.5}
          renderItem={({ item }) => {
            return (
              <CardMarketingNews
                onPress={() => {
                  _props.navigation.navigate("NewsDetails", { dataObj: item, type: "MarketNews" })
                }}
                showMoreInline={true}
                data={item} />
            )
          }}
        />
      }
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_CONTAINER}></View>}
    </Screen>
  )
})
