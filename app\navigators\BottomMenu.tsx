import * as React from "react"
// import { Text } from "app/components"
// import { Dimen } from "app/theme/dimen"
// import { COLUMN, ROW } from "app/theme/mStyle"
import { Text } from "../components"
import { Dimen } from "../theme/dimen"
import { COLUMN, ROW } from "../theme/mStyle"
import { useEffect, useState } from "react"
import { TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { MIcon } from "../components/MIcon"
// import { colors } from "app/theme"
import { colors } from "../theme"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { getInboxBatchCount } from "app/api/model"
import { AppLinkStorage } from "app/utils/appLinkStorage"
import { observer } from "mobx-react-lite"
import { useIsFocused } from "@react-navigation/native"


const MenuContainer: ViewStyle = {
    ...COLUMN,
    width: Dimen.screenWidth,
    backgroundColor: colors.mine.white,    
}
const MenuMainContent: ViewStyle = {
    ...ROW,
    backgroundColor: 'white',
    position: 'absolute',
    top: -60,
    borderTopColor: colors.palette.neutral200,
    borderTopWidth: 1,
    paddingBottom: 25
}

const MenuItem: ViewStyle = {
    ...COLUMN,
    flex: 1,
    alignItems: 'center',
    paddingVertical: Dimen.padding.sm,
    paddingHorizontal: Dimen.padding.ssm
}

const MenuCenterItem: ViewStyle = {
    ...COLUMN,
    width: "25%",
    alignItems: 'center',    
    marginTop: -40
}

const MenuCenterText: TextStyle = {
    marginTop: -18
}

const FloatBatchCount: ViewStyle = {
    position: 'absolute',
    backgroundColor: colors.mine.primary,
    width: 20,
    height: 20,
    right: 15,
    borderRadius: 20,
    padding: 0,
    overflow: "hidden",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
}

const FloatBatchCountText: TextStyle = {
    fontSize: Dimen.fontSize.ssm,
    lineHeight: 20,    
    margin: 0,
    padding: 0,
    color: colors.mine.white
}

export const BottomMenu = observer(function BottomMenu(props: any) {
    const [currentTab, setCurrentTab] = useState(1)
    const insets = useSafeAreaInsets()
    const isFocused = useIsFocused()


    const switchTab = (index: number) => {
        if (index === 1) {
            setCurrentTab(index)
            props.navigation.navigate("Home")
        } else if (index === 2) {
            setCurrentTab(index)
            AppStorage.setCurrentTrendScreenIndex(1)
            props.navigation.navigate("Trend")
        } else if (index === 3) {
            setCurrentTab(index)
            props.navigation.navigate("Trade")
        } else if (index === 4) {
            setCurrentTab(index)
            AppStorage.setCurrentInformationScreenIndex(1)
            props.navigation.navigate("Information")
        } else if (index === 5) {
            if (Helper.isLoggeIn()) {
                props.navigation.navigate("Account")
            } else {
                props.navigation.navigate("Login")
            }            
        }
    }

    const loadInboxBatchCount = async () => {
        console.log("loadInboxBatchCount")
        getInboxBatchCount(
            (data) => {
                if (data.Count) {
                    AppStorage.setInboxBatchCount(data.Count)
                } else {
                    AppStorage.setInboxBatchCount(0)
                }                
                console.log("inbox batch count: " + AppStorage.appStorage.inboxBatchCount)
            },
            (error) => {
                console.log("error: " + error)
            },
            () => {
                console.log("complete")
            }
        )
    }

    const updateCurrentTabView = () => {
        if (props.state.routes != null && props.state.routes.length > 0 && props.state.routes[props.state.routes.length-1].state != null) {
            const mRoutes = props.state.routes[props.state.routes.length-1].state.routes
            console.log("mRoutes isFocused: ", mRoutes[mRoutes.length-1].name)
            if (mRoutes[mRoutes.length-1].name === "Home") {
                setCurrentTab(() => 1)
            } else if (mRoutes[mRoutes.length-1].name === "Trend") {
                setCurrentTab(() => 2)
            } else if (mRoutes[mRoutes.length-1].name === "Trade") {
                setCurrentTab(() => 3)
            } else if (mRoutes[mRoutes.length-1].name === "Information") {
                setCurrentTab(() => 4)
            } else if (mRoutes[mRoutes.length-1].name === "Account" || mRoutes[mRoutes.length-1].name === "Login") {
                // setCurrentTab(() => 5)
            }
        }
    }
    
    useEffect (() => {        
        updateCurrentTabView()     
    }, [props.state])

    useEffect(
        () => {
            console.log("AppLinkStorage.appLinkStorage.screenName: ", AppLinkStorage.appLinkStorage.screenName)
            if (isFocused) {                
                updateCurrentTabView()
            }
            if (isFocused && AppLinkStorage.appLinkStorage.screenName != "") {
                AppLinkStorage.startToGo(props.navigation)
            }
        }, [isFocused]
    )

    useEffect(() => {
        loadInboxBatchCount()
    }, [])

  return (
    <View style={[MenuContainer, {marginBottom: insets.bottom}]}>
        <View style={MenuMainContent} >
            <TouchableOpacity 
                onPress={() => switchTab(1)}
                style={MenuItem}>
                <MIcon name={currentTab == 1 ? "tabHomeActive" : "tabHomeInactive"} size={Dimen.iconSize.md} />
                <Text tx="menu.home" />
            </TouchableOpacity>
            <TouchableOpacity 
                onPress={() => switchTab(2)}
                style={MenuItem}>
                <MIcon name={currentTab == 2 ? "tabNewsActive" : "tabNewsInactive"} size={Dimen.iconSize.md} />
                <Text tx="menu.trend" />
            </TouchableOpacity>
            <TouchableOpacity 
                onPress={() => switchTab(3)}
                style={MenuCenterItem}>
                <MIcon name={"tabTrade"} size={90} />
                <Text style={MenuCenterText} tx="menu.trade" />
            </TouchableOpacity>
            <TouchableOpacity 
                onPress={() => switchTab(4)}
                style={MenuItem}>
                <MIcon name={currentTab == 4 ? "tabInboxActive" : "tabInboxInactive"} size={Dimen.iconSize.md} />
                <Text tx="menu.information" />
                {AppStorage.appStorage.inboxBatchCount > 0 && <View style={FloatBatchCount}>
                    <Text numberOfLines={1} style={FloatBatchCountText} text={AppStorage.appStorage.inboxBatchCount+""} />
                </View>}
            </TouchableOpacity>
            <TouchableOpacity 
                onPress={() => {
                    switchTab(5)                    
                }}
                style={MenuItem}>
                <MIcon name={currentTab == 5 ? "tabProfileActive" : "tabProfileInactive"} size={Dimen.iconSize.md} />
                <Text tx="menu.account" />
            </TouchableOpacity>
        </View>
    </View>
  )
})


