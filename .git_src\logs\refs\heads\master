0000000000000000000000000000000000000000 942362f79218584a4c7289255c2ae9d55b3e293e JN <<EMAIL>> 1740317274 +0800	commit (initial): init commit
942362f79218584a4c7289255c2ae9d55b3e293e 5f423aed2041294839f77f954b0b1a5eb3deed57 JN <<EMAIL>> 1749175587 +0800	pull origin master: Fast-forward
5f423aed2041294839f77f954b0b1a5eb3deed57 a34eec6b8ec17b7e25d2edcf9853db1c900713cf JN <<EMAIL>> 1749194788 +0800	pull origin master: Fast-forward
a34eec6b8ec17b7e25d2edcf9853db1c900713cf 75ed560141b9a3a94b3fc4112dc16305f343e132 JN <<EMAIL>> 1749293845 +0800	commit: fixed get ip; fixed package build command;
75ed560141b9a3a94b3fc4112dc16305f343e132 00533129edbec156bb858f197ab5536bbc67ba5b JN <<EMAIL>> 1749465976 +0800	commit: added getLinkSinoSound api
00533129edbec156bb858f197ab5536bbc67ba5b eb9d7d3d77fde51d5c61427d20554e2d083b2d50 JN <<EMAIL>> 1749468743 +0800	commit: adjusted date format
eb9d7d3d77fde51d5c61427d20554e2d083b2d50 8224a1ce24d12662db3cf29bf7ba62fc47acfb7a JN <<EMAIL>> ********** +0800	commit: bugfix
8224a1ce24d12662db3cf29bf7ba62fc47acfb7a 0fca7fcbbba7c2879fcba5023a85780af5aa0b9b JN <<EMAIL>> ********** +0800	pull origin master: Merge made by the 'ort' strategy.
0fca7fcbbba7c2879fcba5023a85780af5aa0b9b 1f18da3410cf51c3e6419bc63347ff47b563efab JN <<EMAIL>> ********** +0800	commit: changed to live reg form webview
1f18da3410cf51c3e6419bc63347ff47b563efab bf47e8a988811bbd769ff057587c1cc2352b8c9e JN <<EMAIL>> ********** +0800	commit: fixed header for real account webview form
bf47e8a988811bbd769ff057587c1cc2352b8c9e b61762c8f6707de473a13884d63d9fbb8ed05a4a JN <<EMAIL>> ********** +0800	commit: fixed price reminder
b61762c8f6707de473a13884d63d9fbb8ed05a4a 0ad44ac4c351418d6f7f30588f98174bd2c31474 JN <<EMAIL>> ********** +0800	commit: fix demo account id
0ad44ac4c351418d6f7f30588f98174bd2c31474 a4cc0e29f48bca1bb467672ff38078c89af955bf JN <<EMAIL>> ********** +0800	commit: fixed language for live demo webview form
a4cc0e29f48bca1bb467672ff38078c89af955bf f7ca373ab681e5a2796e65ea795a292469a3bcdb JN <<EMAIL>> ********** +0800	commit: fixed issues
f7ca373ab681e5a2796e65ea795a292469a3bcdb ad7963171769ffae6a1c3a5d23e6d201e473af40 JN <<EMAIL>> ********** +0800	commit: fixed issues #2
ad7963171769ffae6a1c3a5d23e6d201e473af40 1f3a3bdcca3daa5d42c83a5d72c3bc47ff1acf3f JN <<EMAIL>> ********** +0800	commit: minor fix
1f3a3bdcca3daa5d42c83a5d72c3bc47ff1acf3f b3e23ebac4b196da162ffd2948b8636fa8c746dc JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
b3e23ebac4b196da162ffd2948b8636fa8c746dc 3f7ad59d4f8083a445b67d94b0d04e02edfb1db9 JN <<EMAIL>> ********** +0800	commit: added getClientLinkSinoSound
3f7ad59d4f8083a445b67d94b0d04e02edfb1db9 9b4ed03fa6676f9db30617b66cda9964898bde79 JN <<EMAIL>> ********** +0800	commit: minor fix
9b4ed03fa6676f9db30617b66cda9964898bde79 ff78ff951fb3f735674a409b5ec986395e89cf0c JN <<EMAIL>> 1749616373 +0800	pull origin master: Fast-forward
ff78ff951fb3f735674a409b5ec986395e89cf0c d725fb4ccd42b2d1c84b358b333511181484efa7 JN <<EMAIL>> 1749710621 +0800	pull origin master: Fast-forward
d725fb4ccd42b2d1c84b358b333511181484efa7 853c89c70cd2f41df7840b62b9b4899f85f4f5dd JN <<EMAIL>> 1749727342 +0800	commit: adjusted running text speed
853c89c70cd2f41df7840b62b9b4899f85f4f5dd b12d9eda55ec8ea0fe6d64eb307cb016302497dc JN <<EMAIL>> 1749733941 +0800	merge ip_switch: Fast-forward
b12d9eda55ec8ea0fe6d64eb307cb016302497dc dfaa4356438ceacd7567fff3e584be4afb288693 JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
dfaa4356438ceacd7567fff3e584be4afb288693 a5e0282599ebed4ace767023742ef7981ddfb915 JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
a5e0282599ebed4ace767023742ef7981ddfb915 d5c9bbb80b6875577d7022b5c7a1b4c85e87e2b3 JN <<EMAIL>> ********** +0800	commit: fixed english month translation
d5c9bbb80b6875577d7022b5c7a1b4c85e87e2b3 d5e62d04063427d3af6238486116dbecc6fb0367 JN <<EMAIL>> ********** +0800	commit: updated agent account balance
d5e62d04063427d3af6238486116dbecc6fb0367 ba3ae4ca13fe4ed8061fac2db85b736dd45505dd JN <<EMAIL>> ********** +0800	commit: fixed ip switching
ba3ae4ca13fe4ed8061fac2db85b736dd45505dd 10f081b32c4f1b6642943b112d5808e1c3faface JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
10f081b32c4f1b6642943b112d5808e1c3faface 91ecdc3c52af1a1073b9b2096e4e870b8429d632 JN <<EMAIL>> ********** +0800	commit: network error handling and creation time remove second
91ecdc3c52af1a1073b9b2096e4e870b8429d632 05d1887b61575e05a86eb80faa56fe7a2d62a0ff JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
05d1887b61575e05a86eb80faa56fe7a2d62a0ff a4537021c4c3d279dc8620f4c3ff3818a692569c JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
a4537021c4c3d279dc8620f4c3ff3818a692569c 322dd44cf3f5f0bc9c995cb9cd7bac95a62e8dff JN <<EMAIL>> ********** +0800	pull origin master: Fast-forward
322dd44cf3f5f0bc9c995cb9cd7bac95a62e8dff bc9ea969228c4487058f88a3b73ad95b93de129a JN <<EMAIL>> 1750078337 +0800	pull origin master: Fast-forward
bc9ea969228c4487058f88a3b73ad95b93de129a 132676e7c870564d6d0efcf8cc1dfc3df4fadeb3 JN <<EMAIL>> 1750218911 +0800	commit: fixed sino yarn android
132676e7c870564d6d0efcf8cc1dfc3df4fadeb3 963fa975c60afd807e78d1fe5adbb732b70d805b JN <<EMAIL>> 1750237577 +0800	commit: implementeed GetGraphLinkSinoSound
963fa975c60afd807e78d1fe5adbb732b70d805b da79063a5acad74e61b686317f4db6b1fff65836 JN <<EMAIL>> 1750247154 +0800	commit: implemented createDomainFailedLog
da79063a5acad74e61b686317f4db6b1fff65836 bbbc4861d8263f7317f1b4887e74faf3baa34161 JN <<EMAIL>> 1750248519 +0800	commit: updated ip to bypass non cn or hk region
bbbc4861d8263f7317f1b4887e74faf3baa34161 a5f8731052f137a7aac0cd412febec8597131bc1 JN <<EMAIL>> 1750249489 +0800	commit: fixed MobX warning
a5f8731052f137a7aac0cd412febec8597131bc1 4f9bdefee1239d36f256c2efa794224f4bd4282a JN <<EMAIL>> 1750250350 +0800	commit: fixed remaining mobx warning
4f9bdefee1239d36f256c2efa794224f4bd4282a 8a067f0430776aa0be317215c6c10b77665ff6c1 JN <<EMAIL>> 1750254444 +0800	pull origin master: Fast-forward
8a067f0430776aa0be317215c6c10b77665ff6c1 58778dc54e7916d1841b834c020da0e8071a36ae JN <<EMAIL>> 1750268096 +0800	pull origin master: Fast-forward
58778dc54e7916d1841b834c020da0e8071a36ae 32fb239f110c89b5c7278e9e0ead6f10fd19a8a1 JN <<EMAIL>> 1750308218 +0800	commit: bug fix
32fb239f110c89b5c7278e9e0ead6f10fd19a8a1 7802bb37ad10d250538eae4dbbaa3ecd9bfbfdbd JN <<EMAIL>> 1750403844 +0800	commit: fix show only 1 network error prompt everytime
7802bb37ad10d250538eae4dbbaa3ecd9bfbfdbd 88b4770403ba9ce29fa6062dd879d7cf92d0f55b JN <<EMAIL>> 1750403864 +0800	pull origin master: Merge made by the 'ort' strategy.
88b4770403ba9ce29fa6062dd879d7cf92d0f55b d6897fb1e54b887a1d4807b01f5566b46898d761 JN <<EMAIL>> 1750407934 +0800	commit: fixed trade screen webview error display while internet is off
d6897fb1e54b887a1d4807b01f5566b46898d761 7b36eeb5d01d898cd5ab8c18026dae3f8c798191 JN <<EMAIL>> 1750749000 +0800	commit: fixed calendar month text translation
7b36eeb5d01d898cd5ab8c18026dae3f8c798191 635e49e85e853cfd59f8187da183311b6062ad05 JN <<EMAIL>> 1750750143 +0800	commit: fixed language for english month
635e49e85e853cfd59f8187da183311b6062ad05 468748ffd0c35fea83ec5c135657209367c34803 JN <<EMAIL>> 1751018600 +0800	commit: minor issue fixed
468748ffd0c35fea83ec5c135657209367c34803 a970fa402b9762630a2eb0d8f88ee7768ef553fb JN <<EMAIL>> 1751348011 +0800	commit: fix month for all uppeercase
a970fa402b9762630a2eb0d8f88ee7768ef553fb 1c7c224bcbc118ca9f8c959057b0bb8cc9f2326e JN <<EMAIL>> 1751356120 +0800	commit: adjusted ip and calendar text
1c7c224bcbc118ca9f8c959057b0bb8cc9f2326e 04572645f28e0ec2261eef80f830e307085da522 JN <<EMAIL>> 1751382845 +0800	commit: price reminder min max fix
04572645f28e0ec2261eef80f830e307085da522 468748ffd0c35fea83ec5c135657209367c34803 JN <<EMAIL>> 1751436826 +0800	reset: moving to head~3
468748ffd0c35fea83ec5c135657209367c34803 04572645f28e0ec2261eef80f830e307085da522 JN <<EMAIL>> 1751436863 +0800	pull origin master: Fast-forward
04572645f28e0ec2261eef80f830e307085da522 a970fa402b9762630a2eb0d8f88ee7768ef553fb JN <<EMAIL>> 1751436883 +0800	reset: moving to head~2
a970fa402b9762630a2eb0d8f88ee7768ef553fb 04572645f28e0ec2261eef80f830e307085da522 JN <<EMAIL>> 1751436922 +0800	pull origin master: Fast-forward
04572645f28e0ec2261eef80f830e307085da522 1c7c224bcbc118ca9f8c959057b0bb8cc9f2326e JN <<EMAIL>> 1751436924 +0800	reset: moving to head~1
1c7c224bcbc118ca9f8c959057b0bb8cc9f2326e 04572645f28e0ec2261eef80f830e307085da522 JN <<EMAIL>> 1751436934 +0800	pull origin master: Fast-forward
04572645f28e0ec2261eef80f830e307085da522 a970fa402b9762630a2eb0d8f88ee7768ef553fb JN <<EMAIL>> 1751436941 +0800	reset: moving to head~2
a970fa402b9762630a2eb0d8f88ee7768ef553fb 04572645f28e0ec2261eef80f830e307085da522 JN <<EMAIL>> 1751437065 +0800	pull origin master: Fast-forward
04572645f28e0ec2261eef80f830e307085da522 d0fa67e25e248c3d2805de39205b9dd64e2d8cc7 JN <<EMAIL>> 1751437207 +0800	commit: critical bug fix
d0fa67e25e248c3d2805de39205b9dd64e2d8cc7 b43c19b8e5746a0be13b9c163ce3800d7383f9a2 JN <<EMAIL>> 1751440838 +0800	commit: adjusted calendar text form
b43c19b8e5746a0be13b9c163ce3800d7383f9a2 7b9a7b1214581d34647c0b8959261f152c92ffaa JN <<EMAIL>> 1751462996 +0800	commit: fix secure and service doman
7b9a7b1214581d34647c0b8959261f152c92ffaa 469af4f03afeace80ca438919f70ebdfd3069e47 JN <<EMAIL>> 1751465640 +0800	commit: integrate eddie's push change
469af4f03afeace80ca438919f70ebdfd3069e47 e827e6ceb69ab75034a08518038d801b18952213 JN <<EMAIL>> 1751466461 +0800	commit: refactored domain code and added service domain to wss
e827e6ceb69ab75034a08518038d801b18952213 ceb21ae73ab79dd21e3a5c23c55e65b4d06d6b36 JN <<EMAIL>> 1751517375 +0800	commit: fixed sending Domain Failed Log to be after gettiing a working service domain
ceb21ae73ab79dd21e3a5c23c55e65b4d06d6b36 853fcdcb147e663b2ad04cba52fb0477121625cc JN <<EMAIL>> 1752215426 +0800	commit: added web portal url check
853fcdcb147e663b2ad04cba52fb0477121625cc abac612b278a73165543955af4b1e3483c9b7ace JN <<EMAIL>> 1752239431 +0800	commit: refiined web portal url and added service domains to some api calls
abac612b278a73165543955af4b1e3483c9b7ace 4091ab2d205512c48a13bd2b466d41b042fe1ca7 JN <<EMAIL>> 1752242833 +0800	commit: renamed the apk on google drive for better readability
4091ab2d205512c48a13bd2b466d41b042fe1ca7 ee1ed9d7c49e5dbcd9dcd8ffd4d077d293485877 JN <<EMAIL>> 1752292947 +0800	commit: added domain parameter for web portal urls
ee1ed9d7c49e5dbcd9dcd8ffd4d077d293485877 520f7463e59d04c5e130334c53e66c719a9dab58 JN <<EMAIL>> 1752481608 +0800	commit: minor fix
520f7463e59d04c5e130334c53e66c719a9dab58 f0b6a30cb06857e995aed0a5253a4e437457caa1 JN <<EMAIL>> 1752644277 +0800	commit: hide loading when clicking back button
f0b6a30cb06857e995aed0a5253a4e437457caa1 e7e0d2b04c10aef239edfd18eff3898b971e3a76 JN <<EMAIL>> 1753440748 +0800	commit: bugfix
