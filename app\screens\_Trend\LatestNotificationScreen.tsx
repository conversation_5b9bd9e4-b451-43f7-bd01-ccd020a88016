import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { FlatList, View } from "react-native"
import { CardNews } from "../_Home/CardNews"
import { getAllPosts } from "app/api/model"
import { RefreshStorage } from "app/utils/RefreshStorage"
import { useIsFocused } from "@react-navigation/native"
import { Api } from "app/api/api"
import { HIDE_PRELOAD_CONTAINER, TEXT_CONTENT } from "app/theme/baseStyle"
import { useNetInfo } from "@react-native-community/netinfo"
import { AppStorage } from "app/utils/appStorage"
import { TopMenuIndex } from "app/navigators/TopMenu"

interface LatestNotificationScreenProps extends AppStackScreenProps<"LatestNotification"> {}

export const LatestNotificationScreen: FC<LatestNotificationScreenProps> = observer(function LatestNotificationScreen(_props) {
 
  const [posts, setPosts] = useState([])
  const [refreshing, setRefreshing] = useState(true)
  const currentPage = useRef(1)
  const hasNext = useRef(false)

  // right value = 5
  // test load more = 0
  const loadPosts = async (isLoadMore = false) => {
    if (!AppStorage.isNetworkConnected()) {
      setRefreshing(false)
      AppStorage.hidePreload(TopMenuIndex.latestNotification)
      return
    }
    if (isLoadMore && hasNext.current) {
      currentPage.current += 1
    } else {
      currentPage.current = 1
    }
    getAllPosts(
      4,
      currentPage.current,
      Api.postsPerPage,
      3,
      (data) => {
        AppStorage.hidePreload(TopMenuIndex.latestNotification)
        hasNext.current = data.hasNext
        setRefreshing(false)
        if (isLoadMore) {
          const tempList = posts
          data.results.map((item) => {
            tempList.push(item)
          })
          setPosts(JSON.parse(JSON.stringify(tempList)))
        } else {          
          setPosts(JSON.parse(JSON.stringify(data.results)))
        }        
      },
      (error) => {
        AppStorage.hidePreload(TopMenuIndex.latestNotification)
        setRefreshing(false)
        console.log("error: " + error)
        if (!isLoadMore) {
          setPosts(() => [])
        }
      }
    )
  }

  // start of count down to refresh feed
  // remember call reset timeout when user reload list
  const isScreenShown = useIsFocused()
  const timeoutRef = useRef(null)
  const resetTimeout = (delayTime = Api.reloadTime) => {
    console.log("resetTimeout: " + delayTime)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    RefreshStorage.updateScreenSetTime("LatestNotification")
    timeoutRef.current = setTimeout(() => {
      console.log("reload posts")
      setRefreshing(true)
      loadPosts()
      resetTimeout()
    }, delayTime*1000)
  }

  useEffect(() => {
    console.log("LatestNewsScreen isScreenShown: ", isScreenShown)
    if (isScreenShown) {
      if (!AppStorage.isLoadingShow()) {
        AppStorage.hidePreload(TopMenuIndex.marketNews)
      }
      if (RefreshStorage.screenNameAdded("LatestNotification")) {
        if (RefreshStorage.shouldRefreshScreen("LatestNotification")) {
          loadPosts()
          resetTimeout()
        } else {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          resetTimeout(RefreshStorage.getDiffSeconds("LatestNotification"))
        }
      } else {
        resetTimeout()
      }
    }
  }, [isScreenShown])
  // end of count down to refresh feed

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        loadPosts()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    AppStorage.showLoading()
    loadPosts()
  }, [])
  

  return (
    <Screen
      preset="fixed">
      {
        posts.length <= 0 && !refreshing &&
        <Text style={[TEXT_CONTENT, { textAlign: "center", marginTop: 20 }]} tx="errors.noRelatedData"/> 
      }
      {
        posts.length > 0 && 
        <FlatList 
          data={posts}
          onRefresh={() => {
            setRefreshing(true)
            loadPosts()
            resetTimeout()
          }}
          initialNumToRender={Api.postsPerPage}
          refreshing={refreshing}
          contentContainerStyle={{paddingBottom: 50}}
          keyExtractor={item => item.id.toString()}
          onEndReached={() => {        
            console.log("onEndReached " + hasNext.current)  
            if (hasNext.current) {
              setRefreshing(true)
              loadPosts(true)
            }          
          }}
          onEndReachedThreshold={0.5}
          ListFooterComponent={<View style={{ height: 100 }} />}  // Add a footer component        
          renderItem={({item}) => {
            return (
              <CardNews 
                key={item.id}
                onPress={() => {
                  if (!AppStorage.isNetworkConnected()) {
                    return
                  }
                  _props.navigation.navigate("NewsDetails", { postId: item.id })
                }}
                data={item} />
            )
          }}
        />
      }
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_CONTAINER}></View>}
    </Screen>
  )
})
