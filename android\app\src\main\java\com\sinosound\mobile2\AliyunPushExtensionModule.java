package com.sinosound.mobile2;

import android.util.Log;

import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.WritableMap;

public class AliyunPushExtensionModule extends ReactContextBaseJavaModule {

    private static WritableMap latestNotification;
    AliyunPushExtensionModule(ReactApplicationContext context) {
        super(context);
    }

    @Override
    public String getName() {
        return "AliyunPushExtensionModule";
    }

    public static void setLatestNotification(WritableMap map) {
        latestNotification = map.copy();
    }

    @ReactMethod
    public void getLatestNotification(Promise promise) {
        promise.resolve(latestNotification != null ? latestNotification.copy() : null);
    }

    @ReactMethod
    public void clearLatestNotification(Promise promise) {
        latestNotification = null;
        promise.resolve(null);
    }

}
