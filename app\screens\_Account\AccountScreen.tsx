import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { spacing } from "../../theme"
import { AppStackScreenProps } from "../../navigators"
import { BG_GRAY, BTN_CONTAINER, BTN_GRAY_CONTAINER, BTN_GREEN_CONTAINER, BTN_IN_CARD, COLOR_8B, CONTAINER_BORDER, FSIZE_13, FSIZE_15, FSIZE_22, FSIZE_24, HIDE_PRELOAD_CONTAINER, HIDE_PRELOAD_HAS_M_TOP_CONTAINER, SETTING_ROW, TEXT_LINK, TEXT_SCREEN_TITLE, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0, TEXT_VALUE_GREEN } from "app/theme/baseStyle"
import { AI_CENTER, BOLD, <PERSON>LUMN, <PERSON><PERSON>, <PERSON><PERSON>_<PERSON>ACE_BETWEEN, J<PERSON>_CENTER, MARGI<PERSON>_DF_BOTTOM, MARGIN_DF_LEFT, MARGIN_DF_TOP, MARGIN_L_TOP, MARGIN_S_DF_LEFT, MARGIN_S_DF_RIGHT, MARGIN_S_DF_TOP, PADDING_S_DF, ROW, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_ALERT, TEXT_COLOR_GREEN, TEXT_GRAY, W_100P, W_30P } from "app/theme/mStyle"
import { Switch, TouchableOpacity, View, ViewStyle } from "react-native"
import { MIcon } from "app/components/MIcon"
import { Dimen } from "app/theme/dimen"
import { getCsUrl, getUserWithoutBanner, subscribeSino, updateUserSettings } from "app/api/model"
import { AppStorage } from "app/utils/appStorage"
import { Helper } from "app/utils/helper"
import { translate } from "app/i18n"
import { Api, ApiService } from "app/api/api"
import { Dialog } from "react-native-simple-dialogs"

import DeviceInfo from 'react-native-device-info';
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

interface AccountScreenProps extends AppStackScreenProps<"Account"> { }

const ConfirmDaialogStyle: ViewStyle = {
  borderRadius: Dimen.borderRadiusLarge,
  backgroundColor: "white"

}

export const AccountScreen: FC<AccountScreenProps> = observer(function AccountScreen(_props) {

  // const [screenOn, setScreenOn] = useState(false)
  const [emailSubscription, setEmailSubscription] = useState(AppStorage.appStorage.setting.emailSubscription)
  const [userInfo, setUserInfo] = useState({} as any)
  const [userOA, setUserOA] = useState({} as any)
  const [demoAccountInfo, setDemoAccountInfo] = useState({} as any)
  const [clubPoint, setClubPoint] = useState(0)
  const [accountBalance, setAccountBalance] = useState(0)
  const [showSubscriptionConfirm, setShowSubscriptionConfirm] = useState(false)
  const [showDisplayModeBox, setShowDisplayModeBox] = useState(false)
  const [showClearCacheConfirm, setShowClearCacheConfirm] = useState(false)
  const [showDeleteAccountConfirm, setShowDeleteAccountConfirm] = useState(false)
  const [showBalanceValue, setShowBalanceValue] = useState(false)
  const [showLogoutSuccessDialog, setShowLogoutSuccessDialog] = useState(false)
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)

  const loadUserData = async () => {
    AppStorage.showLoading()
    console.log(AppStorage.appStorage.accessToken)
    getUserWithoutBanner(
      AppStorage.appStorage.userId,
      // 12,
      // 11,
      (data) => {
        AppStorage.hideLoading()
        console.log("user data:", JSON.stringify(data))
        AppStorage.setUserDevice(data.User)
        setUserInfo(data.User)
        setUserOA(data.OA)
        console.log("data.OA:", JSON.stringify(data.OA))
        console.log("userOA:", JSON.stringify(userOA))

        let isSubscribed = false
        if (Helper.isLiveAccount(data.User)) {
          if (data?.OA?.data) {
            setClubPoint(data?.OA?.data.club_score)
            setAccountBalance(data?.OA?.data.balance)
          }
          AppStorage.setUserOtherInfo(data?.OA)
          if (data?.OA) {
            if (data?.OA?.data && data?.OA?.data.subscribe_state == 1) {
              isSubscribed = true
            } else {
              isSubscribed = false
            }
          }
        }
        if (Helper.isDemoUser(data.User)) {
          setDemoAccountInfo(data.Demo)
          AppStorage.setUserOtherInfo(data?.Demo)
        }
        AppStorage.updateSetting({
          screenOn: AppStorage.appStorage.setting.screenOn,
          emailSubscription: isSubscribed,
          displayMode: Helper.capitalizeFirstLetter(data.User?.settings.redGreen),
          language: data.User?.settings.language,
          clearCache: AppStorage.appStorage.setting.clearCache,
        })
        setEmailSubscription(isSubscribed)
      },
      (error) => {
        AppStorage.hideLoading()
        ApiService.showMessageBox(error)
        console.log("error:", error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const logout = (callback) => {
    AppStorage.reset(() => {
      AppStorage.switchUserById(0, () => {
        callback()
      })
    })

  }

  const getActionButtonText = () => {
    if (Helper.isPortalUser(userInfo)) {
      return translate("homePage.applyLiveAccount")
    }
    if (Helper.isLiveAccount(userInfo)) {
      return translate("account.eject")
    }
    if (Helper.isDemoUser(userInfo)) {
      return translate("account.applyRealTradingAccount")
    }
    return translate("account.applyForDemoAccount")
  }

  const doSubscription = (isChoose) => {
    if (isChoose) {

      setShowSubscriptionConfirm(false)
      AppStorage.showLoading()
      let clientId = AppStorage.appStorage.userDevice.clientId
      if (clientId == null) {
        clientId = AppStorage.appStorage.userDevice.demoClientId
      }
      subscribeSino(
        !emailSubscription,
        clientId,
        (response) => {
          AppStorage.hideLoading()
          console.log("subscribeSino success", JSON.stringify(response))
          setEmailSubscription(!emailSubscription)
          AppStorage.updateEmailSubscription(!emailSubscription)
        },
        (error) => {
          AppStorage.hideLoading()
          ApiService.showMessageBox(error)
          console.log("subscribeSino error", JSON.stringify(error))
        },
        () => {
          _props.navigation.navigate("Logout")
        }
      )
    } else {
      setShowSubscriptionConfirm(false)
    }
  }

  const switchDisplayMode = () => {
    setShowDisplayModeBox(false)
    AppStorage.showLoading()
    updateUserSettings(
      { redGreen: Helper.isDisplayModeRed() ? Api.displayModes.green : Api.displayModes.red },
      AppStorage.appStorage.userId,
      (response) => {
        AppStorage.hideLoading()
        console.log("updateUserSettings success", JSON.stringify(response))
        AppStorage.updateDisplayMode(Helper.isDisplayModeRed() ? Api.displayModes.green : Api.displayModes.red)
      },
      (error) => {
        AppStorage.hideLoading()
        ApiService.showMessageBox(error)
        console.log("updateUserSettings error", JSON.stringify(error))
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const loadCsUrl = () => {
    Helper.loadCsUrlAndGo(_props.navigation)
  }

  const resetToHomePage = () => {
    setShowLogoutSuccessDialog(false)
    setTimeout(() => {
      _props.navigation.reset({
        index: 0,
        routes: [{ name: "BottomTab" }],
      })
    }, 1000)
  }

  useEffect(() => {
    AppStorage.hideLoading()
    // setScreenOn(AppStorage.appStorage.setting.screenOn)
    setEmailSubscription(AppStorage.appStorage.setting.emailSubscription)
    loadUserData()
    const reloadBalance = setInterval(() => {
      if (Helper.isLiveAccount()) {
        console.log("reload balance")
        loadUserData()
      }
    }, Api.reloadBalance * 1000)
    return () => clearInterval(reloadBalance)
  }, [])

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {
      if (netInfo.isConnected) {
        loadUserData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    console.log("language changed")
    loadUserData()
  }, [AppStorage.appStorage.setting.language])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: spacing.md }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }}
        hasPadding={false} />
      <View style={[W_100P, ROW_CENTER, MARGIN_DF_TOP]}>
        <Text
          style={[
            TEXT_SCREEN_TITLE,
            FSIZE_24,
            { maxWidth: '85%' },
          ]}
          numberOfLines={1}
          text={userInfo?.name}
        />
        <View style={FLEX} />
        {!Helper.isPortalUser(userInfo) && <Text style={Helper.isLiveAccount(userInfo) ? BTN_IN_CARD : BTN_GRAY_CONTAINER} text={Helper.getUserTypeName(userInfo?.userType)} />}
      </View>
      <View style={[W_100P, ROW_CENTER, MARGIN_S_DF_TOP]}>
        <Text style={[FSIZE_15, COLOR_8B]} text={Helper.getUserFullPhone(userInfo)} />
        <View style={FLEX} />
        {/* {Helper.isDemoUser(userInfo) && <Text style={[FSIZE_15, COLOR_8B]} text={translate("account.status") + ": " + Helper.getAccountStatus(demoAccountInfo)} />} */}
      </View>
      {Helper.isLiveAccount(userInfo) && <View style={[ROW_CENTER, W_100P, MARGIN_DF_TOP]}>
        {clubPoint > 0 &&
          <View style={[CONTAINER_BORDER, FLEX, PADDING_S_DF, COLUMN, { paddingHorizontal: 9 }]}>
            <Text style={FSIZE_13} tx="account.clubPoint" />
            <Text style={[TEXT_VALUE_GREEN, MARGIN_DF_TOP, MARGIN_DF_BOTTOM, TEXT_COLOR_GREEN, FSIZE_22]} text={clubPoint + ""} />
          </View>
        }
        {
          clubPoint > 0 && <View style={{ width: 16 }} ></View>
        }
        <View style={[CONTAINER_BORDER, FLEX, PADDING_S_DF, { paddingHorizontal: 9 }]}>
          <TouchableOpacity style={ROW_CENTER}
            onPress={() => {
              //_props.navigation.navigate("TransactionHistory")
              Helper.loadWebViewLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice.clientId,
                'transaction'
              )
            }}
          >
            <Text style={[FSIZE_13, FLEX]} tx="account.accountBalance" />
            <MIcon name="next" size={Dimen.iconSize.sm} />
          </TouchableOpacity>
          <View style={[MARGIN_DF_TOP, MARGIN_DF_BOTTOM, ROW]}>
            <Text style={[TEXT_VALUE_GREEN, TEXT_COLOR_GREEN, FSIZE_22]} text={"$"} />
            {showBalanceValue && <Text style={[TEXT_VALUE_GREEN, TEXT_COLOR_GREEN, FSIZE_22]} text={accountBalance + ""} />}
            {!showBalanceValue && <Text style={[TEXT_VALUE_GREEN, TEXT_COLOR_GREEN, FSIZE_22]} text={"* * * * * *"} />}
            <MIcon
              name={showBalanceValue ? "eyeOff" : "eyeOn"}
              size={17}
              style={MARGIN_S_DF_LEFT}
              onPress={() => setShowBalanceValue(!showBalanceValue)} />
          </View>
        </View>
      </View>}
      <View style={[W_100P, ROW_CENTER, MARGIN_DF_TOP, MARGIN_DF_BOTTOM]}>
        <Text
          onPress={() => {
            if (!AppStorage.isNetworkConnected()) {
              return
            }
            console.log("upgrade: " + JSON.stringify(AppStorage.appStorage.storedUsers))
            console.log("id: " + AppStorage.appStorage.userId)
            if (Helper.isPortalUser(userInfo) || Helper.isDemoUser(userInfo)) {
              console.log("userInfo: " + JSON.stringify(userInfo))
              //_props.navigation.navigate("RealAccountRegistration")
              Helper.loadRegLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice?.areaCode || "",
                AppStorage.appStorage.userDevice?.mobile || ""
              )
            } else if (Helper.isLiveAccount(userInfo)) {
              //Helper.loadCsUrlAndGo(_props.navigation)
              Helper.loadWebViewLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice.clientId,
                'deposit'
              )
            }
          }}
          style={[FLEX, BTN_CONTAINER]}
          text={getActionButtonText()} />
        {Helper.isLiveAccount(userInfo) &&
          <Text
            onPress={() => {
              if (!AppStorage.isNetworkConnected()) {
                return
              }
              Helper.loadWebViewLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice.clientId,
                'withdrawal'
              )
            }}
            style={[FLEX, BTN_CONTAINER]}
            text={translate("account.withdraw")} />
        }
      </View>
      {Helper.isPortalUser(userInfo) &&
        <Text
          onPress={() => {
            _props.navigation.navigate("DemoAccountRegistration")
          }}
          style={[TEXT_SMALL, BOLD, TEXT_LINK, TEXT_CENTER]}
          tx="account.applyForDemoAccount" />}
      {(Helper.isDemoUser(userInfo) || Helper.isLiveAccount(userInfo)) && <View>
        <Text style={[TEXT_SECTION, MARGIN_L_TOP]} tx="account.acountInfo" />
        <TouchableOpacity
          onPress={() => {
            //_props.navigation.navigate("PersonalInformation")
            Helper.loadClientLinkSinoSound(
              _props.navigation,
              AppStorage.appStorage.userDevice?.areaCode || "",
              AppStorage.appStorage.userDevice?.mobile || ""
            )
          }}
          style={SETTING_ROW}>
          <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.personalInfo" />
          <View style={FLEX} />
          <MIcon name="next" size={Dimen.iconSize.sm} />
        </TouchableOpacity>
      </View>}
      {
        Helper.isLiveAccount(userInfo) && <View>
          {/* <TouchableOpacity
            onPress={() => {
              _props.navigation.navigate("BankInformation")
            }}
            style={SETTING_ROW}>
            <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.bankInfo" />
            <View style={FLEX} />
            <MIcon name="next" size={Dimen.iconSize.sm} />
          </TouchableOpacity> */}
          {userOA?.data?.ib &&
            <TouchableOpacity
              onPress={() => {
                Helper.loadWebViewLinkSinoSound(
                  _props.navigation,
                  AppStorage.appStorage.userDevice.clientId,
                  'ib'
                )
              }}
              style={SETTING_ROW}>
              <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.agentCenter" />
              <View style={FLEX} />
              {userOA?.data?.ib_balance &&
                <>
                  <Text
                    style={[TEXT_SMALL, COLOR_8B, MARGIN_S_DF_RIGHT]}
                    text={translate("account.ib_balance") + ":  $" + userOA?.data?.ib_balance} />
                  <MIcon name="next" size={Dimen.iconSize.sm} />
                </>
              }
            </TouchableOpacity>
          }
          <TouchableOpacity
            onPress={() => {
              Helper.loadWebViewLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice.clientId,
                'deposit'
              )
            }}
            style={SETTING_ROW}>
            <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.fundAcount" />
            <View style={FLEX} />
            <MIcon name="next" size={Dimen.iconSize.sm} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              Helper.loadWebViewLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice.clientId,
                'transaction'
              )
            }}
            style={SETTING_ROW}>
            <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.transactionHistory" />
            <View style={FLEX} />
            <MIcon name="next" size={Dimen.iconSize.sm} />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              _props.navigation.navigate("ExchangeRate")
            }}
            style={SETTING_ROW}>
            <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.todayExchangeRate" />
            <View style={FLEX} />
            <MIcon name="next" size={Dimen.iconSize.sm} />
          </TouchableOpacity>
        </View>
      }
      <Text style={[TEXT_SECTION, MARGIN_L_TOP]} tx="boarding.setting" />
      <View style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.screenAlwaysOn" />
        <View style={FLEX} />
        <Text style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]} text={AppStorage.appStorage.setting.screenOn ? translate("boarding.on") : translate("boarding.off")} />
        <Switch
          value={AppStorage.appStorage.setting.screenOn}
          onValueChange={(value) => {
            // setScreenOn(value)
            AppStorage.updateScreenOn(value)
          }}
        />
      </View>
      {Helper.isLiveAccount() && <View style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.emailSubscription" />
        <View style={FLEX} />
        <Text style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]} text={emailSubscription ? translate("boarding.on2") : translate("boarding.off")} />
        <Switch
          value={emailSubscription}
          onValueChange={(value) => {
            if (!AppStorage.isNetworkConnected()) {
              return
            }
            ApiService.showMessageBoxTwoOption(
              emailSubscription
                ? translate("account.unsubscribeConfirm")
                : translate("account.subscriptionConfirm"),
              () => {
                doSubscription(true)
              },
              () => {
                // Do nothing on cancel
              }
            )
          }}
        />
      </View>}
      <TouchableOpacity
        onPress={() => {
          setShowDisplayModeBox(true)
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.displaySetting" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT,
            !Helper.isDisplayModeRed() ? TEXT_COLOR_GREEN : TEXT_COLOR_ALERT]}
          text={!Helper.isDisplayModeRed() ? translate("boarding.displayModeGreen") : translate("boarding.displayModeRed")} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          _props.navigation.navigate("ChooseLanguage")
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.language" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]}
          text={AppStorage.getLanguageName()} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setShowClearCacheConfirm(true)
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.clearCache" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]} tx="boarding.clear" />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <Text style={[TEXT_SECTION, MARGIN_L_TOP]} tx="boarding.aboutHan" />
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("WebviewInformation", {
            title: translate("boarding.companyProfile"),
            webUrl: Helper.getCompanyProfileUrl()
          })
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.companyProfile" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          _props.navigation.navigate("MajorEvent")
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.majorEventOfHan" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("WebviewInformation", {
            title: translate("boarding.hansJournal"),
            webUrl: Helper.getJournalUrl()
          })
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.hansJournal" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          // _props.navigation.navigate("Logout")
          loadCsUrl()
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.contactCustomerService" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={() => {
          setShowDeleteAccountConfirm(true)
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="account.deleteAccount" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <Text
        onPress={() => {
          // logout(() => {
          //   setShowLogoutSuccessDialog(true)
          // })
          setShowLogoutConfirm(true)
        }}
        style={[BTN_GRAY_CONTAINER, MARGIN_L_TOP, { marginHorizontal: 0, paddingVertical: Dimen.padding.sm }]}
        tx="common.logOut" />
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_HAS_M_TOP_CONTAINER}></View>}
      <Dialog
        visible={showLogoutSuccessDialog}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => resetToHomePage()}>
        <View style={[PADDING_S_DF, COLUMN]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("account.logoutSuccess")} />
          <View style={[ROW_CENTER, JC_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                resetToHomePage()
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, W_30P]}
              tx="common.ok" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showLogoutConfirm}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => setShowLogoutConfirm(false)}>
        <View style={[PADDING_S_DF, COLUMN]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("account.confirmLogout")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowLogoutConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={() => {
                setShowLogoutConfirm(false)
                logout(() => {
                  setTimeout(() => {
                    setShowLogoutSuccessDialog(true)
                  }, 1000)
                })
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showSubscriptionConfirm}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => setShowSubscriptionConfirm(false)}>
        <View style={[PADDING_S_DF, COLUMN]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={emailSubscription ? translate("account.unsubscribeConfirm") : translate("account.subscriptionConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                if (!AppStorage.isNetworkConnected()) {
                  return
                }
                doSubscription(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={() => {
                if (!AppStorage.isNetworkConnected()) {
                  return
                }
                doSubscription(true)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showDisplayModeBox}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => setShowDisplayModeBox(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <MIcon name={Helper.isDisplayModeRed() ? "displayGreen" : "displayRed"} size={60} />
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={Helper.isDisplayModeRed() ? translate("account.displayGreenConfirm") : translate("account.displayRedConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowDisplayModeBox(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={async () => {
                switchDisplayMode()
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showClearCacheConfirm}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => setShowClearCacheConfirm(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("account.clearCacheConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowClearCacheConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={() => {
                setShowClearCacheConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showDeleteAccountConfirm}
        dialogStyle={ConfirmDaialogStyle}
        onTouchOutside={() => setShowDeleteAccountConfirm(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("account.deleteAccountConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowDeleteAccountConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={() => {
                setShowDeleteAccountConfirm(false)
                _props.navigation.navigate("DeleteAccountConfirm")
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.delete" />
          </View>
        </View>
      </Dialog>
      <View style={[{ marginTop: 5, height: 70 }]}>
        <Text style={[TEXT_CENTER, TEXT_GRAY]} text={'version: ' + DeviceInfo.getVersion()} />
      </View>
    </Screen>
  )
})