import React from "react"
import {
  Platform,
  View,
} from "react-native"
import { colors } from "../../theme"
import { Text } from ".."
import { translate } from "../../i18n"
import {
  MARGIN_S_DF_TOP,
  TEXT_CENTER,
} from "../../theme/mStyle"
import { Helper } from "../../utils/helper"
import { Dimen } from "app/theme/dimen"

export default (function ({ itemType, price, condition, type }) {

  let itemType2 = 'londonGold';
  let underlineColor = "#c5af73";
  if (itemType == "SILVER") {
    underlineColor = "#9e9e9e";
    itemType2 = 'londonSilver';
  } else if (itemType == "USDINDEX") {
    underlineColor = "#4876e2";
    itemType2 = 'dollarIndex';
  }

  return (
    <>
      <Text
        style={[TEXT_CENTER, { marginTop: 30, fontSize: Dimen.fontSize.md }]}
        text={translate("priceModal." + itemType2)}
      />
      <View
        style={{
          height: 4,
          width: 30,
          backgroundColor: underlineColor,
          marginTop: 5,
          marginBottom: 15,
          borderRadius: 2,
        }}
      />
      <View
        style={{
          alignSelf: "stretch",
          backgroundColor: condition === 'rise' ? (!Helper.isDisplayModeRed() ? colors.mine.priceReminderUp : colors.mine.priceReminderDown) : (Helper.isDisplayModeRed() ? colors.mine.priceReminderUp : colors.mine.priceReminderDown),
          borderRadius: 10,
          padding: 15,
        }}
      >
        <View style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}>
          <Text
            style={[TEXT_CENTER, MARGIN_S_DF_TOP, { fontSize: Platform.OS === "ios" ? 15 : 14 }]}
            text={translate(type == 1 ? "priceModal.priceReach" : "priceModal.priceUpDown")}
          />
          <View style={{
            width: 5,
            height: 5,
            borderRadius: 5,
            marginHorizontal: 10,
            backgroundColor: colors.palette.black,
            marginTop: 8,
          }}
          />
          <Text
            style={[TEXT_CENTER, MARGIN_S_DF_TOP, { fontSize: Platform.OS === "ios" ? 15 : 14 }]}
            text={translate(condition === 'rise' ? "priceModal.riseToInNew" : "priceModal.dropTo")}
          />
        </View>
        <Text
          style={[TEXT_CENTER, MARGIN_S_DF_TOP,
            {
              color: condition === 'rise' ? (Helper.isDisplayModeRed() ? colors.mine.red : colors.mine.green) : (Helper.isDisplayModeRed() ? colors.mine.green : colors.mine.red),
              fontSize: Dimen.fontSize.md,
              fontWeight: 'bold',
            }]}
          text={price}
        />
      </View>
    </>
  );
});
