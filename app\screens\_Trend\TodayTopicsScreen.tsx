import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { FlatList, View } from "react-native"
import { CardNews } from "../_Home/CardNews"
import { getAllPosts } from "app/api/model"
import { useIsFocused } from "@react-navigation/native"
import { RefreshStorage } from "app/utils/RefreshStorage"
import { Api } from "app/api/api"
import { HIDE_PRELOAD_CONTAINER, TEXT_CONTENT } from "app/theme/baseStyle"
import { useNetInfo } from "@react-native-community/netinfo"
import { AppStorage } from "app/utils/appStorage"
import { TopMenuIndex } from "app/navigators/TopMenu"

interface TodayTopicsScreenProps extends AppStackScreenProps<"TodayTopics"> {}

export const TodayTopicsScreen: FC<TodayTopicsScreenProps> = observer(function TodayTopicsScreen(_props) {
  
  const [posts, setPosts] = useState([])
  const [refreshing, setRefreshing] = useState(true)
  const currentPage = useRef(1)
  const hasNext = useRef(false)

  // right value = 5
  // test load more = 0
  const loadPosts = async (isLoadMore = false) => {
    if (!AppStorage.isNetworkConnected()) {
      setRefreshing(false)
      AppStorage.hidePreload(TopMenuIndex.todayTopics)
      return
    }
    if (isLoadMore && hasNext.current) {
      currentPage.current += 1
    } else {
      currentPage.current = 1
    }
    getAllPosts(
      5,
      currentPage.current,
      Api.postsPerPage,
      3,
      (data) => {
        AppStorage.hidePreload(TopMenuIndex.todayTopics)
        setRefreshing(false)
        hasNext.current = data.hasNext
        if (isLoadMore) {
          const tempList = posts
          data.results.forEach((item) => {
            tempList.push(item)
          })
          setPosts(JSON.parse(JSON.stringify(tempList)))
        } else {          
          setPosts(() => data.results)
        }        
      },
      (error) => {
        AppStorage.hidePreload(TopMenuIndex.todayTopics)
        setRefreshing(false)
        console.log("error: " + error)
        if (!isLoadMore) {
          setPosts(() => [])
        }
      }
    )
  }

  // start of count down to refresh feed
  // remember call reset timeout when user reload list
  const isScreenShown = useIsFocused()
  const timeoutRef = useRef(null)
  const resetTimeout = (delayTime = Api.reloadTime) => {
    console.log("resetTimeout: " + delayTime)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    RefreshStorage.updateScreenSetTime("TodayTopics")
    timeoutRef.current = setTimeout(() => {
      console.log("reload posts")
      setRefreshing(true)
      loadPosts()
      resetTimeout()
    }, delayTime*1000)
  }

  useEffect(() => {
    console.log("TodayTopicsScreen isScreenShown: ", isScreenShown)
    if (isScreenShown) {
      if (RefreshStorage.screenNameAdded("TodayTopics")) {
        if (RefreshStorage.shouldRefreshScreen("TodayTopics")) {
          loadPosts()
          resetTimeout()
        } else {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          resetTimeout(RefreshStorage.getDiffSeconds("TodayTopics"))
        }
      } else {
        resetTimeout()
      }
    }
  }, [isScreenShown])
  // end of count down to refresh feed

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        loadPosts()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    AppStorage.showLoading()
    loadPosts()
  }, [])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{flex: 1}}>
      {
        posts.length <= 0 && !refreshing &&
        <Text style={[TEXT_CONTENT, { textAlign: "center", marginTop: 20 }]} tx="errors.noRelatedData"/> 
      }
      {posts.length > 0 &&  <FlatList         
        data={posts}
        onRefresh={() => {
          setRefreshing(true)
          loadPosts()
          resetTimeout()
        }}
        initialNumToRender={Api.postsPerPage}
        refreshing={refreshing}
        contentContainerStyle={{paddingBottom: 100}}
        keyExtractor={item => item.id.toString()}
        onEndReached={() => {           
          console.log("onEndReached today topics " + hasNext.current)         
          if (hasNext.current) {
            setRefreshing(true)
            loadPosts(true)
          }          
        }}
        onEndReachedThreshold={0.5}
        ListFooterComponent={<View style={{ height: 100 }} />}  // Add a footer component        
        renderItem={({item}) => {
          return (
            <CardNews 
              key={item.id}
              onPress={() => {
                if (!AppStorage.isNetworkConnected()) {
                  return
                }
                _props.navigation.navigate("NewsDetails", { postId: item.id })
              }}
              data={item} />
          )
        }}
      />}
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_CONTAINER}></View>}
    </Screen>
  )
})
