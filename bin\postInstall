#!/usr/bin/env node
/**
 * Do all things that need to be done after installing packages (with yarn, npm, pnpm).
 *
 * Yes, it slows down package installation a little, but it's nice to not
 * have to remember these extra steps.
 */

// Patch any packages that need patching
run("npx patch-package")

// Kill the metro bundler if it's running.
if (["darwin", "linux"].includes(process.platform)) {
  run('pkill -f "cli.js start" || set exit 0')
}

/**
 * On darwin machine:
 *   1. Check if CocoaPods CLI is installed (and if not, install it)
 *   2. Find an Xcode project dir
 *   3. Run `pod install`, if fails run `pod repo update` and try again
 */
run('npx pod-install')

// Run baby run
function run(command) {
  console.log(`./bin/postInstall script running: ${command}`)

  try {
    require("child_process").execSync(command, { stdio: "inherit" })
  } catch (error) {
    console.error(`./bin/postInstall failed on command:\n  ${command}`)
    process.exit(error.status)
  }
}
