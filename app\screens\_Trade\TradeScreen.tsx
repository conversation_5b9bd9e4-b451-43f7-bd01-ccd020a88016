import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { colors } from "../../theme"
import { AppStackScreenProps } from "../../navigators"
import { Mheader } from "../../navigators/Mheader"
import { translate } from "../../i18n"
import { FlatList, ScrollView, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { CardPrices } from "../_Home/CardPrices"
import { AI_CENTER, COLUMN, FLEX, JC_END, MARGIN_DF_TOP, MARGIN_S_DF_RIGHT, MARGIN_S_DF_TOP, PADDING_S_DF, ROW, ROW_CENTER, TEXT_CENTER, TEXT_GRAY, W_100P } from "../../theme/mStyle"
import { Dimen } from "../../theme/dimen"
import WebView from "react-native-webview"
import { BTN_CONTAINER, CONTAINER_BORDER, GREY_BORDER_B, TEXT_H4, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { MIcon } from "../../components/MIcon"
import { Dialog } from 'react-native-simple-dialogs';
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import moment from "moment"
import { getPriceReminders } from "app/api/model"
import { Api, ApiService } from "app/api/api"
import { CardPriceReminder } from "./CardPriceReminder"
import { BtnContactComp } from "../_Home/BtnContactComp"
import { useIsFocused } from "@react-navigation/native"
import { useNetInfo } from "@react-native-community/netinfo"

interface TradeScreenProps extends AppStackScreenProps<"Trade"> { }

const WebviewStyle: ViewStyle = {
  width: Dimen.screenWidth - 2 * Dimen.padding.base,
  marginHorizontal: Dimen.padding.base,
  marginTop: 10
}

const PriceReminderContainer: ViewStyle = {
  width: Dimen.screenWidth,
  paddingHorizontal: Dimen.padding.base,
  paddingTop: Dimen.padding.base,
  paddingBottom: Dimen.padding.xl,
  flex: 1,
  backgroundColor: colors.mine.bgGrey,
  ...COLUMN,
}

const AddPriceReminderContainer: ViewStyle = {
  ...CONTAINER_BORDER,
  ...ROW,
  width: Dimen.screenWidth - 2 * Dimen.padding.base,
  height: Dimen.screenWidth * 0.25,
  marginVertical: Dimen.padding.base,
  backgroundColor: colors.palette.white,
  justifyContent: "center",
  alignItems: "center",
}

const btnActionText: TextStyle = {
  color: colors.palette.white,
  fontSize: Dimen.fontSize.sm,
  textAlign: "center",
  textAlignVertical: "center",
}

const BtnCreateRealAcc: TextStyle = {
  flex: 1,
  paddingVertical: Dimen.padding.sm,
  paddingHorizontal: Dimen.padding.ssm,
  backgroundColor: colors.mine.primary,
  borderRadius: 6,
  marginEnd: Dimen.padding.ssm,
  overflow: "hidden"
}

const BtnCreateDemoAcc: TextStyle = {
  flex: 1,
  paddingVertical: Dimen.padding.sm,
  paddingHorizontal: Dimen.padding.ssm,
  backgroundColor: colors.mine.textGray,
  borderRadius: 6,
  marginStart: Dimen.padding.ssm,
  overflow: "hidden"
}

export const TradeScreen: FC<TradeScreenProps> = observer(function TradeScreen(_props) {

  // const [webviewHeight, setWebviewHeight] = useState(2 * Dimen.screenWidth / 3);
  const [webviewHeight, setWebviewHeight] = useState(500);
  const [showDialog, setShowDialog] = useState(false)
  const [showDialogReminderExpired, setShowDialogReminderExpired] = useState(false)
  const [showDialogLoginNeeded, setShowDialogLoginNeeded] = useState(false)
  const [reminders, setReminders] = useState([])
  const isScreenFocused = useIsFocused()
  const priceReminderInfo = [
    translate("tradePage.priceReminderInfoContent1"),
    translate("tradePage.priceReminderInfoContent2"),
    translate("tradePage.priceReminderInfoContent3"),
    translate("tradePage.priceReminderInfoContent4"),
    translate("tradePage.priceReminderInfoContent5"),
    translate("tradePage.priceReminderInfoContent6"),
  ]

  const webViewScript = `
    setTimeout(function() { 
      window.ReactNativeWebView.postMessage(document.documentElement.scrollHeight); 
    }, 1000);
    true; // note: this is required, or you'll sometimes get silent failures
  `;

  const getPriceReminderText = () => {
    if (Helper.isPortalUser(AppStorage.appStorage.userDevice)) {
      return "- " + translate("tradePage.timeDemoExpire") + " - " + AppStorage.appStorage.userDevice.priceReminderExpiryDate
    }
    if (Helper.isDemoUser(AppStorage.appStorage.userDevice)) {
      return "- " + translate("tradePage.timeDemoExpire") + " - " + AppStorage.appStorage.userDevice.priceReminderExpiryDate
    }

    if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
      return ""
    }

    return "- " + translate("tradePage.becomeMember") + " -"
  }

  const getLeftButtonText = () => {
    if (Helper.isPortalUser(AppStorage.appStorage.userDevice)) {
      return translate("tradePage.createRealAccount")
    }
    if (Helper.isDemoUser(AppStorage.appStorage.userDevice)) {
      return translate("tradePage.upgradeToRealAccount")
    }
    if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
      return translate("account.eject")
    }
    return translate("tradePage.createRealAccount")
  }

  const leftButtonClicked = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (Helper.isPortalUser(AppStorage.appStorage.userDevice) || Helper.isDemoUser(AppStorage.appStorage.userDevice)) {
      //_props.navigation.navigate("RealAccountRegistration")
      Helper.loadRegLinkSinoSound(
        _props.navigation,
        AppStorage.appStorage.userDevice?.areaCode || "",
        AppStorage.appStorage.userDevice?.mobile || ""
      )
    } else if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
      //Helper.loadCsUrlAndGo(_props.navigation)
      Helper.loadWebViewLinkSinoSound(
        _props.navigation,
        AppStorage.appStorage.userDevice.clientId,
        'deposit'
      )
    } else {
      setShowDialogLoginNeeded(true)
    }
  }

  const rightButtonClicked = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (Helper.isPortalUser(AppStorage.appStorage.userDevice)) {
      _props.navigation.navigate("DemoAccountRegistration")
    } else if (Helper.isDemoUser(AppStorage.appStorage.userDevice) || Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
      Helper.openLink(Api.mt4Link)
    } else {
      setShowDialogLoginNeeded(true)
    }
  }

  const getRightButtonText = () => {
    if (Helper.isPortalUser(AppStorage.appStorage.userDevice)) {
      return translate("tradePage.createDemoAccount")
    }
    if (Helper.isDemoUser(AppStorage.appStorage.userDevice)) {
      return translate("tradePage.goToMT4")
    }
    if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
      return translate("tradePage.goToMT4")
    }
    return translate("tradePage.createDemoAccount")
  }

  const addPriceReminder = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (Helper.isLoggeIn()) {
      if (Helper.isLiveAccount() || moment(AppStorage.appStorage.userDevice.priceReminderExpiryDate).isAfter(moment())) {
        _props.navigation.navigate(
          "NewPriceReminder",
          {
            onGoBack: () => {
              console.log("onGoBack")
              loadPriceReminderList("active")
            }
          }
        )
      } else {
        setShowDialogReminderExpired(true)
      }
    } else {
      setShowDialogLoginNeeded(true)
    }
  }

  const loadPriceReminderList = (status) => {
    AppStorage.showLoading()
    getPriceReminders(
      status,
      (response) => {
        AppStorage.hideLoading()
        console.log("loadPriceReminderList: ", response)
        const reminderList = []
        response.results.map((item) => {
          if (!Helper.timeInPast(item.expiryDate)) {
            reminderList.push(item)
          }
          // reminderList.push(item)
        })
        setReminders(JSON.parse(JSON.stringify(reminderList)))
      },
      (error) => {
        AppStorage.hideLoading()
        ApiService.showShortToast(error)
        console.log("loadPriceReminderList error", error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  useEffect(() => {
    if (isScreenFocused) {
      if (AppStorage.appStorage.userDevice && AppStorage.appStorage.accessToken) {
        loadPriceReminderList("active")
      }
    }
  }, [isScreenFocused])

  // useEffect(() => {
  //   if (AppStorage.appStorage.userDevice && AppStorage.appStorage.accessToken) {
  //     loadPriceReminderList("active")
  //   }
  // }, [])

  const netInfo = useNetInfo()
  const webviewRef = useRef(null)
  
  // Only load price reminders when screen is focused
  useEffect(() => {
    if (isScreenFocused && netInfo.isConnected) {
      if (AppStorage.appStorage.userDevice && AppStorage.appStorage.accessToken) {
        loadPriceReminderList("active")
      }
    }
  }, [isScreenFocused, netInfo.isConnected])

  useEffect(() => {
    if (AppStorage.appStorage.userDevice && AppStorage.appStorage.accessToken) {
      loadPriceReminderList("active")
    }
  }, [AppStorage.appStorage.userId])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ paddingBottom: 10 }}
      safeAreaEdges={["top", "bottom"]}
    >
      <Mheader
        leftTitle={translate("menu.trade")}
        navigator={_props.navigation} />
      <CardPrices />
      <ScrollView
        style={{ marginTop: 5 }}
        showsVerticalScrollIndicator={true}>
        {AppStorage.getGraphURL() && netInfo.isConnected ? (
          <WebView
            key={AppStorage.getGraphURL()}
            ref={webviewRef}
            automaticallyAdjustContentInsets={false}
            scrollEnabled={false}
            onMessage={event => {
              setWebviewHeight(parseInt(event.nativeEvent.data) + 20)
            }}
            onError={() => {
              setWebviewHeight(500);
            }}
            renderError={() => null}
            javaScriptEnabled={true}
            injectedJavaScript={webViewScript}
            domStorageEnabled={true}
            style={[WebviewStyle, { height: webviewHeight }]}
            source={{ 
              uri: AppStorage.getGraphURL()
            }}
          />
        ) : (
          <View style={[WebviewStyle, { height: webviewHeight, backgroundColor: colors.palette.white }]} />
        )}
        <View style={PriceReminderContainer}>
          <View style={[ROW, AI_CENTER]}>
            <Text style={[TEXT_SECTION, MARGIN_S_DF_RIGHT]} tx="tradePage.priceReminder" />
            <MIcon
              onPress={() => setShowDialog(true)}
              name="information" size={Dimen.iconSize.base} />
            {Helper.isLoggeIn() && <TouchableOpacity
              onPress={() => {
                _props.navigation.navigate("PriceReminderHistory")
              }}
              style={[ROW, AI_CENTER, JC_END, FLEX]}>
              <Text style={[TEXT_SMALL_0, MARGIN_S_DF_RIGHT]} tx="tradePage.promptRecord" />
              <MIcon name="right" size={Dimen.iconSize.base} />
            </TouchableOpacity>}
          </View>
          {
            reminders.length > 0 && <FlatList
              style={{ width: Dimen.screenWidth }}
              data={reminders}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => {
                return (
                  <CardPriceReminder
                    onClicked={() => {
                      _props.navigation.navigate("NewPriceReminder", {
                        onGoBack: () => {
                          console.log("onGoBack")
                          loadPriceReminderList("active")
                        },
                        reminderObj: item
                      })
                    }}
                    data={item} />
                )
              }} />
          }
          <TouchableOpacity
            onPress={() => {
              addPriceReminder()
            }}
            style={AddPriceReminderContainer}>
            <MIcon name="plus" size={Dimen.iconSize.base} />
            <Text style={[TEXT_SMALL, TEXT_GRAY]} tx="tradePage.addPriceReminder" />
          </TouchableOpacity>
          <Text
            style={[TEXT_SMALL, TEXT_CENTER, MARGIN_S_DF_TOP]}
            text={getPriceReminderText()} />
          <View style={[W_100P, ROW, MARGIN_DF_TOP]}>
            <View style={BtnCreateRealAcc}>
              <Text
                numberOfLines={1}
                onPress={() => leftButtonClicked()}
                style={btnActionText} text={getLeftButtonText()} />
            </View>
            <View style={BtnCreateDemoAcc}>
              <Text
                numberOfLines={1}
                onPress={() => {
                  rightButtonClicked()
                }}
                style={btnActionText} text={getRightButtonText()} />
            </View>
          </View>
          <BtnContactComp
            containerStyle={{ marginTop: 10, marginBottom: 30 }}
            navigation={_props.navigation} />
        </View>
        <View style={{ height: 110 }}></View>
      </ScrollView>
      <Dialog
        contentStyle={{ margin: 0, padding: 0 }}
        visible={showDialog}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowDialog(false)}>
        <View>
          <View style={[ROW_CENTER, { marginBottom: Dimen.padding.md, marginHorizontal: Dimen.padding.md }]}>
            <Text style={[TEXT_H4, FLEX]} tx="tradePage.priceReminderInfo" />
            <MIcon onPress={() => setShowDialog(false)} name="closeSolid" />
          </View>
          <View style={GREY_BORDER_B}></View>
          <View style={{ margin: Dimen.padding.md }}>
            {
              priceReminderInfo.map((item, index) => {
                return (
                  <View key={index} style={[ROW, MARGIN_S_DF_TOP]}>
                    <Text style={TEXT_SMALL_0} text={(index + 1) + ". "} />
                    <Text style={TEXT_SMALL_0} text={item} />
                  </View>
                )
              })
            }
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showDialogReminderExpired}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowDialogReminderExpired(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("tradePage.priceReminderExpired")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowDialogReminderExpired(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX]}
              tx="common.ok" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showDialogLoginNeeded}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowDialogLoginNeeded(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("tradePage.priceReminderLoginNeeded")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowDialogLoginNeeded(false)
                _props.navigation.navigate("Login")
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX]}
              tx="common.ok" />
          </View>
        </View>
      </Dialog>
    </Screen>
  )
})