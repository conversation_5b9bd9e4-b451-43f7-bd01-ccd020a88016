import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { CONTAINER_BORDER, FSIZE_13, FSIZE_15, FSIZE_18 } from "../../theme/baseStyle"
import { AI_CENTER, AS_STRETCH, COLUMN, FLEX, JC_CENTER, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_DF_TOP, MARGIN_L_TOP, ROW_CENTER, TEXT_BLACK, TEXT_RIGHT, W_100P } from "../../theme/mStyle"
import { FlatList, TextStyle, View } from "react-native"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { CardTransaction } from "./CardTransaction"
import { getTransactionHistory } from "app/api/model"
import { Api } from "app/api/api"
import { Dropdown } from "react-native-element-dropdown"
import { colors, typography } from "app/theme"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"


interface TransactionHistoryScreenProps extends AppStackScreenProps<"TransactionHistory"> {}

const textCardLabel: TextStyle = {
  ...FSIZE_13,
  fontFamily: typography.primary.normal,    
  color: colors.mine.text,
}

const textCardValue: TextStyle = {
  ...FSIZE_18,
  fontFamily: typography.primary.normal,    
  color: colors.mine.green,
  marginVertical: Dimen.padding.ssm
}

export const TransactionHistoryScreen: FC<TransactionHistoryScreenProps> = observer(function TransactionHistoryScreen(_props) {
  
  const [datas, setDatas] = useState([])
  const [selectedTimeRange, setSelectedTimeRange] = useState("0") 
  const totalRecord = useRef(0)
  const pageRef = useRef(1)
  const [timeRanges, setTimeRanges] = useState([
    {
      value: "0",
      label: translate("account.today")
    },
    {
      value: "1",
      label: translate("account.lastWeek")
    },
    {
      value: "2",
      label: translate("account.lastMonth")
    },
    {
      value: "3",
      label: translate("account.last3Month")
    }
  ])

  const prepareData = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    getHistoryOfTransaction()
  }

  const getHistoryOfTransaction = (timeRange = selectedTimeRange) => {
    AppStorage.showLoading()
    getTransactionHistory(
      AppStorage.appStorage.userDevice.clientId,
      parseInt(timeRange),
      pageRef.current,
      Api.transactionHistoryPageSize,
      (response) => {
        console.log(JSON.stringify(response))
        if (pageRef.current == 1) {
          setDatas(response.record)
        } else {
          setDatas([...datas, ...response.record])
        }
        totalRecord.current = response.total
        AppStorage.hideLoading()        
      },
      (error) => {
        console.log("error: ", error)
        AppStorage.hideLoading()
        setDatas(null)
        totalRecord.current = 0
        // ApiService.showShortToast(error)
      }, 
      () => {
        _props.navigation.navigate("Logout")
      }
    )    
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    console.log("selectedTimeRange: ", datas.length)
    console.log("totalRecord: ", totalRecord.current)
  }, [datas])

  useEffect(() => {
    // console.log("- " + JSON.stringify(AppStorage.appStorage.userOtherInfo))
    pageRef.current = 1
    prepareData()
  }, [])

  return (
    <Screen
      preset="fixed"
      style={FLEX}
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false}
        isTitleCenter={false}
        title={translate("account.transactionHistory")} />
      <View style={[ROW_CENTER, MARGIN_L_TOP]}>
        <View style={[CONTAINER_BORDER, COLUMN, FLEX, AS_STRETCH]}>
          <Text style={textCardLabel} tx="account.balance" />
          {
            AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$" + Helper.formatNumberBalance(AppStorage.appStorage.userOtherInfo.data.balance)}/>
          }
          {
            !AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$0.00"}/>
          }
        </View>
        <View style={[CONTAINER_BORDER, COLUMN, FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, AS_STRETCH]}>
          <Text style={textCardLabel} tx="account.netWorth" />
          {
            AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$" + Helper.formatNumberBalance(AppStorage.appStorage.userOtherInfo.data.equity)}/>
          }
          {
            !AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$0.00"}/>
          }
        </View>
        <View style={[CONTAINER_BORDER, COLUMN, FLEX, AS_STRETCH]}>
          <Text style={textCardLabel} tx="account.availableMargin" />
          {
            AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$" + Helper.formatNumberBalance(AppStorage.appStorage.userOtherInfo.data.margin)}/>
          }
          {
            !AppStorage.appStorage.userOtherInfo.data &&
            <Text style={textCardValue} text={"$0.00"}/>
          }
        </View>
      </View>    
      <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
        <Text style={[FSIZE_18, FLEX]} text={translate("account.fundDetails")} />
        {/* <Text style={[TEXT_CONTENT, MARGIN_S_DF_RIGHT]} text={"當天"} />
        <MIcon name="down" size={Dimen.iconSize.base} /> */} 
        <Dropdown
          style={FLEX}
          itemTextStyle={[TEXT_BLACK, FSIZE_15]}
          placeholderStyle={[TEXT_BLACK, FSIZE_15]}
          selectedTextStyle={[TEXT_BLACK, FSIZE_15, TEXT_RIGHT]}          
          selectedTextProps={{allowFontScaling:false}}
          containerStyle={W_100P}      
          data={timeRanges}
          value={selectedTimeRange} // Convert selectedTimeRange to string
          labelField="label"
          valueField="value"
          onChange={(item) => {
            pageRef.current = 1
            setSelectedTimeRange(item.value)
            getHistoryOfTransaction(item.value)
          }}/>
      </View>
      {
        datas != null && datas.length > 0 &&
        <FlatList
          data={datas}
          nestedScrollEnabled={true}
          contentContainerStyle={{paddingBottom: Dimen.padding.xl}}
          onEndReachedThreshold={0.2}
          onEndReached={() => {
            console.log("onEndReached: " + datas.length + " >< " + totalRecord.current)
            if (datas.length < totalRecord.current) {
              pageRef.current = pageRef.current + 1
              getHistoryOfTransaction()
            }
          }}
          ListFooterComponent={() => {
            return (
              <View style={{height: Dimen.screenHeight*0.2}}>
                
              </View>
            )
          }}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => {
            return (
              <CardTransaction data={item}  />
            )
          }}
        />
      }
      {
        datas != null && datas.length == 0 &&
        <View style={[W_100P, AI_CENTER, JC_CENTER, {height: Dimen.screenHeight*0.65}]}>
          <Text style={FSIZE_18} text={translate("account.noRecord")}/>
        </View>
      }
    </Screen>
  )
})