import { useState, useEffect } from "react";
import { Helper } from "app/utils/helper"
import { Text } from "../../components"
import { Dimen } from "../../theme/dimen"
import { COLUMN, FLEX, ROW, SHADOW } from "../../theme/mStyle"
import * as React from "react"
import { TextStyle, View, ViewStyle } from "react-native"
import Animated, { interpolateColor, useAnimatedStyle, useSharedValue, withTiming } from "react-native-reanimated"


const CardPriceContainer: ViewStyle = {
  ...SHADOW,
  ...COLUMN,
  width: Dimen.screenWidth * 0.29,
  minHeight: Dimen.screenWidth * 0.2,
  backgroundColor: 'white',
  borderRadius: Dimen.borderRadius * 2,
}

const TextMarket: TextStyle = {
  fontSize: Dimen.fontSize.sm,
  color: 'black'
}

const TextPrice: TextStyle = {
  fontSize: Dimen.fontSize.md,
  // fontWeight: Platform.OS == "ios" ? "800" : 'bold',
  color: '#02a183'
}

const TextChangingValue: TextStyle = {
  fontSize: Dimen.fontSize.ssm,
  color: '#02a183'
}

export function CardPrice(props: { symbol: string, ytd: { open: number, close: number } | undefined, price: number }) {
  const {
    symbol,
    ytd,
    price,
  } = props;

  const [open2, setOpen2] = useState(0);
  const [price2, setPrice2] = useState(0);
  const [delta, setDelta] = useState(0);
  const [diff, setDiff] = useState(0);
  const [color, setColor] = useState('white');
  const [backgroundColor, setBackgroundColor] = useState('white');
  const progress = useSharedValue(0);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    setIsReady(!!ytd);
    if (!!ytd) {
      setOpen2(ytd.open);
    }
  }, [ytd])

  useEffect(() => {
    if (isReady) {
      setPrice2(price || ytd.close);
      progress.value = 0;
      progress.value = withTiming(1, { duration: 500 })
    }
  }, [price, open2])

  useEffect(() => {
    setDelta(((price2) * 10000 - open2 * 10000) / 10000);
  }, [price2])

  useEffect(() => {
    setDiff((Math.round(delta / open2 * 100 * 1000) / 1000) || 0);
  }, [delta])

  useEffect(() => {
    if (Helper.isDisplayModeRed()) {
      if (delta < 0) {
        setColor("#02a183");
        setBackgroundColor('rgba(0, 255, 0, .1)');
      } else if (delta > 0) {
        setColor("#EC8181");
        setBackgroundColor('rgba(255, 0, 0, .1)');
      }
    } else {
      if (delta < 0) {
        setColor("#EC8181");
        setBackgroundColor('rgba(255, 0, 0, .1)');
      } else if (delta > 0) {
        setColor("#02a183");
        setBackgroundColor('rgba(0, 255, 0, .1)');
      }
    }
  }, [delta, diff, Helper.isDisplayModeRed()])

  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        progress.value,
        [0, 1],
        [backgroundColor, 'white']
      ),
    };
  });

  return (
    <View style={CardPriceContainer}>
      <Animated.View style={[{ borderRadius: Dimen.borderRadius * 2, flex: 1, justifyContent: 'center' }, animatedStyle]}>
        <View style={{ padding: Dimen.padding.ssm }}>
          <Text style={TextMarket} text={symbol} />
          <Text style={[TextPrice, { color }]} text={isReady ? String(price2 || ytd.close) : ""} weight="bold" />
          {isReady &&
            <View style={ROW}>
              <Text
                style={[TextChangingValue, { color }]}
                text={
                  !price2 ? String(ytd.close - ytd.open) :
                    (delta > 0 ? "+" : "") + String(Number(parseFloat(String(delta)).toFixed(3)))
                }
              />
              <View style={FLEX} />
              <Text
                style={[TextChangingValue, { color }]}
                text={(delta > 0 ? "+" : "") + String(isFinite(diff) ? diff : '0') + "%"}
              />
            </View>
          }
        </View>
      </Animated.View>
    </View>
  )
}