<resources>    
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.DayNight.NoActionBar">
        <!-- Customize your theme here. -->
        <!-- <item name="android:editTextBackground">@drawable/rn_edit_text_material</item> -->
    </style>
    <style name="AppTheme.Launcher">
        <item name="android:windowBackground">@drawable/launch_screen</item>
    </style>

    <style name="BootTheme" parent="Theme.SplashScreen">
        <item name="android:windowDisablePreview">true</item>
        <item name="background">@drawable/launch_screen</item>
        <item name="backgroundImage">@drawable/launch_screen</item>
<!--         <item name="windowSplashScreenBackground">@color/bootsplash_background</item>-->
<!--        <item name="android:colorBackground">@color/bootsplash_background</item>-->
<!--        <item name="windowSplashScreenAnimatedIcon">@drawable/launch_screen</item>-->
        <item name="postSplashScreenTheme">@style/AppTheme</item>
    </style>

</resources>
