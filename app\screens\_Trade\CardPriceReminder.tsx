import { CONTAINER_BORDER, FSIZE_12, FSIZE_18, TEXT_H4_M, TEXT_SECTION, TEXT_VALUE_GREEN, TEXT_VALUE_RED } from "app/theme/baseStyle"
import { Text } from "../../components"
import { Dimen } from "../../theme/dimen"
import { COLUMN, FLEX, FLEX_2, MARGIN_S_DF_TOP, ROW, ROW_CENTER, TEXT_COLOR_ALERT, TEXT_COLOR_GREEN } from "../../theme/mStyle"
import * as React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { Api } from "app/api/api"
import { colors } from "app/theme"
import { translate } from "app/i18n"
import moment from "moment"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"


const CardReminderRoot: ViewStyle = {
    ...ROW,
    ...CONTAINER_BORDER,
    width: Dimen.screenWidth - 2 * Dimen.padding.base,
    borderRadius: Dimen.borderRadiusLarge,
    backgroundColor: colors.palette.white,
    marginVertical: Dimen.padding.ssm,
    padding: 8,
}

const ReminderInfoContainer: ViewStyle = {
    ...FLEX,
    ...COLUMN,
    padding: 5,
}

const CreatedAtContainer: ViewStyle = {
    ...FLEX,
    ...COLUMN,
    padding: 5
}

const HoriLine: ViewStyle = {
    width: 30,
    height: 5,
    marginTop: 1,
    borderRadius: 50,
    backgroundColor: colors.mine.primary,
}

const PriceContainer: ViewStyle = {
    ...COLUMN,
    alignItems: "flex-end",
    paddingVertical: 5,
    paddingRight: 10,
    paddingLeft: 20,
    borderRadius: Dimen.borderRadiusLarge,
    backgroundColor: colors.mine.priceReminderUp,
}

const Dot: ViewStyle = {
    width: 5,
    height: 5,
    borderRadius: 5,
    marginHorizontal: 10,
    backgroundColor: colors.palette.black,
}


export function CardPriceReminder(props: any) {
    const {
        containerStyle = {},
        type = 1,
        data = null,
        onClicked = () => { console.log("onClicked not set up") }
    } = props

    const getItemName = () => {
        const itemType = (data && data.item) ? data.item : ""
        if (itemType == "GOLD") {
            return translate("newPriceReminderScreen.londonGold")
        } else if (itemType == "SILVER") {
            return translate("newPriceReminderScreen.londonSilver")
        } else if (itemType == "USDINDEX") {
            return translate("newPriceReminderScreen.dollarIndex")
        } else {
            return "-"
        }
    }
    const getCreatedAt = () => {
        return data.createdAt ? moment(data.createdAt).format("YYYY-MM-DD HH:mm") : ""
    }

    const getItemColor = () => {
        const itemType = (data && data.item) ? data.item : ""
        if (itemType == "GOLD") {
            return "#c5af72"
        } else if (itemType == "SILVER") {
            return "#9e9e9e"
        } else if (itemType == "USDINDEX") {
            return "#4876e2"
        } else {
            return "#c5af72"
        }
    }

    const getDislayPrice = () => {
        if (data && data.type) {
            if (data.type == 1) {
                return (parseFloat(data.price)).toFixed(2)
            } else if (data.type == 2) {
                if (data.condition == "rise") {
                    return (parseFloat(data.latestPrice) + parseFloat(data.target)).toFixed(2)
                } else {
                    return (parseFloat(data.latestPrice) - parseFloat(data.target)).toFixed(2)
                }
            } else {
                return (parseFloat(data.price)).toFixed(2)
            }
        } else {
            return "-"
        }
    }

    const getPriceBackgroundColor = () => {
        console.log("data: ", JSON.stringify(AppStorage.appStorage))
        if (AppStorage.appStorage && AppStorage.appStorage.setting) {
            if (!Helper.isDisplayModeRed()) {
                if (data.condition == "rise") {
                    return colors.mine.priceReminderUp
                } else {
                    return colors.mine.priceReminderDown
                }
            } else {
                if (data.condition == "rise") {
                    return colors.mine.priceReminderDown
                } else {
                    return colors.mine.priceReminderUp
                }
            }
        } else {
            if (data.condition == "rise") {
                return colors.mine.priceReminderUp
            } else {
                return colors.mine.priceReminderDown
            }
        }
    }

    const getTestPriceStyleColor = () => {
        if (AppStorage.appStorage && AppStorage.appStorage.setting) {
            if (!Helper.isDisplayModeRed()) {
                if (data.condition == "rise") {
                    return TEXT_COLOR_GREEN
                } else {
                    return TEXT_COLOR_ALERT
                }
            } else {
                if (data.condition == "rise") {
                    return TEXT_COLOR_ALERT
                } else {
                    return TEXT_COLOR_GREEN
                }
            }
        } else {
            if (data.condition == "rise") {
                return TEXT_COLOR_GREEN
            } else {
                return TEXT_COLOR_ALERT
            }
        }
    }

    
    const renderStatusText = () => {
        if (data.status === 'expired') {
            return (
                <Text style={[TEXT_VALUE_RED, FSIZE_12, MARGIN_S_DF_TOP]}>
                    {translate("tradePage.expired")}
                </Text>
            );
        }
    
        if (data.status === 'cancelled') {
            return (
                <Text style={[TEXT_VALUE_RED, FSIZE_12, MARGIN_S_DF_TOP]}>
                    {translate("tradePage.cancelled")}
                </Text>
            );
        }
    
        if (data.status === 'inactive') {
            return (
                <Text style={[TEXT_VALUE_GREEN, FSIZE_12, MARGIN_S_DF_TOP]}>
                    {translate("tradePage.inactive")}
                </Text>
            );
        }
    
        return (
            <Text style={[FSIZE_12, MARGIN_S_DF_TOP]}>
                {translate("tradePage.validUntil") + ": " + moment(data.expiryDate).format(Helper.dateFormateList.dateApi)}
            </Text>
        );
    };

    React.useEffect(() => {
        getPriceBackgroundColor()
    }, [])

    return (
        <TouchableOpacity
            onPress={onClicked}
            style={[CardReminderRoot, containerStyle]}>
            <View style={ReminderInfoContainer}>
                <Text style={[TEXT_SECTION, FSIZE_18]} text={getItemName()} />
                <View style={[HoriLine, { backgroundColor: getItemColor() }]}></View>
                {renderStatusText()} 
            </View>
            <View style={CreatedAtContainer}>
                <Text style={[TEXT_SECTION, FSIZE_12]} text={getCreatedAt()} />
            </View>
            <View style={[PriceContainer, { backgroundColor: getPriceBackgroundColor() }]}>
                <View style={ROW_CENTER}>
                    <Text style={FSIZE_12} text={data.type == 2 ? translate("tradePage.priceUpDown") : translate("tradePage.priceReach")} />
                    <View style={Dot}></View>
                    <Text style={FSIZE_12} text={data.condition == "rise" ? translate("tradePage.riseTo") : translate("tradePage.dropTo")} />
                </View>
                <Text style={[TEXT_H4_M, MARGIN_S_DF_TOP, FSIZE_18, getTestPriceStyleColor()]} text={getDislayPrice()} />
            </View>
        </TouchableOpacity>
    )
}