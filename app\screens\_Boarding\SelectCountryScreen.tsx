
import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"

import { Screen, Text } from "../../components"
import { colors, spacing } from "../../theme"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "app/components/MIcon"
import { TEXT_LINK, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "app/theme/baseStyle"
import { FLEX, MARGIN_DF_TOP, TEXT_GRAY } from "app/theme/mStyle"
import { TouchableOpacity, ViewStyle } from "react-native"
import { Dimen } from "app/theme/dimen"
import { translate } from "app/i18n"

interface SelectCountryScreenProps extends AppStackScreenProps<"SelectCountry"> {}

const PHONE_ROW: ViewStyle = {
  width: "100%",
  flexDirection: "row",
  alignItems: "center",
  borderBottomColor: colors.mine.textGray,
  borderBottomWidth: 0.5,
  paddingVertical: Dimen.padding.base,
}

export const SelectCountryScreen: FC<SelectCountryScreenProps> = observer(function SelectCountryScreen(_props) {
  const [phoneList, setPhoneList] = useState([])
  const countryPhoneList = [
    {
      "name": 'common.china',
      "dial_code": "+86",
      "phone_length_from": 11,
      "phone_length_to": 11
    },
    {
      "name": 'common.hkchina',
      "dial_code": "+852",
      "phone_length_from": 8,
      "phone_length_to": 8
    },
    {
      "name": 'common.macauchina',
      "dial_code": "+853",
      "phone_length_from": 8,
      "phone_length_to": 8
    },
    {
      "name": 'common.twchina',
      "dial_code": "+886",
      "phone_length_from": 8,
      "phone_length_to": 8
    },
    {
      "name": 'common.malaysia',
      "dial_code": "+60",
      "phone_length_from": 8,
      "phone_length_to": 10
    },
    {
      "name": 'common.vietnam',
      "dial_code": "+84",
      "phone_length_from": 7,
      "phone_length_to": 10
    }
  ]

  const onSelectPhoneCountry = (item: { label: string; value: string }) => {
    if (_props.route && _props.route.params && _props.route.params.onSelect) {
      console.log("item: " + JSON.stringify(item))
      const selectedItem = item
      countryPhoneList.map((item) => {
        if (item.dial_code === selectedItem.value) {
          selectedItem.phone_length_from = item.phone_length_from
          selectedItem.phone_length_to = item.phone_length_to
        }
      })
      _props.navigation.goBack()
      _props.route.params.onSelect(selectedItem)
    }
  }

  useEffect(() => {
    const tempList: { label: string; value: string }[] = []
      countryPhoneList.map((item) => tempList.push({
        name: item.name,
        value: item.dial_code
      }))
      setPhoneList(JSON.parse(JSON.stringify(tempList)))
  },[])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: spacing.md }}
      safeAreaEdges={["top", "bottom"]}
    >
      <MIcon name="closeBlack" onPress={() => {_props.navigation.goBack()}} />
      <Text style={[TEXT_SECTION, TEXT_LINK, MARGIN_DF_TOP]} tx="boarding.selectCountry" />
      {
        phoneList.map((item, index) => {
          return (
            <TouchableOpacity 
              onPress={() => onSelectPhoneCountry(item)}
              key={index}
              style={PHONE_ROW}>
              <Text style={[TEXT_SMALL_0, FLEX]} tx={item.name} />
              <Text style={[TEXT_SMALL, TEXT_GRAY]} text={item.value} />
            </TouchableOpacity>
          )
        })
      }
    </Screen>
  )
})