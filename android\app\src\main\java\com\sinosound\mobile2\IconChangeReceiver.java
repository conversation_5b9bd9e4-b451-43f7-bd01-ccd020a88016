package com.sinosound.mobile2;

import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Debug;
import android.util.Log;

public class IconChangeReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        String iconName = intent.getStringExtra("iconName");
        PackageManager pm = context.getPackageManager();
        String packageName = context.getPackageName();
        String dfName = packageName + ".MainActivity";
        String prodName = packageName + ".MainActivityprod";
        String uatName =  packageName + ".MainActivityuat";

        // Disable all icons first
//        pm.setComponentEnabledSetting(new ComponentName(context, dfName),
//                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
        pm.setComponentEnabledSetting(new ComponentName(context, uatName),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
        pm.setComponentEnabledSetting(new ComponentName(context, prodName),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);

        // Enable the selected icon
        if ("prod".equals(iconName)) {
            pm.setComponentEnabledSetting(new ComponentName(context, prodName),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        } else if ("uat".equals(iconName)) {
            pm.setComponentEnabledSetting(new ComponentName(context, uatName),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        } else {
            pm.setComponentEnabledSetting(new ComponentName(context, prodName),
                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
        }
    }
}
