import { observer } from "mobx-react-lite"
import React, { FC } from "react"

import { Screen, Text } from "../../components"
import { spacing } from "../../theme"
import { AppStackScreenProps } from "../../navigators"

interface InformationScreenProps extends AppStackScreenProps<"Information"> {}

export const InformationScreen: FC<InformationScreenProps> = observer(function InformationScreen(_props) {
  

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: spacing.md }}
      safeAreaEdges={["top", "bottom"]}
    >
      <Text text="Information screen" />
    </Screen>
  )
})
