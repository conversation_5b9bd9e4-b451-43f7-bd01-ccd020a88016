# CMAKE generated file: DO NOT EDIT!
# Generated by CMake Version 3.22
cmake_policy(SET CMP0009 NEW)

# input_SRC at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:38 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/build/generated/rncli/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/build/generated/rncli/src/main/jni/rncli.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Ka<PERSON>/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/AliyunPush-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/AliyunPushJSI-generated.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/aliyun-react-native-push/android/build/generated/source/codegen/jni/react/renderer/components/AliyunPush/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/RNImagePickerSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/RNImagePickerSpecJSI-generated.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/pagerview-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/States.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/react/renderer/components/pagerview/pagerviewJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:21 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/safeareacontext-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CUSTOM_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:22 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp/react/renderer/components/rnscreens/utils/*.cpp")
set(OLD_GLOB
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# LIB_CODEGEN_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:23 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/rnsvg-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# react_codegen_SRCS at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt:9 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp"
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()

# input_SRC at /Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake:38 (file)
file(GLOB NEW_GLOB LIST_DIRECTORIES true "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/*.cpp")
set(OLD_GLOB
  "/Users/<USER>/working/Karsten/sino/sino_new/sino/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"
  )
if(NOT "${NEW_GLOB}" STREQUAL "${OLD_GLOB}")
  message("-- GLOB mismatch!")
  file(TOUCH_NOCREATE "/Users/<USER>/working/Karsten/sino/sino_new/sino/android/app/.cxx/Debug/2107702h/arm64-v8a/CMakeFiles/cmake.verify_globs")
endif()
