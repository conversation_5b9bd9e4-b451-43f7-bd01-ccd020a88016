import { COLOR_8B, FSIZE_12, FSIZE_13, TEXT_SMALL_0 } from "app/theme/baseStyle"
import { Text, } from "../../components"
import { Dimen } from "../../theme/dimen"
import { COLUMN, FLEX, FLEX_2, MARGIN_S_DF_TOP, ROW, TEXT_RIGHT, W_100P } from "../../theme/mStyle"
import * as React from "react"
import { ImageStyle, TextStyle, View, ViewStyle } from "react-native"
import FastImage from "react-native-fast-image"
import { Rating } from '@kolking/react-native-rating';
import { colors } from "app/theme"
import { translate } from "app/i18n"
import { AppStorage } from "app/utils/appStorage"
import { useEffect, useState } from "react"
import { Helper } from "app/utils/helper"
import moment from "moment"
import { Api } from "app/api/api"


const CardCalenderContainer: ViewStyle = {
    ...ROW,
    alignItems: "flex-start",
    paddingHorizontal: Dimen.padding.sm,
    marginVertical: Dimen.padding.sm,    
}

const TimeCountryContainer: ViewStyle = {
    ...ROW,
    alignItems: "center",
    width: Dimen.screenWidth*0.22,
}

const FlagImage: ImageStyle = {
    width: 30,
    height: 30,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: "#F6DA86",
    marginHorizontal: Dimen.padding.ssm,
    overflow: "hidden",
}

const AffectRed2StarLess: TextStyle = {
    ...FSIZE_12,
    lineHeight: 20,
    color: colors.mine.red,
    backgroundColor: colors.mine.white,
    borderColor: colors.mine.red,
    fontWeight: "bold",        
    paddingHorizontal: 6,
    borderRadius: Dimen.borderRadius,
    borderWidth: 1,
    alignSelf: 'flex-start',
    overflow: "hidden"
}

const AffectRed3StarMore: TextStyle = {
    ...FSIZE_12,
    lineHeight: 20,
    color: colors.mine.white,
    backgroundColor: colors.mine.red,
    borderColor: colors.mine.red,
    fontWeight: "bold",    
    paddingHorizontal: 6,
    borderRadius: Dimen.borderRadius,
    borderWidth: 1,
    alignSelf: 'flex-start',
    overflow: "hidden"
}

const AffectGreen2StarLess: TextStyle = {
    ...FSIZE_12,
    lineHeight: 20,
    color: colors.mine.green,
    backgroundColor: colors.mine.white,
    borderColor: colors.mine.green,
    fontWeight: "bold",    
    paddingHorizontal: 6,
    borderRadius: Dimen.borderRadius,
    borderWidth: 1,
    alignSelf: 'flex-start',
    overflow: "hidden"
}

const AffectGreen3StarMore: TextStyle = {
    ...FSIZE_12,
    lineHeight: 20,
    color: colors.mine.white,
    backgroundColor: colors.mine.green,
    borderColor: colors.mine.green,
    fontWeight: "bold",    
    paddingHorizontal: 6,
    borderRadius: Dimen.borderRadius,
    borderWidth: 1,
    alignSelf: 'flex-start',
    overflow: "hidden"
}

export const textRowValue: TextStyle = {
    ...FSIZE_12,
    ...COLOR_8B,
    lineHeight: 20,
}

export function CardCalendarNews (props: any) {
    const {
        containerStyle = {},
        data = {},
        type = 0,
        previousTime = "",
    } = props
    const [cardData, setCardData] = useState(data)

    const getAffectText = (affect: string) => {
        if (affect == translate("trendPage.affectLApi")) {
            return translate("trendPage.affectL")
        } else {
            return translate("trendPage.affectD")
        }
    }

    const getStyleAffect = (affect: string, rating: number) => {
        if (AppStorage.appStorage.setting.displayMode == Api.displayModes.red) {
            if (affect == translate("trendPage.affectLApi")) {
                return rating <=2 ? AffectRed2StarLess : AffectRed3StarMore
            } else {
                return rating <=2 ? AffectGreen2StarLess : AffectGreen3StarMore
            }
        } else {
            if (affect == translate("trendPage.affectLApi")) {
                return rating <=2 ? AffectGreen2StarLess : AffectGreen3StarMore
            } else {
                return rating <=2 ? AffectRed2StarLess : AffectRed3StarMore
            }
        }
    }

    const showTimeAndFlag = () => {
        if (Helper.formatDate(previousTime, "HH:MM") == Helper.formatDate(cardData.pub_time, "HH:mm")) {
            return false
        } else {
            return true
        }
    }    

    useEffect(() => {
        setCardData(() => data)
    }, [data, data.country])

    return (
        <View style={[CardCalenderContainer, containerStyle]}>
            <View style={[TimeCountryContainer, { opacity: showTimeAndFlag() ? 1 : 0}]}>
                <Text style={[TEXT_SMALL_0, FLEX, TEXT_RIGHT]} text={moment(type == 2 ? cardData.date : cardData.pub_time).format("HH:mm")} />
                {/* <View style={FlagImage}>
                    <SvgUri                        
                         width="100%"
                         height="100%"
                        uri="https://flagicons.lipis.dev/flags/4x3/hk.svg" />
                </View> */}
                <FastImage
                    style={FlagImage} 
                    source={{uri: Helper.getCountryFlagUrl(cardData.country)}} />                
            </View>
            { type == 2 && <View style={[FLEX, COLUMN, {marginTop: 4}]}>
                <View style={[ROW, W_100P]}>
                    <Text style={FSIZE_13} text={cardData.exchange_name}/>
                    <View style={FLEX}></View>
                    <Text style={FSIZE_13} text={cardData.name}/>
                </View>
                <Text style={TEXT_SMALL_0} text={cardData.rest_note}/>
            </View>

            }
            {type != 2 && <View style={[FLEX, COLUMN, {marginTop: 4}]}>
                <Text style={[FSIZE_13, {color: cardData.star <= 2 ? colors.mine.text : colors.mine.red}]} text={cardData.name}/>
                <Rating 
                    variant="stars-outline"
                    size={10}
                    style={MARGIN_S_DF_TOP}
                    baseColor={colors.palette.black}
                    fillColor={cardData.star <= 2 ? colors.mine.gray : colors.mine.red}
                    rating={cardData.star}/>
                {type == 0 && <View style={[ROW, W_100P, MARGIN_S_DF_TOP]}>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_12, COLOR_8B]} tx="trendPage.formerValue" />
                        <Text style={textRowValue} text={cardData.previous ? cardData.previous : "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_12, COLOR_8B]} tx="trendPage.predictiveValue" />
                        <Text style={textRowValue} text={cardData.consensus ? cardData.consensus : "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_12, COLOR_8B]} tx="trendPage.publishedValue" />
                        <Text style={textRowValue} text={cardData.actual ? cardData.actual: "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_12, COLOR_8B]} tx="trendPage.affect" />
                        <Text style={getStyleAffect(cardData.affect, cardData.star)} text={getAffectText(cardData.affect)} />
                    </View>
                </View>}
            </View>}
        </View>
    )   
}