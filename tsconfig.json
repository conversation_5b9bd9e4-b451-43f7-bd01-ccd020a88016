{"compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "jsx": "react-native", "module": "es2015", "moduleResolution": "node", "noImplicitAny": false, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "sourceMap": true, "target": "esnext", "lib": ["esnext", "dom"], "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": "./"}, "exclude": ["node_modules"], "include": ["index.js", "App.js", "app", "test", "*.js"], "extends": "expo/tsconfig.base"}