package com.sinosound.mobile2;

import android.content.ComponentName;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;

import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;

public class DeviceIconModule extends ReactContextBaseJavaModule {

    ReactApplicationContext mContext;
    DeviceIconModule (ReactApplicationContext context) {
        super(context);
        this.mContext = context;
    }

    @Override
    public String getName() {
        return "DeviceIconModule";
    }

    @ReactMethod
    public void changeAppIcon(String iconName, Promise promise) {
        PackageManager pm = mContext.getPackageManager();
        String packageName = mContext.getPackageName();
        Intent intent = new Intent(packageName + ".CHANGE_ICON");
        intent.putExtra("iconName", iconName);
        getReactApplicationContext().sendBroadcast(intent);

//        PackageManager pm = mContext.getPackageManager();
//        String packageName = mContext.getPackageName();
//        String prodName = packageName + ".MainActivityprod";
//        String uatName =  packageName + ".MainActivityuat";
//
//        // Disable all icons first
////        pm.setComponentEnabledSetting(new ComponentName(mContext, ".MainActivity"),
////                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
//        pm.setComponentEnabledSetting(new ComponentName(mContext, uatName),
//                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
//        pm.setComponentEnabledSetting(new ComponentName(mContext, prodName),
//                PackageManager.COMPONENT_ENABLED_STATE_DISABLED, PackageManager.DONT_KILL_APP);
//
//        // Enable the selected icon
//        if ("prod".equals(iconName)) {
//            pm.setComponentEnabledSetting(new ComponentName(mContext, prodName),
//                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//        } else if ("uat".equals(iconName)) {
//            pm.setComponentEnabledSetting(new ComponentName(mContext, uatName),
//                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//        } else {
//            pm.setComponentEnabledSetting(new ComponentName(mContext, prodName),
//                    PackageManager.COMPONENT_ENABLED_STATE_ENABLED, PackageManager.DONT_KILL_APP);
//        }

        try {
            String deviceName = Build.MODEL;
            promise.resolve(deviceName + " - " + iconName + " - " + packageName + ".CHANGE_ICON");
        } catch (Exception e) {
            promise.reject("Error", e);
        }
    }

    @ReactMethod
    public void getDeviceName(Promise promise) {
        PackageManager pm = mContext.getPackageManager();

        try {
            String deviceName = Build.MODEL;
            promise.resolve(deviceName);
        } catch (Exception e) {
            promise.reject("Error", e);
        }
    }
}