import { Helper } from "app/utils/helper"
import { Text } from "../../components"
import { Dimen } from "../../theme/dimen"
import { AI_CENTER, CENTER, COLUMN, FLEX, JC_END, ROW, SHADOW, TEXT_CENTER } from "../../theme/mStyle"
import * as React from "react"
import { ImageBackground, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { COLOR_WHITE, TEXT_B2, TEXT_B2_REGULAR } from "app/theme/baseStyle"
import { translate } from "app/i18n"
import { AppStorage } from "app/utils/appStorage"

const BtnContactRoot: ViewStyle = {
  width: Dimen.screenWidth,
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginTop: 5,
}

const BtnContactBg: ViewStyle = {
  width: Dimen.screenWidth * 0.5,
  height: 0.4 * Dimen.screenWidth * 0.5,
}

const BtnContactText: TextStyle = {
  ...TEXT_B2, 
  ...COLOR_WHITE, 
  ...TEXT_CENTER,
  width: Dimen.screenWidth * 0.35,
  marginTop: 10,
}

export function BtnContactComp(props: any) {
  const {
    text = translate("homePage.contactCustomerService"),
    containerStyle = {},
    navigation,
  } = props;

  return (
    <TouchableOpacity
      onPress={() => {
        if (!AppStorage.isNetworkConnected()) {
          return
        }
        if (navigation) {
          Helper.loadCsUrlAndGo(navigation)
        }
      }}
      style={[BtnContactRoot, containerStyle]}>
      <ImageBackground 
        style={[BtnContactBg, ROW, AI_CENTER, JC_END]}
        source={require("../../../assets/images/btn-contact.png")}>
        <Text style={BtnContactText} text={text} />
      </ImageBackground>
    </TouchableOpacity>
  )
}