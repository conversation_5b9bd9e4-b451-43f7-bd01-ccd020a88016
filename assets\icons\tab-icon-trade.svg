<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="86.569" height="86.569" viewBox="0 0 86.569 86.569">
  <defs>
    <filter id="btn-trade" x="0" y="0" width="86.569" height="86.569" filterUnits="userSpaceOnUse">
      <feOffset dy="1" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="5" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="tab-trade" transform="translate(-144.216 -657.088)">
    <rect id="bg" width="72" height="72" transform="translate(151.873 663.745)" fill="none"/>
    <g id="icon" transform="translate(480.044 3546.745)">
      <g transform="matrix(1, 0, 0, 1, -335.83, -2889.66)" filter="url(#btn-trade)">
        <g id="btn-trade-2" data-name="btn-trade" transform="translate(43.28 19.66) rotate(45)" fill="#c5af73" stroke="rgba(255,255,255,0.5)" stroke-width="4">
          <rect width="32" height="32" stroke="none"/>
          <rect x="-2" y="-2" width="36" height="36" fill="none"/>
        </g>
      </g>
      <rect id="btn-trade-3" data-name="btn-trade" width="17.901" height="17.901" transform="translate(-292.544 -2860.031) rotate(45)" fill="#fff"/>
      <rect id="btn-trade-4" data-name="btn-trade" width="11.901" height="11.901" transform="translate(-292.544 -2855.788) rotate(45)" fill="#87af2a"/>
      <line id="Line_3" data-name="Line 3" x2="20" transform="translate(-302.5 -2847.5)" fill="none" stroke="#fff" stroke-width="1"/>
      <rect id="btn-trade-5" data-name="btn-trade" width="8" height="8" transform="translate(-292.544 -2853.029) rotate(45)" fill="#fff"/>
    </g>
  </g>
</svg>
