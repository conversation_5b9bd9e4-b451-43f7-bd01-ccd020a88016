import { Platform, TextStyle, ViewStyle } from "react-native";
import { Dimen } from "./dimen";
import { colors } from "./colors";
import { typography } from "./typography";

export const COLUMN: any = { flexDirection: 'column' }
export const COLUMN_CENTER: any = { flexDirection: 'column', alignItems: 'center' }
export const ROW: any = { flexDirection: 'row' }
export const ROW_CENTER: any = { flexDirection: 'row', alignItems: 'center' }
export const CENTER: any  = { justifyContent: 'center', alignItems: 'center' }
export const JC_START: any = { justifyContent: 'flex-start' }
export const JC_CENTER: any = { justifyContent: 'center' }
export const JC_END: any = { justifyContent: 'flex-end' }
export const JC_SPACE_BETWEEN: any = { justifyContent: 'space-between' }
export const JC_SPACE_AROUND: any = { justifyContent: 'space-around' }
export const AI_START: any = { alignItems: 'flex-start' }
export const AI_CENTER: any = { alignItems: 'center' }
export const AI_END: any = { alignItems: 'flex-end' }
export const AS_CENTER: any = { alignSelf: 'center' }
export const AS_START: any = { alignSelf: 'flex-start' }
export const AS_END: any = { alignSelf: 'flex-end' }
export const AS_STRETCH: any = { alignSelf: 'stretch' }
export const FLEX: any = { flex: 1 }
export const FLEX_2: any = { flex: 2 }
export const FLEX_3 = { flex: 3 }

export const MARGIN_L_TOP = { marginTop: Dimen.padding.xl }

export const MARGIN_DF = { margin: Dimen.padding.base }
export const MARGIN_DF_TOP = { marginTop: Dimen.padding.base }
export const MARGIN_DF_BOTTOM = { marginBottom: Dimen.padding.base }
export const MARGIN_DF_LEFT = { marginStart: Dimen.padding.base }
export const MARGIN_DF_RIGHT = { marginRight: Dimen.padding.base }

export const MARGIN_S_DF = { margin: Dimen.padding.ssm }
export const MARGIN_S_DF_TOP = { marginTop: Dimen.padding.ssm }
export const MARGIN_S_DF_BOTTOM = { marginBottom: Dimen.padding.ssm }
export const MARGIN_S_DF_LEFT = { marginStart: Dimen.padding.ssm }
export const MARGIN_S_DF_RIGHT = { marginRight: Dimen.padding.ssm }

export const PADDING_DF = { padding: Dimen.padding.base }
export const PADDING_DF_TOP = { paddingTop: Dimen.padding.base }
export const PADDING_DF_BOTTOM = { paddingBottom: Dimen.padding.base }
export const PADDING_DF_LEFT = { paddingLeft: Dimen.padding.base }
export const PADDING_DF_RIGHT = { paddingRight: Dimen.padding.base }

export const PADDING_S_DF = { padding: Dimen.padding.ssm }
export const PADDING_S_DF_TOP = { paddingTop: Dimen.padding.ssm }
export const PADDING_S_DF_BOTTOM = { paddingBottom: Dimen.padding.ssm }
export const PADDING_S_DF_LEFT = { paddingLeft: Dimen.padding.ssm }
export const PADDING_S_DF_RIGHT = { paddingRight: Dimen.padding.ssm }

export const MARGIN_TINY = { margin: 3 }
export const PADDING_TINY = { padding: 3 }

export const MARGIN_PADDING_ZERO = { margin: 0, padding: 0 }

export const W_100P: any = { width: '100%' }
export const W_95P = { width: '95%' }
export const W_90P = { width: '90%' }
export const W_85P = { width: '85%' }
export const W_80P = { width: '80%' }
export const W_75P = { width: '75%' }
export const W_70P = { width: '70%' }
export const W_65P = { width: '65%' }
export const W_60P = { width: '60%' }
export const W_55P = { width: '55%' }
export const W_50P = { width: '50%' }
export const W_45P = { width: '45%' }
export const W_40P = { width: '40%' }
export const W_35P = { width: '35%' }
export const W_30P = { width: '30%' }
export const W_25P = { width: '25%' }
export const W_20P = { width: '20%' }
export const W_15P = { width: '15%' }
export const W_10P = { width: '10%' }

export const BOLD: TextStyle = {
    fontWeight: Platform.OS === 'ios' ? '700' : 'normal'
}

export const TEXT_GRAY: TextStyle = {
    color: colors.mine.textGray
}

export const TEXT_WHITE: TextStyle = {
    color: colors.palette.white
}

export const TEXT_BLACK: TextStyle = {
    color: colors.palette.black
}

export const TEXT_CENTER: TextStyle = {
    textAlign: 'center'
}

export const TEXT_RIGHT: TextStyle = {
    textAlign: 'right'
}

export const TEXT_COLOR_PRIMARY: TextStyle = {
    color: colors.mine.primary
}

export const TEXT_COLOR_GREEN: TextStyle = {
    color: colors.mine.green
}

export const TEXT_COLOR_ALERT: TextStyle = {
    color: colors.mine.alert
}

export const TEXT_COLOR_GRAY: TextStyle = {
    color: colors.mine.gray,
}

export const SHADOW: ViewStyle = {
    shadowColor: colors.mine.shadow,
    shadowOffset: { 
        width: 0, 
        height: 2 
    },
    shadowOpacity: 0.12,
    shadowRadius: 3.84,
    elevation: 5,
}

export const CHAT_LIST_ITEM: ViewStyle = {
    ...ROW_CENTER,
  borderBottomColor: colors.mine.border,
  borderBottomWidth: 1,
  paddingVertical: Dimen.padding.sm,
  backgroundColor: colors.palette.white
}

export const CHAT_HIDDEN_ITEM_CONTAINER: ViewStyle = {
    ...ROW,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
    borderBottomColor: colors.mine.border,
    borderBottomWidth: 1,
}

export const TEXT_TIME_APPOINMENT: TextStyle = {
    fontFamily: typography.primary.bold, // Replace typography.primary with the specific font family string value
    fontSize: Dimen.fontSize.base,
    lineHeight: Dimen.lineHeight.base,
    color: colors.mine.text,
    padding: 0,
    margin: 0
}

export const ENABLED: ViewStyle = {
    opacity: 1
}

export const DISABLED: ViewStyle = {
    opacity: 0.5
}