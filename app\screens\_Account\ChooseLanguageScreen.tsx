import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { ROW_INFO, TEXT_SCREEN_TITLE, TEXT_SMALL_0} from "../../theme/baseStyle"
import { FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_DF_TOP, ROW_CENTER, TEXT_CENTER, W_100P } from "../../theme/mStyle"
import { FlatList, TouchableOpacity, View } from "react-native"
import { AppStorage } from "app/utils/appStorage"
import { colors } from "app/theme"
import { updateUserSettings } from "app/api/model"
import i18n from "i18n-js"
import { Helper } from "app/utils/helper"
import { BackNavComponent } from "app/components/BackNavComponent"
import { Api } from 'app/api/api'
import { runInAction } from "mobx"

interface ChooseLanguageScreenProps extends AppStackScreenProps<"ChooseLanguage"> {}
export const ChooseLanguageScreen: FC<ChooseLanguageScreenProps> = observer(function ChooseLanguageScreen(_props) {
  
  const [data, setData] = useState([
    translate("account.langTraditionChinese"),
    translate("account.langSimplifiedChinese")
    ,translate("account.langEnglish")
  ])
  const languageCodes = ["TC", "SC", "EN"]

  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
  }

  const updateLanguage = (pos) => {    
    if (Helper.isLoggeIn()) {
      updateUserSettings(
        {language : languageCodes[pos]},
        AppStorage.appStorage.userId,
        (response) => {
          AppStorage.hideLoading()
          console.log("updateUserSettings success", JSON.stringify(response))
          runInAction(() => {
            AppStorage.updateLanguage(languageCodes[pos])
          })
          i18n.locale = languageCodes[pos].toLowerCase()
          _props.navigation.goBack()
        },
        (error) => {
          AppStorage.hideLoading()
          console.log("updateUserSettings error", JSON.stringify(error))
        },
        () => {
          _props.navigation.navigate("Logout")
        }
      )
    } else {
      runInAction(() => {
        AppStorage.updateLanguage(languageCodes[pos])
      })
      i18n.locale = languageCodes[pos].toLowerCase()
      _props.navigation.goBack()
    }
  }

  useEffect(() => {
    // console.log("- " + JSON.stringify(AppStorage.appStorage.userOtherInfo))
    prepareData()
  }, [])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{backgroundColor: colors.mine.white, height: Dimen.screenHeight }}
      safeAreaEdges={["top", "bottom"]}
    >      
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        title={translate("boarding.language")} />
      <FlatList
        data={data}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({item, index}) => 
          <TouchableOpacity
            onPress={async() => {
              updateLanguage(index)
            }}
            style={[ROW_INFO, MARGIN_DF_LEFT, {paddingVertical: Dimen.padding.base}]}>
            <Text style={[TEXT_SMALL_0, FLEX]} text={item} />
            {
              AppStorage.getLanguageName() == item && <MIcon name="checkGreen" size={Dimen.iconSize.base} />
            }
          </TouchableOpacity>
        } />
    </Screen>
  )
})