import React, { useState, useEffect } from 'react';
import { Image, View, Dimensions } from 'react-native';

export function AutoHeightImage (props: any) {
    const { source, imageWidth = Dimensions.get('window').width, style } = props;
    const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  
    useEffect(() => {
      if (source && source.uri) {
        Image.getSize(source.uri, (width, height) => {
          // Calculate the aspect ratio of the image
          const aspectRatio = width / height;
          // Get the container's width (you might want to use Dimensions or onLayout to make this dynamic)
        //   const containerWidth = Dimensions.get('window').width;
        const containerWidth =  imageWidth;
          // Calculate the height based on the container's width and the image's aspect ratio
          const containerHeight = containerWidth / aspectRatio;
          setDimensions({ width: containerWidth, height: containerHeight });
        });
      }
    }, [source]);
  
    return (
      <View style={style}>
        <Image
          source={source}
          style={{ width: dimensions.width, height: dimensions.height, resizeMode: 'contain' }}
        />
      </View>
    );
  };