import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { translate } from "../../i18n"
import { FlatList, View, Image, ImageStyle, TextStyle, ViewStyle, TouchableOpacity } from "react-native"
import { COLUMN, FLEX, MARGIN_DF_RIGHT, ROW_CENTER, TEXT_CENTER, W_100P } from "../../theme/mStyle"
import { Dimen } from "../../theme/dimen"
import { COLOR_WHITE, TEXT_SCREEN_TITLE, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { MIcon } from "../../components/MIcon"
import { getAllPosts } from "app/api/model"
import { AppStorage } from "app/utils/appStorage"
import { Helper } from "app/utils/helper"
import moment from "moment"
import { colors } from "app/theme"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

const ImageEvent: ImageStyle = {
  width: Dimen.screenWidth - Dimen.padding.base*2, 
  height: Dimen.screenHeight*0.4,
  marginHorizontal: Dimen.padding.base,
  borderRadius: Dimen.borderRadiusLarge,
  overflow: "hidden"  
}

const YearEvent: TextStyle = {
  width: Dimen.screenWidth*0.2,
  alignItems: "center",
  justifyContent: "center",
  borderRadius: 100,
  overflow:"hidden",
  padding: Dimen.padding.ssm,
  backgroundColor: colors.mine.primary,  
  marginLeft: Dimen.padding.xl, 
  marginTop: -Dimen.padding.base
}

const EventContent: ViewStyle = {
  marginLeft: Dimen.padding.xxl,
  marginRight: Dimen.padding.base,
  borderLeftColor: colors.mine.primary,
  borderLeftWidth: 1,
  flexDirection: "row",
  alignItems: "flex-start",
  paddingVertical: Dimen.padding.sm
}

const EventDot: ViewStyle = {
  width: 10,
  height: 10,
  borderRadius: 10,
  borderColor: colors.mine.primary,
  borderWidth: 1,
  backgroundColor: colors.mine.white,
  marginTop: 8,
  marginRight: Dimen.padding.sm,
  marginLeft: -5
}

const SmallDot: ViewStyle = {
  width: 4,
  height: 4,
  borderRadius: 4,  
  backgroundColor: colors.mine.black,
  marginTop: Dimen.padding.sm,
  marginHorizontal: 8,
}

interface MajorEventScreenProps extends AppStackScreenProps<"MajorEvent"> {}
export const MajorEventScreen: FC<MajorEventScreenProps> = observer(function MajorEventScreen(_props) {
  
  const [majorEvents, setMajorEvents] = useState([])

  const getYearImage = (eventDate) => {
    const year = moment(eventDate).get("year")
    let yearImage = ""
    for (let i = 0; i < majorEvents.length && yearImage == ""; i ++) {
      const item = majorEvents[i]
      if (item.isShowYear && moment(item.videoTC).get("year") == year) {
        yearImage = item.video
      }
    }
    return yearImage
  }

  const loadMajorEvent = () => {
    console.log("loadMajorEvent")
    AppStorage.showLoading()
    getAllPosts(
      8,
      0,
      0,
      3,
      (response) => {
        AppStorage.hideLoading()
        console.log("loadMajorEvent", response)
        let year = 0;
        const tempList = []
        response.results.map((item, index) => {
          const eventItem = item
          if (year == 0) {
            eventItem.isShowYear = true
            eventItem.year = moment(item.videoTC).get("year")
            year = moment(item.videoTC).get("year")
          } else {
            if (year != moment(item.videoTC).get("year")) {
              eventItem.isShowYear = true
              eventItem.year = moment(item.videoTC).get("year")
              year = moment(item.videoTC).get("year")
            } else {
              eventItem.isShowYear = false
            }
          }
          tempList.push(eventItem)
        })
        setMajorEvents(() => tempList)
      },
      (error) => {
        AppStorage.hideLoading()
        console.log("loadMajorEvent", error)
      }
    )
  }

  const renderItem = ({ item, index }) => {
    return (
      <View style={[W_100P, COLUMN]}>
        {
          item.isShowYear && item.video != null && item.video != "" && 
            <View style={COLUMN}>
              <Image 
                resizeMode="cover"
                source={{ uri: item.video }} 
                style={ImageEvent} />
              <View style={YearEvent}>
                <Text style={[TEXT_SMALL_0, COLOR_WHITE, TEXT_CENTER]} text={item.year} />
              </View>
            </View>
        }
        <TouchableOpacity 
          onPress={() => {
            if (!AppStorage.isNetworkConnected()) {
              return
            }
            _props.navigation.navigate(
              "MajorEventDetails", 
              {
                event: item,
                imageTitle: getYearImage(item.videoTC)
              }
            )
          }}
          style={EventContent}>
          <View style={EventDot} />
          <Text style={TEXT_SMALL_0} text={Helper.formatDate(item.videoTC, "MM . DD")} />
          <View style={SmallDot} />
          <Text style={[TEXT_SMALL_0, FLEX]} text={Helper.getValue(item, "title")} />
        </TouchableOpacity>
      </View>
    )
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        loadMajorEvent()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    loadMajorEvent()
  }, [])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ flex: 1 }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        title={translate("boarding.majorEventOfHan")} />     
      <FlatList
        data={majorEvents}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderItem}/>     
    </Screen>
  )
})
