// This is the first file that ReactNative will run when it starts up.
// If you use Expo (`yarn expo:start`), the entry point is ./App.js instead.
// Both do essentially the same thing.

import App from "./app/app.tsx"
import React from "react"
import { AppRegistry } from "react-native"
import RN<PERSON><PERSON>Splash from "react-native-bootsplash"
// import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only';

// RNBootSplash.hide()
if (typeof global.AbortController === 'undefined') {
  const { AbortController, AbortSignal } = require('abortcontroller-polyfill/dist/abortcontroller-polyfill-only');
  global.AbortController = AbortController;
  global.AbortSignal = AbortSignal;
}

function IgniteApp() {
  return <App hideSplashScreen={RNBootSplash.hide} />
}

AppRegistry.registerComponent('main', () => IgniteApp)
export default App
