const sc = {
  common: {
    ok: "确定",
    cancel: "取消",
    confirm: "确认",
    back: "退出",
    logOut: "登出",
    jan: "一月",
    feb: "二月",
    mar: "三月",
    apr: "四月",
    may: "五月",
    jun: "六月",
    jul: "七月",
    aug: "八月",
    sep: "九月",
    oct: "十月",
    nov: "十一月",
    dec: "十二月",
    resend: "重发",
    submit: "提交",
    yes: "是",
    no: "否",
    year: "年",
    month: "月",
    day: "日",
    delete: "删除",
    sun: "日",
    mon: "一",
    tue: "二",
    wed: "三",
    thu: "四",
    fri: "五",
    sat: "六",
    upload: "上载",
    return: "返回",
    male: "男",
    female: "女",
    china: "中国",
    hongkongAndOthers: "香港及其他",
    hkchina: "中国香港",
    macauchina: "中国澳门",
    twchina: "中国台湾",
    malaysia: "马来西亚",
    vietnam: "越南",
    success: "成功",
    today: "今天",
    janShort: "一月",
    febShort: "二月",
    marShort: "三月",
    aprShort: "四月",
    mayShort: "五月",
    junShort: "六月",
    julShort: "七月",
    augShort: "八月",
    sepShort: "九月", 
    octShort: "十月",
    novShort: "十一月",
    decShort: "十二月"
  },
  errors: {
    invalidEmail: "电子邮件地址无效。",
    invalidReminderId: "到价提示ID无效。",
    standard: "请稍后再试。",
    enterPhone: "请输入您的电话号码。",
    enterEmail: "请输入您的电子邮件地址。",
    enterName: "请输入您的姓名。",
    phoneExits: "电话号码已存在。",
    otpWrong: "你输的验证码错误,请重新输入。",
    phoneVerificationIncorrect: "手机验证码不正确",
    unAuthorize: "未授权，需要重新登入。",
    addPriceReminder: "请新增到价提示。",
    noRelatedData: "无相关数据",
    inputAllRequiredValues: "请输入所有必填栏位。",
    watchInvestRelatedVideo: "请观看投资相关影片。",
    selectCountry: "请选择一个国家或地区。",
    phoneFormatWrong: "手机号码无效，请更正您的电话号码。",
    demoRgCode10017: "手机验证码不正确",
    demoRgCode10018: "电邮验证码不正确",
    demoRgCode200: "你已成功登记模拟账户",
    realRgCode10029: "手机验证码不正确",
    realRgCode10033: "电邮验证码不正确",
    realRgCode10032: "手机号码已被注册",
    realRgCode10031: "邮箱已被注册",
    realRgCode200: "你已成功登记真实账户",
    networkDelay: "操作逾时，请再试一次",
    networkTimeout: "操作逾时，请再试一次",
    canTypeSymbolForPhone: "电话号码不能输入符号",
    noInternet: "网路不好，请稍后重试",
    accountDisabled: "用户无效",
    wrongToken: "验证码错误，请重新登录"
  },
  menu: {
    home: "首页",
    trend: "动向",
    trade: "行情",
    information: "信息",
    account: "我的",
  },
  homePage: {
    updateApp: "请更新至最新版本",
    createAccount: "立即开户",
    trend: "动向",
    latestNotification: "最新公告",
    newsNotification: "消息公告",
    todayTopics: "金价动态",
    morningNews: "早讯",
    latestNews: "最新活动",
    financialNews: "财经快讯",
    financialCalendar: "财经日历",
    information: "个资讯",
    applyDemoAccount: "申请模拟交易账号",
    applyLiveAccount: "申请真实交易账号",
    investNow: "立即注资",
    contactCustomerService: "联系客服",
  },
  trendPage: {
    // todayTopics: "今日话题",
    // latestNews: "最新活动",
    // latestNotification: "最新公告",
    eveningNews: "晚讯",
    news: "快讯",
    calendar: "日历",
    readMore: "了解更多",
    readLess: "收起",
    events: "事件",
    financialEvents: "财经事件",
    holidayClosure: "假期休市",
    formerValue: "前值",
    predictiveValue: "预测值",
    publishedValue: "公布值",
    affect: "影响",
    affectL: "利多 金银",
    affectD: "利空 金银",
    affectLApi: "利多",
    affectDApi: "利空",
  },
  searchPage: {
    todayNewsTitle: "搜索金价动态",
    latestNewsTitle: "搜索最新活动",
    noSearchFound: "没有找到任何搜索结果。",
    searchPlaceholder: "输入搜索关键字",
  },
  tradePage: {
    priceReminder: "到价提示",
    addPriceReminder: "新增到价提示",
    becomeMember: "成为会员，使用到价提示",
    createRealAccount: "建立真实账号",
    createDemoAccount: "建立模拟账号",
    priceReminderInfo: "到价提示资讯",
    priceReminderInfoContent: "1. 新用户有15天免费使用期。\n2. 成功申请模拟交易账号，有15天免费使用期。\n3. 成功申请真实交易账号，可永久使用。\n4. 同时可以建立12个活跃的提示。\n5.在免费使用期内建立的提示,在过期后仍然生效运作。\n6.在免费使用期过期后，不能再建立新提示。",
    priceReminderInfoContent1: "新用户有15天免费使用期。",
    priceReminderInfoContent2: "成功申请模拟交易账号，有15天免费使用期。",
    priceReminderInfoContent3: "成功申请真实交易账号，可永久使用。",
    priceReminderInfoContent4: "同时可以建立12个活跃的提示。",
    priceReminderInfoContent5: "在免费使用期内建立的提示,在过期后仍然生效运作。",
    priceReminderInfoContent6: "在免费使用期过期后，不能再建立新提示。",
    priceReminderCreated: "已建立到价提示。",
    priceReminderExpired: "到价提示的试用期已经过期。",
    priceReminderLoginNeeded: "请在登录后使用这个功能。",
    timeDemoExpire: "试用期至",
    upgradeToRealAccount: "升级真实账号",
    goToMT4: "前往 MT4",
    promptRecord: "提示记录",
    priceReach: "到价",
    priceUpDown: "涨跌",
    riseTo: "升至",
    riseToInNew: "涨至",
    dropTo: "降至",
    validUntil: "有效期至",
    cancelled: "已取消",
    inactive: "已提示",
    expired: "已过期",
    showPromtRecordTo: "显示提示记录至",
  },
  priceModal: {
    londonGold: "伦敦金",
    londonSilver: "伦敦银",
    dollarIndex: "美元指数",
    priceReach: "到价",
    priceUpDown: "涨跌",
    riseTo: "升至",
    riseToInNew: "涨至",
    dropTo: "降至",
    notificationDate: "通知日期",
    askNotification: "您是否允许接收推送通知？",
    enableNotification: "允许",
    disableNotification: "不允许",
  },
  boarding: {
    setting: "设定",
    login: "登入",
    register: "注册",
    screenAlwaysOn: "屏幕常亮",
    on: "常亮",
    on2: "开启",
    off: "关闭",
    displaySetting: "显示设置",
    language: "语言",
    clearCache: "清除缓存",
    displayModeRed: "红升绿降",
    displayModeGreen: "绿升红降",
    traditionChinese: "繁体中文",
    simplifiedChinese: "简体中文",
    clear: "清除",
    aboutHan: "关于汉声",
    companyProfile: "公司介绍",
    majorEventOfHan: "汉声大事记",
    hansJournal: "汉声期刊",
    contactCustomerService: "联络客服",
    selectCountry: "选择国家/地区",
    enterVerifyCode: "输入认证码",
    sentTo: "已经发送到",
    newCodeSent: " 新的验证码已经送出。",
    inputYourName: "填写你的名字",
    submitRegister: "提交",
    registerSuccess: "你已成功登记。",
    returnHome: "返回首页"
  },
  account: {
    acountInfo: "账号资讯",
    personalInfo: "个人资讯",
    emailSubscription: "短信订阅",
    deleteAccount: "删除账号",
    userTypeDemo: "模拟账号",
    userTypeLive: "真实账号",
    applyForDemoAccount: "申请模拟账号",
    applyRealTradingAccount: "升级真实交易账号",
    enabled: "有效",
    disabled: "无效",
    status: "状态",
    clubPoint: "汉声荟积分",
    accountBalance: "真实账号资金",
    eject: "注资",
    withdraw: "取款",
    bankInfo: "银行资讯",
    agentCenter: "代理中心",
    fundAcount: "注资真实账户",
    transactionHistory: "交易记录",
    todayExchangeRate: "今日汇率",
    live: "真实",
    currentAccountCurrency: "US$",
    tradingAccountNumber: "交易账号",
    name: "姓名",
    email: "电邮",
    gender: "性别",
    male: "男",
    female: "女",
    bankCardNo: "银行卡号码",
    bankBranch: "开户支行",
    bankName: "银行名称",
    bankBranchName: "开户支行",
    accountHolderName: "开户人名称",
    bankCardNumber: "银行卡号码",
    balance: "余额",
    netWorth: "净值",
    availableMargin: "可用保证金",
    fundDetails: "资金明细",
    transAmount: "申请金额",
    handleFee: "手续费",
    currency: "货币",
    exchangeRate: "汇率",
    finish: "完成",
    processing: "处理中",
    canceled: "取消",
    today: "当天",
    lastWeek: "最近1周",
    lastMonth: "最近1个月",
    last3Month: "最近3个月",
    exchangeRateDeposite: "存",
    exchangeRatePick: "取",
    transactionDeposite: "入金申请",
    transactionWithdraw: "出金申请",
    transactionProcessing: "处理中",
    transactionFinish: "完成",
    transactionCancel: "取消",
    subscriptionConfirm: "要开始订阅吗?",
    unsubscribeConfirm: "要取消订阅吗?",
    displayRedConfirm: "要设定为红升绿降吗?",
    displayGreenConfirm: "要设定为绿升红降吗?",
    langTraditionChinese: "繁体中文",
    langSimplifiedChinese: "简体中文",
    langEnglish: "English",
    clearCacheConfirm: "确认要清除缓存吗?",
    deleteAccountConfirm: "确定要删除账号?",
    logoutSuccess: "登出成功",
    confirmLogout: "确认登出?",
    noRecord: "没有记录",
    ib_balance: "账户余额",
  },
  deleteAccount: {
    title: "删除账号",
    content: "亲爱的用户,\n\n\n在您决定删除您的手机应用程式账号之前,我们有以下重要提示:\n\n\n资料移除:\n\n一旦您删除账号,我们将不会保留您在应用程式上输入的任何个人资料和喜好设定。这些资讯将被完全删除,无法恢复。\n\n\n设定丢失:\n\n您在应用程式上的所有个人设定和偏好,例如语言、到价提示等,都将随着账号删除而一并被移除。\n\n\n服务中断:\n\n删除账号后,您将无法再使用该应用程式提供的任何服务。如果您未来仍需使用该服务,请慎重考虑是否要删除账号。\n\n\n客服支援:\n\n如果您在删除账号后仍有任何问题或疑问,欢迎随时联系我们的客户服务部门。我们将竭尽全力提供协助。\n\n\n再次提醒您在删除账号前,请仔细考虑并做好充分准备。如果您有任何疑问,欢迎随时与我们联系。祝您使用愉快!",
    confirmDelete: "确认要删除账号"
  },
  accountLogout: {
    title: "账号已被登出",
    message: "你的账号已在其他装置上登入了。"
  },
  demoAccountSignUp: {
    title: "申请模拟账号",
    name: "真实姓名*",
    namePlaceholder: "输入真实姓名",
    phoneNo: "电话号码*",
    phoneNoPlaceholder: "输入电话号码",
    verifyCodePlaceholder: "填写认证码",
    email: "电子邮箱*",
    emailPlaceholder: "输入电子邮箱",    
    getVerifyCode: "获取验证码",
    resendVerifyCode: "重发验证码",
    verifycodeSent: "验证码已经送出。",
  },
  liveAccountSignUp: {
    title: "申请真实账号",
    agentCode: "代理人及推荐码",
    auth: "身份认证",
    name: "真实姓名*",
    namePlaceholder: "输入真实姓名",
    gender: "性别*",
    genderPlaceholder: "选择性别",
    idDocumentType: "证明文件*",
    idDocumentTypePlaceholder: "选择文件类型",
    idCardFrontPhoto: "身份证正面照*",
    idCardFrontPhotoPlaceholder: "上载身份到正面照",
    idCardBackPhoto: "身份证反面照*",
    idCardBackPhotoPlaceholder: "上载身份证反面照",
    holdingCardPhoto: "手持证件照*",
    holdingCardPhotoPlaceholder: "上载手持证件照",
    bankName: "银行名称*",
    bankNamePlaceholder: "输入银行名称",
    branchOpening: "开户支行*",
    branchOpeningPlaceholder: "输入开户支行",
    bankCardNo: "银行卡号码*",
    bankCardNoPlaceholder: "输入银行卡号码",
    bankCardFacePhoto: "银行卡正面照*",
    bankCardFacePhotoPlaceholder: "上载银行卡正面照",
    agent: "代理人",
    agentPlaceholder: "输入代理人名称",
    referralCode: "推荐码",
    referralCodePlaceholder: "输入推荐码",
    phoneNo: "电话号码*",
    phoneNoPlaceholder: "输入电话号码",
    verifyCodePlaceholder: "填写认证码",
    email: "电子邮箱*",
    emailPlaceholder: "输入电子邮箱",
    watchInvestRelatedVideo: "观看投资相关影片",
    getVerifyCode: "获取验证码",
    selectImageFrom: "从哪里选择图片",
    camera: "相机",
    gallery: "图库",
    idNumber: "证件号码*",
    idNumberPlaceholder: "输入证件号码",
    bankAccountName: "银行户口名称*",
    bankAccountNamePlaceholder: "输入银行户口名称",
  },
  newPriceReminderScreen: {
    title: "到价提示设定",
    goods: "商品",
    londonGold: "伦敦金",
    londonSilver: "伦敦银",
    dollarIndex: "美元指数",
    dollar: "美元",
    promptMode: "提示方式",
    priceReminder: "到价提示",
    priceAlert: "涨跌提示",
    promptCondition: "提示条件",
    valueGridIncrease: "价格升至",
    valueGridDecrease: "价格降至",
    latestPrice: "最新价",
    riseTo: "涨至",
    dropTo: "降至",
    riseToPlaceholder: "高于最新价2-50美元。",
    dropToPlaceholder: "低于最新价2-50美元。",
    validPeriod: "有效期",
    validToday: "当天有效",
    validThisWeek: "当周有效",
    validThisMonth: "当月有效",
    validThisYear: "当年有效",
    establish: "建立",
    cancelConfirm: "确定要取消吗?"
  },
  information: {
    title: "信息中心",
    all: "全部",
    notification: "消息通知",
    eventNotificaiont: "活动通知",
    transactionNotice: "出入金通知",
    systemNotification: "系统通知",
  },
  // from this line, below texts from template screen of project. Just leave it here.
  welcomeScreen: {
    postscript:
      "psst  — This probably isn't what your app looks like. (Unless your designer handed you these screens, and in that case, ship it!)",
    readyForLaunch: "Your app, almost ready for launch!",
    exciting: "(ohh, this is exciting!)",
    letsGo: "Let's go!",
  },
  errorScreen: {
    title: "Something went wrong!",
    errorTitle: "Error title",
    errorContent: "Error content",
    reset: "RESET APP",
  },
  emptyStateComponent: {
    generic: {
      heading: "So empty... so sad",
      content: "No data found yet. Try clicking the button to refresh or reload the app.",
      button: "Let's try this again",
    },
  },

  loginScreen: {
    signIn: "Sign In",
    enterDetails:
      "Enter your details below to unlock top secret info. You'll never guess what we've got waiting. Or maybe you will; it's not rocket science here.",
    emailFieldLabel: "Email",
    passwordFieldLabel: "Password",
    emailFieldPlaceholder: "Enter your email address",
    passwordFieldPlaceholder: "Super secret password here",
    tapToSignIn: "Tap to sign in!",
    hint: "Hint: you can use any email address and your favorite password :)",
  },
  demoNavigator: {
    componentsTab: "Components",
    debugTab: "Debug",
    communityTab: "Community",
    podcastListTab: "Podcast",
  },
  demoCommunityScreen: {
    title: "Connect with the community",
    tagLine:
      "Plug in to Infinite Red's community of React Native engineers and level up your app development with us!",
    joinUsOnSlackTitle: "Join us on Slack",
    joinUsOnSlack:
      "Wish there was a place to connect with React Native engineers around the world? Join the conversation in the Infinite Red Community Slack! Our growing community is a safe space to ask questions, learn from others, and grow your network.",
    joinSlackLink: "Join the Slack Community",
    makeIgniteEvenBetterTitle: "Make Ignite even better",
    makeIgniteEvenBetter:
      "Have an idea to make Ignite even better? We're happy to hear that! We're always looking for others who want to help us build the best React Native tooling out there. Join us over on GitHub to join us in building the future of Ignite.",
    contributeToIgniteLink: "Contribute to Ignite",
    theLatestInReactNativeTitle: "The latest in React Native",
    theLatestInReactNative: "We're here to keep you current on all React Native has to offer.",
    reactNativeRadioLink: "React Native Radio",
    reactNativeNewsletterLink: "React Native Newsletter",
    reactNativeLiveLink: "React Native Live",
    chainReactConferenceLink: "Chain React Conference",
    hireUsTitle: "Hire Infinite Red for your next project",
    hireUs:
      "Whether it's running a full project or getting teams up to speed with our hands-on training, Infinite Red can help with just about any React Native project.",
    hireUsLink: "Send us a message",
  },
  demoShowroomScreen: {
    jumpStart: "Components to jump start your project!",
    lorem2Sentences:
      "Nulla cupidatat deserunt amet quis aliquip nostrud do adipisicing. Adipisicing excepteur elit laborum Lorem adipisicing do duis.",
    demoHeaderTxExample: "Yay",
    demoViaTxProp: "Via `tx` Prop",
    demoViaSpecifiedTxProp: "Via `{{prop}}Tx` Prop",
  },
  demoDebugScreen: {
    howTo: "HOW TO",
    title: "Debug",
    tagLine:
      "Congratulations, you've got a very advanced React Native app template here.  Take advantage of this boilerplate!",
    reactotron: "Send to Reactotron",
    reportBugs: "Report Bugs",
    demoList: "Demo List",
    demoPodcastList: "Demo Podcast List",
    androidReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running, run adb reverse tcp:9090 tcp:9090 from your terminal, and reload the app.",
    iosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    macosReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    webReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
    windowsReactotronHint:
      "If this doesn't work, ensure the Reactotron desktop app is running and reload app.",
  },
  demoPodcastListScreen: {
    title: "React Native Radio episodes",
    onlyFavorites: "Only Show Favorites",
    favoriteButton: "Favorite",
    unfavoriteButton: "Unfavorite",
    accessibility: {
      cardHint:
        "Double tap to listen to the episode. Double tap and hold to {{action}} this episode.",
      switch: "Switch on to only show favorites",
      favoriteAction: "Toggle Favorite",
      favoriteIcon: "Episode not favorited",
      unfavoriteIcon: "Episode favorited",
      publishLabel: "Published {{date}}",
      durationLabel: "Duration: {{hours}} hours {{minutes}} minutes {{seconds}} seconds",
    },
    noFavoritesEmptyState: {
      heading: "This looks a bit empty",
      content:
        "No favorites have been added yet. Tap the heart on an episode to add it to your favorites!",
    },
  },
}

export default sc
export type Translations = typeof sc