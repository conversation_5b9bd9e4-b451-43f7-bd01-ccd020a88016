import { Text } from "."
import { COLUMN } from "../theme/mStyle"
import * as React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { colors } from "../theme"

const OptionalRoot: ViewStyle = {
    ...COLUMN,
    alignItems: 'center',
}

const CurrentLine: ViewStyle = {
    width: "100%",
    height: 5,
    marginTop: 5,
    borderRadius: 50,
    backgroundColor: colors.mine.primary,
}

const HiddenLine: ViewStyle = {
    width: "100%",
    height: 5,
    marginTop: 5,
    borderRadius: 50,
    backgroundColor: colors.palette.white,
}


export function MOptionalText(props: any) {
    const { 
        textStyle, 
        containerStyle, 
        text, 
        onPress, 
        clickabled = true,
        isSelected } = props

    return (
        <TouchableOpacity 
            activeOpacity={clickabled ? 0.8 : 1}
            onPress={() => {
                if (clickabled) {
                    onPress()
                }
            }}            
            style={[OptionalRoot, containerStyle]}>
                <Text style={textStyle} text={text} />
                {
                    isSelected ? <View style={CurrentLine} /> : <View style={HiddenLine} />
                }            
        </TouchableOpacity>
    )
}


