import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { translate } from "../../i18n"
import { View, Image, ImageStyle } from "react-native"
import { FLEX, MARGIN_DF_RIGHT, ROW_CENTER, TEXT_CENTER, W_100P } from "../../theme/mStyle"
import { Dimen } from "../../theme/dimen"
import { DF_CONTENT, TEXT_SCREEN_TITLE } from "../../theme/baseStyle"
import { MIcon } from "../../components/MIcon"
import { Helper } from "app/utils/helper"
import WebView from "react-native-webview"
import { BackNavComponent } from "app/components/BackNavComponent"
import { AppStorage } from "app/utils/appStorage"
import { useNetInfo } from "@react-native-community/netinfo"


const ImageEvent: ImageStyle = {
  width: Dimen.screenWidth - Dimen.padding.base*2, 
  height: Dimen.screenHeight*0.4,
  marginHorizontal: Dimen.padding.base,
  borderRadius: Dimen.borderRadiusLarge,
  overflow: "hidden"  
}
interface MajorEventDetailsScreenProps extends AppStackScreenProps<"MajorEventDetails"> {}
export const MajorEventDetailsScreen: FC<MajorEventDetailsScreenProps> = observer(function MajorEventDetailsScreen(_props) {
  
  const [majorEvent, setMajorEvent] = useState(null)
  const [eventImage, setEventImage] = useState("")
  const prepareData = () => {
    if (_props.route.params && _props.route.params.event) {
      setMajorEvent(() => _props.route.params.event)
    }
    if (_props.route.params && _props.route.params.imageTitle) {
      setEventImage(() => _props.route.params.imageTitle)
    }    
  }

  const getUrl = () => {
    const slugType = Helper.getAppConfig().EVENT_SLUG
    const slug = Helper.getValue(majorEvent, "slug")
    const url = Helper.getAppConfig().PORTAL_URL + "/" + AppStorage.appStorage.setting.language.toLocaleLowerCase() + "/" + slugType + slug
    console.log("getNewsDetailsUrl", url)
    return url.replace(" ", "")
  }

  const buildHtml = () => {
    if (majorEvent) {
      if (majorEvent.css) {
        return '<style>' + majorEvent.css + '</style>' + Helper.getValue(majorEvent, "content")
      } else {
        return Helper.getValue(majorEvent, "content")
      }
    } else {
      return ""
    }
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  const webviewRef = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
        if (webviewRef.current) {
          webviewRef.current.reload()
        }
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    prepareData()
  }, [_props.route])


  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ flex: 1 }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        title={translate("boarding.majorEventOfHan")} />     
      {/* <Image 
          resizeMode="cover"
          source={{ uri: eventImage }} 
          style={ImageEvent} /> */}
      <View style={[DF_CONTENT, {flex: 1}]}>
        {/* <Text style={[TEXT_SCREEN_TITLE, TEXT_COLOR_PRIMARY]} text={Helper.getValue(majorEvent, "title")} /> */}
        {/* <Text style={[TEXT_SMALL_0, MARGIN_DF_TOP]} text={majorEvent?.videoTC} />         */}
        {majorEvent != null && 
        <WebView
              ref={webviewRef}
              style={{width: Dimen.screenWidth - 2 * Dimen.padding.base, height: Dimen.screenHeight - 100}}
              originWhitelist={['*']}
              scalesPageToFit={true}
              allowsJavaScript={true}            
              injectedJavaScript={`const meta = document.createElement('meta'); meta.setAttribute('content', 'width=width, initial-scale=1, maximum-scale=1, user-scalable=2.0'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta); `}
              // source={{ html: Helper.getValue(data, "content") }}
              source={{ uri: getUrl() }}
          />
        // <HTMLView
        //   value={Helper.getValue(majorEvent, "content")}/>
        }
      </View>
    </Screen>
  )
})
