import { action, makeObservable, observable } from "mobx"
import { load, remove, save } from "./storage"
import { AppStorage } from "./appStorage"
import { Helper } from "./helper"

interface IAppLinkStorage {
  screenName: string,
  extraData: string,
  navigation: any
}

class AppLinkStorageImpl {  
  appLinkStorage: IAppLinkStorage = {
    screenName: "",
    extraData: "", 
    navigation: null
  }

    constructor() {
        makeObservable(this, {
            appLinkStorage: observable,
            setScreenName: action,
            getScreenName: action,
            getExtraData: action,
            handleDeepLink: action,
            startToGo: action,
            clearScreenName: action,
            setNavigation: action,
        })
    }

    setNavigation(navigation: any) {
        this.appLinkStorage.navigation = navigation
    }

    async clearScreenName() {
        this.appLinkStorage.screenName = ""
        this.appLinkStorage.extraData = ""
        await this.saveToStorage()
    }

    async setScreenName(screenName: string, mExtraData = "") {
        this.appLinkStorage.screenName = screenName
        this.appLinkStorage.extraData = mExtraData
        await this.saveToStorage()
    }

    getScreenName() {
        return this.appLinkStorage.screenName
    }

    getExtraData() {
        return this.appLinkStorage.extraData
    }

    startToGo(navigation) {
        console.log("startToGo")
        if (this.appLinkStorage.screenName === "Information") {
            console.log("startToGo Information")
            navigation.navigate("Information", {screen: "InformationTransaction"})
            AppStorage.setCurrentInformationScreenIndex(4)
        } else if (this.appLinkStorage.screenName === "NewsDetails") {
            console.log("startToGo NewsDetails")
            navigation.navigate("NewsDetails", {postId: this.appLinkStorage.extraData})            
        } else if (this.appLinkStorage.screenName === "TransactionHistory") {
            console.log("startToGo TransactionHistory")
            navigation.navigate("TransactionHistory")
        }
        this.clearScreenName()
    }

    async handleDeepLink(url: string) {         
        console.log("handlingDeepLink", url)
        if (url && url.includes("sinosounduat://")) {
            const path = url.replace("sinosounduat://", "")
            if (path.includes("inbox3")) {
                this.setScreenName("Information")            
            } else if (path.includes("post/")) {
                this.setScreenName("NewsDetails", path.split("post/")[1])
            } else if (path.includes("ticketstatement")) {
                if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
                    this.setScreenName("TransactionHistory")
                }
            }
        } else if (url && url.includes("sinosound://")) {
            const path = url.replace("sinosound://", "")
            if (path.includes("inbox3")) {
                this.setScreenName("Information")            
            } else if (path.includes("post/")) {
                this.setScreenName("NewsDetails", path.split("post/")[1])
            } else if (path.includes("ticketstatement")) {
                if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
                    this.setScreenName("TransactionHistory")
                }
            }
        }
    }

    handleDeepLinkInsideApp(url: string, navigation: any) {
        console.log("handlingDeepLinkInsideApp", url)
        if (url) {            
            if (url.includes("sinosounduat://")) {
                const path = url.replace("sinosounduat://", "")
                if (path.includes("home")) {
                    navigation.navigate("MainStack", {screen: "Home"})
                } else if (path.includes("inbox3")) {
                    navigation.navigate("Information", {screen: "InformationTransaction"})
                    AppStorage.setCurrentInformationScreenIndex(4)
                } else if (path.includes("post/")) {
                    navigation.navigate("NewsDetails", {postId: path.split("post/")[1]})
                } else if (path.includes("ticketstatement")) {
                    if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
                        navigation.navigate("TransactionHistory")
                    }
                }
            } else if (url.includes("sinosound://")) {
                const path = url.replace("sinosound://", "")
                if (path.includes("home")) {
                    navigation.navigate("MainStack", {screen: "Home"})
                } else if (path.includes("inbox3")) {
                    navigation.navigate("Information", {screen: "InformationTransaction"})
                    AppStorage.setCurrentInformationScreenIndex(4)
                } else if (path.includes("post/")) {
                    navigation.navigate("NewsDetails", {postId: path.split("post/")[1]})
                } else if (path.includes("ticketstatement")) {
                    if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
                        navigation.navigate("TransactionHistory")
                    }
                }
            } else if (url.includes("http://") || url.includes("https://")) {
                navigation.navigate("WebviewInformation", {
                    title: "",
                    webUrl: url
                })
            }
        }
    }

  
    async prepareForStart(callback: any) {
        this.initializeFromAsyncStorage().then(() => {
        callback()
        })
    }
    
    async initializeFromAsyncStorage() {
        const result = await load("appLinkStorage")
        if (result) {      
        this.appLinkStorage = result as IAppLinkStorage
        console.log(JSON.stringify(this.appLinkStorage))      
        }
    }

    async saveToStorage () {
        await save("appStorage", this.appLinkStorage)
        console.log("save appStorage successfully")
    }

    async saveToStorageWithCallback (callback: any) {
        save("appStorage", this.appLinkStorage).then(() => {
        console.log("save appStorage successfully")
        callback()
        })
    }

    reset(callback: any = null) {    
        remove("appStorage").then(() => {
            this.appLinkStorage = {
                screenName: "",        
            }
            if (callback) {
                callback()
            }
        })
    }
}

const AppLinkStorage = new AppLinkStorageImpl()
AppLinkStorage.initializeFromAsyncStorage()
export { AppLinkStorage }
