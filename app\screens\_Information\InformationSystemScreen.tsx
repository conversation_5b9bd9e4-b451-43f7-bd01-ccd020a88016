import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { FlatList, View } from "react-native"
import { CardNews } from "../_Home/CardNews"
import { getAllInboxMessage, getAllPosts } from "app/api/model"
import { RefreshStorage } from "app/utils/RefreshStorage"
import { useIsFocused } from "@react-navigation/native"
import { Api } from "app/api/api"
import { Dimen } from "app/theme/dimen"
import { HIDE_PRELOAD_CONTAINER, TEXT_CONTENT } from "app/theme/baseStyle"
import { CardInboxMessage } from "./CardInboxMessage"
import { AppLinkStorage } from "app/utils/appLinkStorage"
import { useNetInfo } from "@react-native-community/netinfo"
import { AppStorage } from "app/utils/appStorage"

interface InformationSystemScreenProps extends AppStackScreenProps<"InformationSystem"> {}

export const InformationSystemScreen: FC<InformationSystemScreenProps> = observer(function InformationSystemScreen(_props) {
  const [posts, setPosts] = useState([])
  const [refreshing, setRefreshing] = useState(true)
  const currentPage = useRef(0)
  const hasNext = useRef(false)

  // right value = 6
  // test load more = 0
  const loadPosts = async (isLoadMore = false) => {
    if (!AppStorage.isNetworkConnected()) {
      AppStorage.hideLoading()
      setRefreshing(false)
      return
    }
    if (isLoadMore && hasNext.current) {
      currentPage.current += 1
    } else {
      currentPage.current = 1
    }
    getAllInboxMessage(
      4,
      currentPage.current,
      Api.postsPerPage,
      (data) => {
        AppStorage.hideLoading()
        setRefreshing(false)
        hasNext.current = data.hasNext
        if (isLoadMore) {
          const tempList = posts
          data.results.map((item) => {
            tempList.push(item)
          })        
          setPosts(JSON.parse(JSON.stringify(tempList)))
        } else {          
          setPosts(JSON.parse(JSON.stringify(data.results)))
        }
        console.log("loadPosts " + data.results.length + " - " + hasNext.current)
        resetTimeout()
      },
      (error) => {
        AppStorage.hideLoading()
        setRefreshing(false)
        if (!isLoadMore) {
          setPosts(() => [])
        }
        console.log("error: " + error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }
  

  // start of count down to refresh feed
  // remember call reset timeout when user reload list
  const isScreenShown = useIsFocused()
  const timeoutRef = useRef(null)
  const resetTimeout = (delayTime = Api.reloadTime) => {
    console.log("resetTimeout: " + delayTime)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    RefreshStorage.updateScreenSetTime("InformationSystem")
    timeoutRef.current = setTimeout(() => {
      console.log("reload posts latest news")
      setRefreshing(true)
      loadPosts()
      // resetTimeout()
    }, delayTime*1000)
  }

  const updatePostRead = (postId) => {
    console.log("updatePostRead: " + postId)
    const tempList = posts
    tempList.map((item) => {
      if (item.id == postId) {
        item.isRead = "Y"
      }
    })
    setPosts(JSON.parse(JSON.stringify(tempList)))  
  }

  useEffect(() => {
    console.log("LatestNewsScreen isScreenShown: ", isScreenShown)
    if (isScreenShown) {
      if (RefreshStorage.screenNameAdded("InformationSystem")) {
        if (RefreshStorage.shouldRefreshScreen("InformationSystem")) {
          loadPosts()
          resetTimeout()
        } else {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          resetTimeout(RefreshStorage.getDiffSeconds("LatestNews"))
        }
      } else {
        resetTimeout()
      }
    }
  }, [isScreenShown])
  // end of count down to refresh feed

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        loadPosts()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    AppStorage.showLoading()
    loadPosts()
  }, [])

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["bottom"]}>
      {
        posts.length <= 0 && !refreshing &&
        <Text style={[TEXT_CONTENT, { textAlign: "center", marginTop: 20 }]} tx="errors.noRelatedData"/> 
      }
      {
        posts.length > 0 && 
        <FlatList 
          data={posts}        
          onRefresh={() => {
            setRefreshing(true)
            loadPosts()
          }}       
          initialNumToRender={Api.postsPerPage}
          refreshing={refreshing}
          keyExtractor={item => item.id.toString()}
          extraData={posts}
          ListFooterComponent={<View style={{ height: 100 }} />}
          onEndReached={() => {
            console.log("onEndReached latest news " + hasNext.current + " " + posts.length)
            if (hasNext.current && posts.length > 0) {
              setRefreshing(true)
              loadPosts(true)
            }
          }}
          onEndReachedThreshold={0.5}
          renderItem={({item}) => {
            return (
              <View style={{width: Dimen.screenWidth - 2*Dimen.padding.base}}>
                <CardInboxMessage 
                  onPress={(post) => {
                    if (!AppStorage.isNetworkConnected()) {
                      return
                    }
                    updatePostRead(post.id)
                    if (post.postId) {
                      _props.navigation.navigate("NewsDetails", {postId: post.postId})
                    } else {
                      AppLinkStorage.handleDeepLinkInsideApp(post.deepLink, _props.navigation)
                    }
                  }}
                  key={item.id}                  
                  data={item} />
              </View>
            )
          }}
        />
      }
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_CONTAINER}></View>}
    </Screen>
  )
})
