
import moment from "moment"
import { Api, ApiService } from "../api/api"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { translate } from "app/i18n"
import { Platform } from "react-native"

export const getBanners = async (onSuccess, onFail) => {
  ApiService.callGetRequestNoToken(
    Api.endpoint.banner.getBanners,
    {},
    (response) => {
      onSuccess(response)
    },
    (error) => {
      onFail(error)
    }
  )
}

export const searchPosts = async (mKeyword = "", postTypeId = 0, onSuccess, onFail) => {
  ApiService.callPostRequestNoToken(
    Api.endpoint.posts.search,
    {
      keyword: mKeyword,
      postTypeId,
    },
    (response) => {
      onSuccess(response)
    },
    (error) => {
      onFail(error)
    }
  )
}


export const getAllPosts = async (postTypeId = 0, page = 0, pageSize = 0, mStatus = 1, onSuccess, onFail) => {
  const requestData = {
    status: mStatus
  }
  if (postTypeId != 0) {
    requestData["postTypeId"] = postTypeId
  }
  if (page != 0) {
    requestData["page"] = page
  }
  if (pageSize != 0) {
    requestData["pageSize"] = pageSize
  }
  ApiService.callGetRequestNoToken(
    Api.endpoint.posts.getAllPosts,
    requestData,
    (response) => {
      onSuccess(response)
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getPostById = async (postId, onSuccess, onFail) => {
  ApiService.callGetRequestNoToken(
    Api.endpoint.posts.getPostById + "/" + postId,
    {},
    (response) => {
      onSuccess(response)
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getMarketNews = async (page = 1, mDate = null, onSuccess, onFail) => {
  const requestData = {
    page: page + "",
    row: Api.postsPerPage,
    ip: AppStorage.getPublicIp(),
  }
  if (mDate != null) {
    requestData["date"] = mDate
  }
  console.log('requestData:', requestData)
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.getMarketNews,
    requestData,
    (response) => {
      if (response.state) {
        if (response.state == 10000) {
          onSuccess(response)
        } else {
          onFail(response.msg)
        }
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getCalendarSinoSound = async (mDate = moment().format(Helper.dateFormateList.dateApi), onSuccess, onFail, showMessage = true) => {
  const requestData = {
    date: mDate,
    ip: AppStorage.getPublicIp(),
  }
  console.log('requestData:', requestData)
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.getCalendar,
    requestData,
    (response) => {
      onSuccess(response)
    },
    (error) => {
      onFail(error)
    },
    showMessage
  )
}

export const requestLogin = async (
  mAreaCode: string,
  mPhone: string,
  onContinueLoginFlow: (arg0: any) => void,
  onContinueRegisterFlow: (arg0: any) => void,
  onFail: (arg0: any) => void) => {
  const rBody = {
    areaCode: mAreaCode.replace("+", ""),
    mobile: mPhone,
    ip: AppStorage.getPublicIp(),
  }
  AppStorage.showLoading()
  ApiService.callPostRequestNoToken(
    Api.endpoint.boarding.requestLogin,
    rBody,
    (response) => {
      AppStorage.hideLoading()
      if (response.Code == 200) {
        onContinueLoginFlow(response)
      } else if (response.Code == -201) {
        onContinueRegisterFlow(response)
      } else {
        onFail(response)
      }
    },
    (error) => {
      AppStorage.hideLoading()
      onFail(error)
    }
  )
}

export const userLogin = async (
  mAreaCode: string,
  mPhone: string,
  mOtp: string,
  mDeviceId: string,
  onSuccess: (arg0: any) => void,
  onWrongOtp: (arg0: any) => void,
  onFail: (arg0: any) => void) => {
  const rBody = {
    areaCode: mAreaCode.replace("+", ""),
    mobile: mPhone,
    otp: mOtp,
    ip: AppStorage.getPublicIp(),
    deviceId: mDeviceId
  }
  AppStorage.showLoading()
  ApiService.callPostRequestNoToken(
    Api.endpoint.boarding.login,
    rBody,
    async (response) => {
      AppStorage.hideLoading()
      // console.log('userLogin:', JSON.stringify(response))
      if (response.Code == 200) {
        if (response.accessToken != null && response.accessToken != "") {
          await AppStorage.setUserDevice({
            id: response.id,
            clientId: response.clientId
          })
          await AppStorage.updateUserId(response.id)
          await AppStorage.setAccessToken(response.accessToken)
          await AppStorage.setRefreshToken(response.refreshToken)
          onSuccess(response)
        }
      } else if (response.Code == -201) {
        AppStorage.hideLoading()
        onWrongOtp(response.Message)
      } else {
        AppStorage.hideLoading()
        onFail(response)
      }
    },
    (error) => {
      AppStorage.hideLoading()
      onFail(error)
    }
  )
}

export const requestRegisterPortalUser = async (
  mAreaCode: string,
  mPhone: string,
  onSuccess: (arg0: any) => void,
  onFail: (arg0: any) => void) => {
  const rBody = {
    areaCode: mAreaCode.replace("+", ""),
    mobile: mPhone,
    ip: AppStorage.getPublicIp(),
  }
  AppStorage.showLoading()
  ApiService.callPostRequestNoToken(
    Api.endpoint.boarding.requestRegisterPortalUser,
    rBody,
    (response) => {
      AppStorage.hideLoading()
      if (ApiService.isApiCodeSuccess(response)) {
        onSuccess(response)
      } else {
        onFail(response)
      }
    },
    (error) => {
      AppStorage.hideLoading()
      onFail(error)
    }
  )
}

export const createUser = async (
  mAreaCode: string,
  mPhone: string,
  mOtp: string,
  mName: string,
  onSuccess: (arg0: any) => void,
  onPhoneExisted: (arg0: any) => void,
  onFail: (arg0: any) => void) => {
  const rBody = {
    areaCode: mAreaCode.replace("+", ""),
    mobile: mPhone,
    otp: mOtp,
    ip: AppStorage.getPublicIp(),
    name: mName
  }
  AppStorage.showLoading()
  ApiService.callPostRequestNoToken(
    Api.endpoint.boarding.createUser,
    rBody,
    (response) => {
      AppStorage.hideLoading()
      if (response.id != null && response.id != undefined && response.id != 0) {
        onSuccess(response)
      } else if (response.Code == -201) {
        if (response.message) {
          onPhoneExisted(response.message)
        } else if (response.Message) {
          onPhoneExisted(response.Message)
        } else {
          onPhoneExisted("")
        }
      } else {
        onFail(response)
      }
    },
    (error) => {
      console.log("error here")
      AppStorage.hideLoading()
      onFail(error)
    }
  )
}

export const getUserWithoutBanner = async (userId, onSuccess, onFail, onWrongToken) => {
  if (userId == null || userId == "" || userId == 0) {
    //onFail("User Id is empty")
    return
  }
  const rsBody = {
    id: userId,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.user.getUserWithBanner,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        AppStorage.updateSettingFromUserInfo(response.User.settings)
        onSuccess(response)
      } else {
        onFail(response.message)
      }
    },
    (error) => {
      console.log(error)
      onFail(translate("errors.standard"))
    },
    () => {
      onWrongToken()
    }
  )
}

export const deleteUser = async (userId, onSuccess, onFail, onWrongToken) => {
  const endpoint = Api.endpoint.user.deleteUser + "/" + userId
  ApiService.callDeleteRequest(
    endpoint,
    {},
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getMyAccount = async (onSuccess, onFail, onWrongToken) => {
  ApiService.callGetRequest(
    Api.endpoint.user.getMySimpleInfo,
    {},
    (response) => {
      if (response && response.code && response.code == -200) {
        //onFail(response.message)
      } else {
        AppStorage.updateUserId(response.id)
        AppStorage.setUserDevice(response)
        AppStorage.addLoggedInUser(response)
        onSuccess(response)
      }
    },
    (error) => {
      console.log(error)
      //onFail(translate("errors.unAuthorize"));
    },
    () => {
      onWrongToken()
    }
  )
}

export const createPriceReminder = async (
  mItem = "GOLD",
  mType = 1,
  mCondition = "rise",
  mPrice = "0",
  mDwm = "w",
  onSuccess,
  onFail,
  onWrongToken) => {
    // mItem: GOLD SILVER USDINDEX
    // type: 1 2
    // condition: rise drop
    // dwm W M
    if (mPrice == "0" || mPrice == "") {
      // ApiService.showMessageBox(translate("errors.addPriceReminder"))
      onFail(translate("errors.addPriceReminder"))
      return
    }
    const rsBody = {
      item: mItem,
      type: mType,
      condition: mCondition,      
      dwm: mDwm,
      ip: AppStorage.getPublicIp(),
    }
    if (mType == 1) {
      rsBody["price"] = mPrice    
    }
    if (mType == 2) {
      rsBody["target"] = mPrice        
    }
    ApiService.callPostRequest(
      Api.endpoint.priceReminder.create, 
      rsBody, 
      (response) => {
        console.log('createPriceReminder:', response)
        if (response.Code == 200) {
          onSuccess(response)
        } else if (response.Code == -201) {
          onFail(response.Message)
        } else {
          if (response.Message) {
            onFail(response.Message)
          } else {
            onFail(translate("errors.standard"))
          }
        }
      },
      (error) => {
        onFail(error)
      },
      () => {
        onWrongToken()
      }
    )
}

export const getPriceReminders = async (mstatus = "", onSuccess, onFail, onWrongToken) => {
  ApiService.callGetRequest(
    Api.endpoint.priceReminder.get,
    mstatus == "" ? {} : { status: mstatus },
    (response) => {
      if (response.results) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const deletePriceReminder = async (mId = 0, onSuccess, onFail, onWrongToken) => {
  if (mId == 0) {
    ApiService.showMessageBox(translate("errors.invalidReminderId"))
    onFail(translate("errors.invalidReminderId"))
  } else {
    ApiService.callDeleteRequest(
      Api.endpoint.priceReminder.delete + "/" + mId,
      {},
      (response) => {
        onSuccess(response)
      },
      (error) => {
        onFail(error)
      },
      () => {
        onWrongToken()
      }
    )
  }
}

export const getTransactionHistory = async (mClientId = "", timeRangeType = 1, mPage = 1, pageSize = 20, onSuccess, onFail, onWrongToken) => {
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getTransactionHistory,
    {
      clientId: mClientId,
      timeRangeType,
      ticketType: 0,
      page: mPage,
      row: pageSize,
      ip: AppStorage.getPublicIp(),
    },
    (response) => {
      if (response.data) {
        onSuccess(response.data)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const subscribeSino = async (isSubscribed, mClientId, onSuccess, onFail, onWrongToken) => {
  ApiService.callPostRequest(
    isSubscribed ? Api.endpoint.sinoSound.subscribe : Api.endpoint.sinoSound.unsubscribe,
    {
      clientId: mClientId,
      ip: AppStorage.getPublicIp(),
    },
    (response) => {
      console.log('subscribeSino:', JSON.stringify(response))
      if (response && response.state && response.state == 10000) {
        onSuccess(response)
      } else {
        onFail(response.msg)
      }
    },
    (error) => {
      console.log('subscribeSino:', JSON.stringify(error))
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const updateUserSettings = async (mSettings, userId, onSuccess, onFail, onWrongToken) => {
  if (userId == "0" || userId == 0 || userId == "") {
    ApiService.showMessageBox(translate("errors.unAuthorize"))
    onFail(translate("errors.unAuthorize"))
    return
  }
  const rsBody = mSettings
  rsBody["id"] = userId

  ApiService.callPutRequest(
    Api.endpoint.user.updateUser,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getExchangeRate = async (onSuccess, onFail) => {
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.getExchangeRate,
    { ip: AppStorage.getPublicIp() },
    (response) => {
      if (response.state) {
        if (response.state == 10000) {
          onSuccess(response)
        } else {
          onFail(response.msg)
        }
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getCurrentPrice = async (mSymbol, onSuccess, onFail) => {
  const requestData = {
    symbol: mSymbol,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.getCurrentPrice,
    requestData,
    (response) => {
      if (response.state) {
        if (response.state == 10000) {
          onSuccess(response)
        } else {
          console.log('getCurrentPrice:', response)
          onFail(response.msg)
        }
      }
    },
    (error) => {
      console.log('getCurrentPrice:', error)
      onFail(error)
    },
    false
  )
}

export const uploadImage = async (mImageInBase64String, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    img: mImageInBase64String,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequestWithCustomTime(
    Api.endpoint.user.uploadImage,
    rsBody,
    60 * 1000,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      if (error && error.message) {
        onFail(error.message)
      } else {
        onFail(translate("errors.networkDelay"))
      }
    },
    () => {
      onWrongToken()
    }
  )
}

export const getRegisterDemoPhoneCode = async (phoneAreaCode, phoneNo, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    areaCode: phoneAreaCode,
    phone: phoneNo,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getRegisterDemoPhoneCode,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getRegisterDemoEmailCode = async (mEmail, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    email: mEmail,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getRegisterDemoEmailCode,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getRegisterPhoneCode = async (phoneAreaCode, phoneNo, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    areaCode: phoneAreaCode,
    phone: phoneNo,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getRegisterPhoneCode,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getRegisterEmailCode = async (mEmail, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    email: mEmail,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getRegisterEmailCode,
    rsBody,
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getLinkSinoSound = async (clientId, lang, domain, page, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    clientId: clientId,
    lang: lang,
    domain: domain,
    page: page
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getLinkSinoSound,
    rsBody,
    (response) => {
      if (response && response.data) {
        onSuccess(response)
      } else if (response && response.msg)  {
        onFail(response.msg)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getClientLinkSinoSound = async (area_code, phone, lang, domain, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    area_code: area_code,
    phone: phone,
    lang: lang,
    domain: domain,
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getClientLinkSinoSound,
    rsBody,
    (response) => {
      if (response && response.data) {
        onSuccess(response)
      } else if (response && response.msg)  {
        onFail(response.msg)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}

export const getRegLinkSinoSound = async (area_code, phone, lang, domain, onSuccess, onFail, onWrongToken) => {
  const rsBody = {
    area_code: area_code,
    phone: phone,
    lang: lang,
    domain: domain,
  }
  ApiService.callPostRequest(
    Api.endpoint.sinoSound.getRegLinkSinoSound,
    rsBody,
    (response) => {
      if (response && response.data) {
        onSuccess(response)
      } else if (response && response.msg)  {
        onFail(response.msg)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    }
  )
}
export const getGraphLinkSinoSound = async (color, lang, domain, onSuccess, onFail) => {
  const rsBody = {
    color: color,
    lang: lang,
    domain: domain,
  }
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.getGraphLinkSinoSound,
    rsBody,
    (response) => {
      if (response && response.data) {
        onSuccess(response)
      } else if (response && response.msg)  {
        onFail(response.msg)
      }
    },
    (error) => {
      onFail(error)
    }
  )
}
export const createDomainFailedLog = async (url, onSuccess, onFail) => {
  const rsBody = {
    Url: url,
  }
  ApiService.callPostRequestNoToken(
    Api.endpoint.sinoSound.createDomainFailedLog,
    rsBody,
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail()
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const registerLiveAccount = async (data, onSuccess, onFail, onWrongToken) => {
  const rqBody = {
    id: data.id,
    client_name: data.client_name,
    gender: data.gender,
    id_type: data.id_type,
    id_number: data.id_number,
    phone_code: data.phone_code,
    email: data.email,
    email_code: data.email_code,
    bank_account_name: data.bank_account_name,
    bank_name: data.bank_name,
    bank_account: data.bank_account,
    bank_branch: data.bank_branch,
    id_front_img: data.id_front_img,
    id_back_img: data.id_back_img,
    bank_card_img: data.bank_card_img,
    id_handheld_img: data.id_handheld_img,
    agent_number: data.agent_number,
    referral_code: data.referral_code,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.user.registerLiveAccount,
    rqBody,
    (response) => {
      if (response.Code && response.Data && response.Data.msg) {
        onSuccess(response.Data.msg, response.Code)
      } else {
        if (response.Code == 200) {
          onSuccess(response, null)
        } else {
          onFail(response.Data.msg)
        }
      }
    },
    (error) => {
      console.log('registerLiveAccount err:', JSON.stringify(error))
      if (error && error.message) {
        onFail(error.message)
      } else if (error && error.Data) {
        onFail(error.Data.msg)
      } else {
        onFail(error)
      }
    },
    () => {
      onWrongToken()
    }
  )
}

export const registerDemoAccount = async (data, onSuccess, onFail, onWrongToken) => {
  const rqBody = {
    id: data.id,
    client_name: data.client_name,
    phone_code: data.phone_code,
    email: data.email,
    email_code: data.email_code,
    ip: AppStorage.getPublicIp(),
  }
  ApiService.callPostRequest(
    Api.endpoint.user.registerDemoAccount,
    rqBody,
    (response) => {
      if (response.Code && response.Data && response.Data.msg) {
        onSuccess(response.Data.msg, response.Code)
      } else {
        if (response.Code == 200) {
          onSuccess(response, null)
        } else {
          onFail(response.Data.msg)
        }
      }
    },
    (error) => {
      console.log('registerDemoAccount err: ', JSON.stringify(error))
      if (error && error.message) {
        onFail(error.message)
      } else if (error && error.Data) {
        onFail(error.Data.msg)
      } else {
        onFail(error)
      }
    },
    () => {
      onWrongToken()
    }
  )
}

export const getAllInboxMessage = async (type = null, mPage = null, pageSize = null, onSuccess, onFail, onWrongToken) => {
  const rsBody = {}
  if (type != null) {
    rsBody["pushType"] = type
  }
  if (mPage != null) {
    rsBody["page"] = mPage
  }
  if (pageSize != null) {
    rsBody["pageSize"] = pageSize
  }
  ApiService.callGetRequest(
    Api.endpoint.inbox.getAll,
    rsBody,
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    },
    false
  )
}

export const updateInboxBatch = async (onSuccess, onFail, onWrongToken) => {
  ApiService.callPutRequest(
    Api.endpoint.inbox.updateInboxBatch,
    {},
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    },
    false
  )
}

export const updateInboxRead = async (postId, onSuccess, onFail, onWrongToken) => {
  ApiService.callPutRequest(
    Api.endpoint.inbox.updateInboxRead,
    { id: postId },
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    },
    false
  )
}

export const getInboxBatchCount = async (onSuccess, onFail, onWrongToken) => {
  ApiService.callGetRequest(
    Api.endpoint.inbox.getAllInboxBatchCount,
    {},
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    },
    false
  )
}

export const getPopups = async (onSuccess, onFail) => {
  ApiService.callGetRequestNoToken(
    Api.endpoint.popup.getAll,
    { status: "active" },
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const updateDeviceId = async (deviceId, userId, onSuccess, onFail, onWrongToken) => {
  if (userId == "0" || userId == 0 || userId == "") {
    ApiService.showMessageBox(translate("errors.unAuthorize"))
    onFail(translate("errors.unAuthorize"))
    return
  }
  ApiService.callPutRequest(
    Api.endpoint.user.updateDeviceId,
    {
      id: userId,
      deviceId
    },
    (response) => {
      if (response.Code == 200) {
        onSuccess(response)
      } else {
        onFail(response)
      }
    },
    (error) => {
      onFail(error)
    },
    () => {
      onWrongToken()
    },
  )
}

export const getCsUrl = async (onSuccess, onFail) => {
  ApiService.callGetRequestDirectly(
    Api.loadCsUrl(),
    {},
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail(response.Message)
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getAppVersion = async (onSuccess, onFail) => {
  ApiService.callGetRequestNoToken(
    Platform.OS == "android" ? Api.endpoint.params.androidVersion : Api.endpoint.params.iosVersion,
    {},
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail("")
      }
    },
    (error) => {
      onFail(error)
    }
  )
}

export const getAppDownloadLink = async (onSuccess, onFail) => {
  ApiService.callGetRequestNoToken(
    Platform.OS == "android" ? Api.endpoint.params.androidDownloadLink : Api.endpoint.params.iosDownloadLink,
    {},
    (response) => {
      if (response) {
        onSuccess(response)
      } else {
        onFail("")
      }
    },
    (error) => {
      onFail(error)
    }
  )
}