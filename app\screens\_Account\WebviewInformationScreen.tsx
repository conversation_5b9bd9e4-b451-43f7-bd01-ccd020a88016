import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { View, ViewStyle } from "react-native"
import { LoadingBox } from "../../components/LoadingBox"
import { FLEX, MARGIN_DF_RIGHT, ROW, ROW_CENTER, TEXT_CENTER, W_100P } from "../../theme/mStyle"
import { Dimen } from "../../theme/dimen"
import WebView from "react-native-webview"
import { TEXT_SCREEN_TITLE } from "../../theme/baseStyle"
import { MIcon } from "../../components/MIcon"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"
import { colors } from "../../theme"


const WebviewStyle: ViewStyle = {
  width: Dimen.screenWidth,
  height: Dimen.screenHeight*0.8,
  marginTop: 0
}

interface WebviewInformationScreenProps extends AppStackScreenProps<"WebviewInformation"> {
  route: {
    params?: {
      title?: string
      webUrl?: string
      showBackText?: boolean
    }
  }
}
export const WebviewInformationScreen: FC<WebviewInformationScreenProps> = observer(function WebviewInformationScreen(_props) {
  
  const [screenTitle, setScreenTitle] = useState("")
  const [url, setUrl] = useState("")
  const [loading, setLoading] = useState(false)

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  const webviewRef = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        if (webviewRef.current) {
          webviewRef.current.reload()
        }
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    if (_props.route.params) {
      setScreenTitle(_props.route.params.title)
      setUrl(_props.route.params.webUrl)
    }
  }, [_props.route])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ flex: 1 }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        title={screenTitle}
        showBackText={_props.route.params?.showBackText ?? false} />     
      <LoadingBox visible={loading} indicatorColor={colors.primary} />
      <WebView
        ref={webviewRef}
        style={[WebviewStyle, { display: loading ? 'none' : 'flex' }]}
        source={{ uri: url }}
        onLoadStart={() => setLoading(true)}
        onLoadEnd={() => setLoading(false)}
      />      
    </Screen>
  )
})