import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Button, Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BTN_CONTAINER, BTN_IN_CARD, BTN_IN_CARD_GREY, COLOR_WHITE, CONT_INPUT, FSIZE_12, FSIZE_12_L, FSIZE_13, TEXT_H4, TEXT_SCREEN_TITLE, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { AI_CENTER, CENTER, COLUMN, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_DF_TOP, MARGIN_L_TOP, MARGIN_S_DF_RIGHT, PADDING_S_DF, ROW, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_ALERT, TEXT_COLOR_GREEN, W_100P } from "../../theme/mStyle"
import { Image, ImageStyle, TextInput, TouchableOpacity, View, ViewStyle } from "react-native"
import { Api, ApiService } from "app/api/api"
import { createPriceReminder, deletePriceReminder, getCurrentPrice, getPriceReminderMinLink, getPriceReminderMaxLink } from "app/api/model"
import { MOptionalText } from "app/components/MOptionalText"
import { AppStorage } from "app/utils/appStorage"
import { Dialog } from "react-native-simple-dialogs"
import { Helper } from "app/utils/helper"
import { BackNavComponent } from "app/components/BackNavComponent"
import { colors } from "app/theme"
import { useNetInfo } from "@react-native-community/netinfo"


const RowTitle: ViewStyle = {
  ...ROW,
  width: Dimen.screenWidth * 0.25
}

const ReminderImage: ImageStyle = {
  width: Dimen.screenWidth - Dimen.padding.base * 2,
  height: (336 / 1200) * (Dimen.screenWidth - Dimen.padding.base * 2),
  borderRadius: 10,
  overflow: "hidden"
}

interface NewPriceReminderScreenProps extends AppStackScreenProps<"NewPriceReminder"> { }
export const NewPriceReminderScreen: FC<NewPriceReminderScreenProps> = observer(function NewPriceReminderScreen(_props) {

  const [selectedGoods, setSelectedGoods] = useState(0)
  const [reminderType, setReminderType] = useState(1)
  const [alertCondition, setAlertCondition] = useState(0)
  const [riseTo, setRiseTo] = useState("")
  const [validPeriod, setValidPeriod] = useState(0)
  const [reminderObj, setReminderObj] = useState(null)
  const [editable, setEditable] = useState(true)
  const [isActive, setActive] = useState(true)
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const [showMessageBox, setShowMessageBox] = useState(false)
  const [messageContent, setMessageContent] = useState("")
  const [priceOpen, setPriceOpen] = useState(null)
  const [priceClose, setPriceClose] = useState(null)
  const [minValue, setMinValue] = useState("2")
  const [maxValue, setMaxValue] = useState("50")
  const selectedGoodsRef = useRef(selectedGoods);


  const prepareData = () => {
    console.log("prepareData")
    if (_props.route.params?.reminderObj) {
      const reminderItem = _props.route.params.reminderObj
      setReminderObj(JSON.parse(JSON.stringify(reminderItem)))
      setEditable(false)
      setActive(!Helper.timeInPast(reminderItem.expiryDate) && reminderItem.status == 'active');
      setReminderType(reminderItem.type)
      if (reminderItem.type == 1) {
        setRiseTo(reminderItem.price)
      } else {
        setRiseTo(reminderItem.target)
      }
      Api.priceReminderItem.map((item, index) => {
        if (item.value == reminderItem.item) {
          setSelectedGoods(index)
          selectedGoodsRef.current = index
        }
      })
      Api.priceReminderCondition.map((item, index) => {
        if (item == reminderItem.condition) {
          setAlertCondition(index)
        }
      })
      Api.priceReminderDwm.map((item, index) => {
        if (item == reminderItem.dwm) {
          setValidPeriod(index)
        }
      })
    }
    loadCurrentPrice(selectedGoodsRef.current)
    loadPriceReminderLimits()
  }

  const getSymbolForPrice = (goodsId) => {
    if (goodsId == 0) {
      return "gold"
    } else if (goodsId == 1) {
      return "silver"
    } else if (goodsId == 2) {
      return "usdindex"
    } else {
      return "gold"
    }
  }

  const loadCurrentPrice = (goodsId) => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    let gId = goodsId
    if (goodsId == null) {
      gId = selectedGoodsRef.current
    }
    getCurrentPrice(
      getSymbolForPrice(gId),
      (response) => {
        console.log("getCurrentPrice: ", response)
        setPriceClose(parseFloat(response.data.close))
        setPriceOpen(parseFloat(response.data.open))
      },
      (error) => {
        console.log("getCurrentPrice error", error)
      }
    )
  }

  const loadPriceReminderLimits = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    
    // Load minimum value
    getPriceReminderMinLink(
      (response) => {
        console.log("getPriceReminderMinLink: ", response)
        if (response && response.value) {
          setMinValue(response.value)
        }
      },
      (error) => {
        console.log("getPriceReminderMinLink error", error)
      }
    )

    // Load maximum value
    getPriceReminderMaxLink(
      (response) => {
        console.log("getPriceReminderMaxLink: ", response)
        if (response && response.value) {
          setMaxValue(response.value)
        }
      },
      (error) => {
        console.log("getPriceReminderMaxLink error", error)
      }
    )
  }

  const doDeletePriceReminder = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    setShowCancelConfirm(false)
    AppStorage.showLoading()
    deletePriceReminder(
      reminderObj.id,
      (response) => {
        AppStorage.hideLoading()
        console.log("deletePriceReminder: ", response.Message)
        // ApiService.showSuccessToast("deletePriceReminder: " + response.Message)
        _props.navigation.goBack()
        if (_props.route.params?.onGoBack) {
          _props.route.params.onGoBack()
        }
      },
      (error) => {
        AppStorage.hideLoading()
        console.log("deletePriceReminder error", error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const addPrice = () => {
    AppStorage.showLoading()
    createPriceReminder(
      Api.priceReminderItem[selectedGoods].value,
      reminderType,
      Api.priceReminderCondition[alertCondition],
      riseTo,
      Api.priceReminderDwm[validPeriod],
      (response) => {
        AppStorage.hideLoading()
        console.log("addPrice: ", response.Message)
        // ApiService.showSuccessToast("addPrice: " + response.Message)
        ApiService.showMessageBoxAndBack(
          translate("tradePage.priceReminderCreated"),
          () => {
            _props.navigation.goBack()
            if (_props.route.params?.onGoBack) {
              _props.route.params.onGoBack()
            }
          }
        )
      },
      (error) => {
        AppStorage.hideLoading()
        setMessageContent(error)
        setShowMessageBox(true)
        console.log("addPrice error", error)

      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const getPriceReminderImage = () => {
    if (reminderType == 1 && alertCondition == 0) {
      return require("../../../assets/images/pr1.png")
    } else if (reminderType == 1 && alertCondition == 1) {
      return require("../../../assets/images/pr2.png")
    } else if (reminderType == 2 && alertCondition == 0) {
      return require("../../../assets/images/pr3.png")
    } else if (reminderType == 2 && alertCondition == 1) {
      return require("../../../assets/images/pr4.png")
    } else {
      return { uri: "https://dummyimage.com/1200x336/000/fff" }
    }
  }

  const getPriceColor = () => {
    if (priceClose && priceOpen) {
      if (priceClose > priceOpen) {
        if (Helper.isDisplayModeRed()) {
          return TEXT_COLOR_ALERT
        } else {
          return TEXT_COLOR_GREEN
        }
      } else if (priceClose < priceOpen) {
        if (Helper.isDisplayModeRed()) {
          return TEXT_COLOR_GREEN
        } else {
          return TEXT_COLOR_ALERT
        }
      }
    }
    return TEXT_COLOR_GREEN

  }

  // useEffect(() => {
  //   selectedGoodsRef.current = selectedGoods
  //   loadCurrentPrice()
  // }, [selectedGoods])

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    prepareData()
    const fetchPriceTimer = setInterval(() => {
      loadCurrentPrice(null)
    }, 60000)
    return () => {
      clearInterval(fetchPriceTimer)
    }
  }, [])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
      keyboardOffset={Dimen.keyboardOffset}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false}
        title={translate("newPriceReminderScreen.title")} />      
      <View style={COLUMN}>
        <View style={[ROW, MARGIN_DF_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} tx="newPriceReminderScreen.goods" />
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => {
                setSelectedGoods(0)
                selectedGoodsRef.current = 0
                loadCurrentPrice(0)
              }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.londonGold")}
              isSelected={selectedGoods == 0} />
          </View>
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => {
                setSelectedGoods(1)
                selectedGoodsRef.current = 1
                loadCurrentPrice(1)
              }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.londonSilver")}
              isSelected={selectedGoods == 1} />
          </View>
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => {
                setSelectedGoods(2)
                selectedGoodsRef.current = 2
                loadCurrentPrice(2)
              }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.dollarIndex")}
              isSelected={selectedGoods == 2} />
          </View>
        </View>
        <View style={[ROW, MARGIN_L_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} tx="newPriceReminderScreen.promptMode" />
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setReminderType(1) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.priceReminder")}
              isSelected={reminderType == 1} />
          </View>
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setReminderType(2) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.priceAlert")}
              isSelected={reminderType == 2} />
          </View>
        </View>
        <View style={[ROW, MARGIN_L_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} tx="newPriceReminderScreen.promptCondition" />
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setAlertCondition(0) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.valueGridIncrease")}
              isSelected={alertCondition == 0} />
          </View>
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setAlertCondition(1) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.valueGridDecrease")}
              isSelected={alertCondition == 1} />
          </View>
        </View>
        <Image style={[ReminderImage, MARGIN_L_TOP]} source={getPriceReminderImage()} />
        <View style={[ROW, MARGIN_L_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} tx="newPriceReminderScreen.latestPrice" />
          <Text style={[MARGIN_DF_RIGHT, FSIZE_12, getPriceColor()]} text={priceClose ? priceClose.toFixed(2) : " - "} />
          <Text style={[MARGIN_DF_RIGHT, FSIZE_12, getPriceColor()]} text={priceClose && priceOpen ? (priceClose - priceOpen).toFixed(2) : "-"} />
          <Text style={[RowTitle, FSIZE_12, getPriceColor()]} text={priceClose && priceOpen ? ((100 * (priceClose - priceOpen)) / priceOpen).toFixed(2) + "%" : "-"} />
        </View>
        <View style={[ROW, AI_CENTER, MARGIN_DF_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} text={alertCondition == 0 ? translate("newPriceReminderScreen.riseTo") : translate("newPriceReminderScreen.dropTo")} />
          <View style={[CONT_INPUT, ROW, FLEX, { marginStart: -10 }]}>
            <TextInput
              allowFontScaling={false}
              keyboardType="numeric"
              value={riseTo}
              editable={editable}
              onChangeText={(text) => { setRiseTo(text) }}
              placeholder={alertCondition == 0 ? 
                translate("newPriceReminderScreen.riseToPlaceholderPrefix") + minValue + "-" + maxValue + translate("newPriceReminderScreen.riseToPlaceholderSuffix") : 
                translate("newPriceReminderScreen.dropToPlaceholderPrefix") + minValue + "-" + maxValue + translate("newPriceReminderScreen.dropToPlaceholderSuffix")}
              placeholderTextColor={colors.mine.placeHolderText}
              style={[TEXT_SMALL, FSIZE_12_L, FLEX, { margin: 0, padding: 0 }]} />
            {riseTo != "" && <Text style={TEXT_SMALL} text={translate("newPriceReminderScreen.dollar")} />}
          </View>
        </View>
        <View style={[ROW, MARGIN_L_TOP]}>
          <Text style={[RowTitle, FSIZE_12_L]} tx="newPriceReminderScreen.validPeriod" />
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setValidPeriod(0) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.validToday")}
              isSelected={validPeriod == 0} />
          </View>
          <View style={RowTitle}>
            <MOptionalText
              clickabled={editable}
              onPress={() => { setValidPeriod(1) }}
              textStyle={FSIZE_12_L}
              text={translate("newPriceReminderScreen.validThisMonth")}
              isSelected={validPeriod == 1} />
          </View>
        </View>
        {isActive && (
          <TouchableOpacity
            style={[BTN_CONTAINER, MARGIN_L_TOP, CENTER]}
            onPress={() => {
              if (!AppStorage.isNetworkConnected()) {
                return
              }
              if (editable) {
                addPrice()
              } else {
                setShowCancelConfirm(true)
              }
            }}>
            <Text
              style={[FSIZE_13, COLOR_WHITE, TEXT_CENTER]}
              text={editable ? translate("newPriceReminderScreen.establish") : translate("common.cancel")} />
          </TouchableOpacity>
        )}
      </View>
      <Dialog
        visible={showCancelConfirm}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => {
          setShowCancelConfirm(false)
        }}>
        <View style={COLUMN}>
          <Text style={[TEXT_SECTION, MARGIN_DF_TOP, TEXT_CENTER]} tx="newPriceReminderScreen.cancelConfirm" />
          <View style={[ROW_CENTER, MARGIN_L_TOP]}>
            <Text
              onPress={() => { setShowCancelConfirm(false) }}
              style={[BTN_IN_CARD_GREY, FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, { height: 40 }]}
              tx="common.cancel" />
            <Text
              onPress={() => { doDeletePriceReminder() }}
              style={[BTN_IN_CARD, FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, { height: 40 }]}
              tx="common.delete" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showMessageBox}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowMessageBox(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={messageContent} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowMessageBox(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX]}
              tx="common.ok" />
          </View>
        </View>
      </Dialog>
    </Screen>
  )
})
