import React from "react"
import { ActivityIndicator, Dimensions, View, ViewStyle } from "react-native";
import { Text } from "./Text";
import { INVISIBLE, VISIBLE } from "app/theme/baseStyle";
import { colors } from "app/theme";


const screenApp = Dimensions.get("screen")

const CONTAINER: ViewStyle = {
    width: screenApp.width,
    height: screenApp.height,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 99999,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center"
}

export function LoadingBox (props: any) {
    const {indicatorColor = colors.mine.primary , size="large", message = "", visible = false} = props        
    return (
        <View style={[CONTAINER, visible ? VISIBLE : INVISIBLE]}>
            <ActivityIndicator size={size} color={indicatorColor} />
            {
                message !== "" && <Text text={message}/>
            }
        </View>
    )
}