import * as React from "react"
import { Text } from "../components"
import { Dimen } from "../theme/dimen"
import { FLEX, MARGIN_DF, MARGIN_DF_RIGHT, MARGIN_S_DF_TOP, ROW, ROW_CENTER, TEXT_BLACK, TEXT_GRAY, W_100P } from "../theme/mStyle"
import { Image, TextStyle, View, ViewStyle } from "react-native"
import { MIcon } from "../components/MIcon"
import { BTN_CONTAINER, BTN_IN_CARD, FSIZE_13, FSIZE_18, TEXT_SECTION } from "../theme/baseStyle"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { colors } from "app/theme"
import { translate } from "app/i18n"
import { Dropdown } from "react-native-element-dropdown"
import { useEffect } from "react"
import { storedUserList } from "app/api/userlist"
import { getUserWithoutBanner } from "app/api/model"
import { observer } from "mobx-react-lite"
import { ApiService } from "app/api/api"

const HeaderContainer: ViewStyle = {
  ...ROW,
  width: Dimen.screenWidth,
  height: 50,
  alignItems: 'center',
  backgroundColor: 'white',
  paddingHorizontal: Dimen.padding.base,
}

const LiveText: TextStyle = {
  borderTopLeftRadius: 6,
  borderBottomLeftRadius: 6,
  overflow: "hidden",
  backgroundColor: colors.mine.primary,
  paddingHorizontal: Dimen.padding.ssm,
  paddingVertical: 2,
  color: colors.palette.white,
  fontSize: Dimen.fontSize.sm,
  textAlign: "center",
}

const LiveAccountBalance: TextStyle = {
  borderTopRightRadius: 6,
  borderBottomRightRadius: 6,
  overflow: "hidden",
  backgroundColor: colors.mine.bgGrey,
  paddingHorizontal: Dimen.padding.ssm,
  paddingVertical: 2,
  color: colors.mine.text,
  fontSize: Dimen.fontSize.sm,
  textAlign: "center",
}

export const Mheader = observer(function Mheader(props: any) {
  const { showSearchIcon = false, onSearchIconClicked, leftTitle = "", navigator, containerStyle } = props
  const [users, setUsers] = React.useState([])
  const [selectedUserId, setSelectedUserId] = React.useState(AppStorage.appStorage.userId)

  const goToLogin = () => {
    navigator.navigate("Login")
  }

  const getAccountBalance = () => {
    if (AppStorage.appStorage.userOtherInfo && AppStorage.appStorage.userOtherInfo.data && AppStorage.appStorage.userOtherInfo.data.balance) {
      return translate("account.currentAccountCurrency") + Helper.formatNumberValue(AppStorage.appStorage.userOtherInfo.data.balance)
    }
    return "-"
  }

  const loadUserInformation = async () => {
    AppStorage.showLoading()
    console.log("accessToken " + AppStorage.appStorage.accessToken)
    if (AppStorage.appStorage.accessToken != '') {
      getUserWithoutBanner(
        AppStorage.appStorage.userId,
        (data) => {
          AppStorage.hideLoading()
          console.log("user data:", data)
          let isSubscribed = false
          AppStorage.setUserDevice(data.User)
          if (Helper.isLiveAccount(data.User)) {
            AppStorage.setUserOtherInfo(data?.OA)
            // subscribe_state
            if (data?.OA) {
              if (data?.OA?.data && data?.OA?.data.subscribe_state == 1) {
                isSubscribed = true
              } else {
                isSubscribed = false
              }
            }
          }
          if (Helper.isDemoUser(data.User)) {
            AppStorage.setUserOtherInfo(data?.Demo)
          }

          AppStorage.updateSetting({
            screenOn: AppStorage.appStorage.setting.screenOn,
            emailSubscription: isSubscribed,
            displayMode: data.User?.settings.redGreen,
            language: data.User?.settings.language,
            clearCache: AppStorage.appStorage.setting.clearCache,
          })
          // navigator.reset({
          //   index: 0,
          //   routes: [{ name: "BottomTab" }],
          // })    
        },
        (error) => {
          AppStorage.hideLoading()
          ApiService.showMessageBox(error)
          console.log("error:", error)
        },
        () => {
          navigator.navigate("Logout")
        }
      )
    } else {
      AppStorage.hideLoading()
    }
  }

  const prepareData = () => {
    AppStorage.hideLoading()
    const tempList: { label: string; value: number }[] = []
    storedUserList.map((item) => {
      tempList.push({
        label: item.name,
        value: item.id
      })
    })
    if (Helper.isLoggeIn()) {
      let shouldAddLoginUser = true
      tempList.map((item) => {
        if (item.value === AppStorage.appStorage.userId) {
          shouldAddLoginUser = false
        }
      })
      if (shouldAddLoginUser) {
        tempList.unshift({
          label: "Logging in as " + AppStorage.appStorage.userDevice.name,
          value: AppStorage.appStorage.userDevice.id
        })

      }
    }
    console.log("selectedUserId", AppStorage.appStorage.userId)
    if (AppStorage.appStorage.userId) {
      setSelectedUserId(AppStorage.appStorage.userId)
    } else {
      setSelectedUserId(tempList[0].value)
    }
    console.log("tempList", JSON.stringify(tempList))
    setUsers(JSON.parse(JSON.stringify(tempList)))
  }

  useEffect(() => {
    // console.log("useEffect " + JSON.stringify(AppStorage.appStorage.userDevice))
    prepareData()
  }, [])

  useEffect(() => { }, [AppStorage.appStorage.setting.language])

  useEffect(() => {
    // console.log("AppStorage.appStorage.userId", AppStorage.appStorage.userId)
    if (selectedUserId != AppStorage.appStorage.userId) {
      setSelectedUserId(AppStorage.appStorage.userId);
    }
  }, [AppStorage.appStorage.userId])

  return (
    <View style={[containerStyle, { backgroundColor: colors.palette.white }]}>
      {/* { users.length > 0 &&
          <Dropdown
            style={MARGIN_DF}
            itemTextStyle={TEXT_BLACK}
            placeholderStyle={TEXT_BLACK}
            selectedTextStyle={TEXT_BLACK}
            containerStyle={W_100P}            
            data={users} 
            labelField="label"
            valueField="value"
            value={selectedUserId}
            onChange={item => {
              console.log(item)
              // setSelectedUserId(item.value);
              AppStorage.switchUserById(item.value, () => {
                prepareData()
                loadUserInformation()              
              })
            }}
          />
        } */}
      <View style={HeaderContainer}>
        {
          leftTitle === ""
            ?
            <Image source={require('../../assets/images/header-logo.png')} resizeMode="contain" style={{ width: 100 }} />
            :
            <Text style={[TEXT_SECTION, FSIZE_18]} text={leftTitle} />
        }
        <View style={FLEX} />
        {
          showSearchIcon && <MIcon
            onPress={() => {
              if (onSearchIconClicked) {
                onSearchIconClicked()
              }
            }}
            containerStyle={MARGIN_DF_RIGHT}
            name="search"
            size={Dimen.iconSize.sm} />
        }
        {
          Helper.isPortalUser(AppStorage.appStorage.userDevice) &&
          <Text
            style={[BTN_IN_CARD, FSIZE_13]}
            onPress={() => {
              //navigator.navigate("RealAccountRegistration")
              Helper.loadRegLinkSinoSound(
                navigator,
                AppStorage.appStorage.userDevice?.areaCode || "",
                AppStorage.appStorage.userDevice?.mobile || ""
              )
            }}
            tx="tradePage.createRealAccount" />
        }
        {
          Helper.isDemoUser(AppStorage.appStorage.userDevice) &&
          <Text
            style={[BTN_IN_CARD, FSIZE_13]}
            onPress={() => {
              //navigator.navigate("RealAccountRegistration")
              Helper.loadRegLinkSinoSound(
                navigator,
                AppStorage.appStorage.userDevice?.areaCode || "",
                AppStorage.appStorage.userDevice?.mobile || ""
              )
            }}
            tx="tradePage.upgradeToRealAccount" />
        }
        { /* hide the fund amount on every page except account page as client requested */
          /* Helper.isLiveAccount(AppStorage.appStorage.userDevice) && 
          <View style={[ROW_CENTER, {borderRadius: 6, overflow: "hidden"}]}>
            <Text 
              style={LiveText}
              // onPress={() => goToLogin()} 
              tx="account.live" />
            <Text 
              style={LiveAccountBalance}
              // onPress={() => goToLogin()} 
              text={getAccountBalance()} />
          </View> */
        }
        {
          !Helper.isLoggeIn() && <Text style={FSIZE_13} onPress={() => goToLogin()} tx="boarding.login" />
        }
      </View>
    </View>
  )
}
)  
