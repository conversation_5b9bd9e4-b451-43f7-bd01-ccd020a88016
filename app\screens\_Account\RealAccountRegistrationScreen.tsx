import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BG_GOLD, BG_GRAY, BTN_CONTAINER, DF_CONTENT, FSIZE_12, TEXT_CONTENT, TEXT_SECTION } from "../../theme/baseStyle"
import { AI_CENTER, DISABLED, ENABLED, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_TOP, MARGIN_S_DF_LEFT, ROW, ROW_CENTER, TEXT_BLACK, TEXT_CENTER, TEXT_COLOR_GREEN, TEXT_WHITE, W_100P } from "../../theme/mStyle"
import { TextInput, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { AppStorage } from "app/utils/appStorage"
import { colors } from "app/theme"
import { Dropdown } from "react-native-element-dropdown"
import { ApiService } from "app/api/api"
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import { getRegisterEmailCode, getRegisterPhoneCode, registerLiveAccount, uploadImage } from "app/api/model"
import { Helper } from "app/utils/helper"
import { BackNavComponent } from "app/components/BackNavComponent"


const SectionContent: ViewStyle = {
  ...ROW,
  width: Dimen.screenWidth,
  paddingHorizontal: Dimen.padding.base,
  paddingVertical: Dimen.padding.sm,
  marginTop: Dimen.padding.base,
  alignItems: "center",
  backgroundColor: colors.mine.bgGrey
}

const TextLabel: TextStyle = {
  ...TEXT_CONTENT,
  ...FSIZE_12,
  marginLeft: Dimen.padding.base,
  marginTop: Dimen.padding.base
}

const TextValue: TextStyle = {
  ...TEXT_CONTENT,
  ...FLEX,
  ...FSIZE_12,
  lineHeight: 16,
}

const TextMessage: TextStyle = {
  ...TEXT_CONTENT,
  ...FSIZE_12,
  marginLeft: Dimen.padding.base
}

const TextInputValue: TextStyle = {
  ...TEXT_CONTENT,
  ...ROW,
  ...AI_CENTER,
  ...FSIZE_12,
  lineHeight: 16,
  width: Dimen.screenWidth - Dimen.padding.base * 2,
  height: 45,
  marginHorizontal: Dimen.padding.base,
  marginTop: Dimen.padding.ssm,
  paddingHorizontal: Dimen.padding.sm,
  borderRadius: Dimen.borderRadiusLarge,
  borderColor: colors.mine.textInputBorder,
  borderWidth: 1,
}

const TextInputContainer: TextStyle = {
  ...ROW,
  ...AI_CENTER,
  width: Dimen.screenWidth - Dimen.padding.base * 2,
  height: 45,
  marginHorizontal: Dimen.padding.base,
  marginTop: Dimen.padding.ssm,
  paddingLeft: 8,
  paddingRight: 6,
  borderRadius: Dimen.borderRadiusLarge,
  borderColor: colors.mine.textInputBorder,
  borderWidth: 1,
}

const TextInputDisabledContainer: TextStyle = {
  ...TextInputContainer,
  backgroundColor: colors.mine.bgGrey
}

const BtnUpload: ViewStyle = {
  width: 100,
  height: "85%",
  backgroundColor: colors.mine.primary,
  borderRadius: Dimen.borderRadiusLarge,
  overflow: "hidden",
  marginRight: -3,
  justifyContent: "center",
  alignItems: "center",
}

const BtnUploadText: TextStyle = {
  ...FSIZE_12,
  textAlign: "center",
  color: colors.mine.white
}

interface RealAccountRegistrationScreenProps extends AppStackScreenProps<"RealAccountRegistration"> { }
export const RealAccountRegistrationScreen: FC<RealAccountRegistrationScreenProps> = observer(function RealAccountRegistrationScreen(_props) {

  const [name, setName] = useState("")
  const [gender, setGender] = useState(0)
  const [idDocumentType, setIdDocumentType] = useState(0)
  const [idNumber, setIdNumber] = useState("")
  const [bankName, setBankName] = useState("")
  const [branchOpening, setBranchOpening] = useState("")
  const [bankCardNo, setBankCardNo] = useState("")
  const [bankAccountName, setBankAccountName] = useState("")
  const [agent, setAgent] = useState("")
  const [referralCode, setReferralCode] = useState("")
  const [phoneNo, setPhoneNo] = useState("")
  const [phoneVerifyCode, setPhoneVerifyCode] = useState("")
  const [email, setEmail] = useState("")
  const [emailVerifyCode, setEmailVerifyCode] = useState("")
  const [idCardFrontPhoto, setIdCardFrontPhoto] = useState("")
  const [idCardBackPhoto, setIdCardBackPhoto] = useState("")
  const [holdingCardPhoto, setHoldingCardPhoto] = useState("")
  const [bankCardFacePhoto, setBankCardFacePhoto] = useState("")
  const [idCardFrontPhotoName, setIdCardFrontPhotoName] = useState(translate("liveAccountSignUp.idCardFrontPhotoPlaceholder"))
  const [idCardBackPhotoName, setIdCardBackPhotoName] = useState(translate("liveAccountSignUp.idCardBackPhotoPlaceholder"))
  const [holdingCardPhotoName, setHoldingCardPhotoName] = useState(translate("liveAccountSignUp.holdingCardPhotoPlaceholder"))
  const [bankCardFacePhotoName, setBankCardFacePhotoName] = useState(translate("liveAccountSignUp.bankCardFacePhotoPlaceholder"))
  const [videoWatched, setVideoWatched] = useState(false)
  const [areaCode, setAreaCode] = useState("+852")
  const genderList = [
    /* {
      label: translate("liveAccountSignUp.genderPlaceholder"),
      value: 0
    }, */
    {
      label: translate("common.male"),
      value: 1
    },
    {
      label: translate("common.female"),
      value: 2
    }
  ]
  const typesList = [
    /* {
      label: translate("liveAccountSignUp.idDocumentTypePlaceholder"),
      value: 0
    }, */
    {
      label: translate("common.china"),
      value: 1
    },
    {
      label: translate("common.hongkongAndOthers"),
      value: 3
    }
  ]

  const [countDownSecondPhone, setCountDownSecondPhone] = useState(61)
  const [resentCodePhoneCount, setResentCodePhoneCount ] = useState(0)
  const [phoneErrorText, setPhoneErrorText] = useState("")
  const timerPhoneRef = useRef(null)
  const countDownSecondPhoneRef = useRef(countDownSecondPhone);

  const [countDownSecondEmail, setCountDownSecondEmail] = useState(31)
  const [resentCodeEmailCount, setResentCodeEmailCount ] = useState(0)
  const [EmailErrorText, setEmailErrorText] = useState("")
  const timerEmailRef = useRef(null)
  const countDownSecondEmailRef = useRef(countDownSecondEmail);
  

  const startCountdown = (isPhone: boolean) => {
    if (isPhone) {
      if (timerPhoneRef.current == null) {
        timerPhoneRef.current = setInterval(() => {
          // console.log("countDownSecond: " + countDownSecondRef.current);
          if (countDownSecondPhoneRef.current > 0) {
            setCountDownSecondPhone(countDownSecondPhoneRef.current - 1);
          } else {
            setPhoneErrorText("")
            setCountDownSecondPhone(61);
            clearInterval(timerPhoneRef.current);
            timerPhoneRef.current = null;
            countDownSecondPhoneRef.current = 60;
          }
        }, 1000)
      }
    } else {
      if (timerEmailRef.current == null) {
        timerEmailRef.current = setInterval(() => {
          // console.log("countDownSecond: " + countDownSecondRef.current);
          if (countDownSecondEmailRef.current > 0) {
            setCountDownSecondEmail(countDownSecondEmailRef.current - 1);
          } else {
            setEmailErrorText("")
            setCountDownSecondEmail(31);
            clearInterval(timerEmailRef.current);
            timerEmailRef.current = null;
            countDownSecondEmailRef.current = 30;
          }
        }, 1000)
      }
    }    
  }

  const clearTimer = (isPhone: boolean) => {
    if (isPhone) {
      if (timerPhoneRef.current != null) {
        setPhoneErrorText("")
        clearInterval(timerPhoneRef.current)
        setCountDownSecondPhone(61)
        setResentCodePhoneCount(0)
        timerPhoneRef.current = null
      }
    } else {
      if (timerEmailRef.current != null) {
        setEmailErrorText("")
        clearInterval(timerEmailRef.current)
        setCountDownSecondEmail(61)
        setResentCodeEmailCount(0)
        timerEmailRef.current = null
      }
    }
  }

  const getRequestCodeBtnText = (isPhone: boolean) => {
    if (isPhone) {
      if (countDownSecondPhone > 60) {
        if (resentCodePhoneCount < 1) {
          return translate("demoAccountSignUp.getVerifyCode")
        } else {
          return translate("demoAccountSignUp.resendVerifyCode")
        }
      } else {
        return translate("common.resend") + " (" + countDownSecondPhone + "s)"
      }
    } else {
      if (countDownSecondEmail > 30) {
        if (resentCodeEmailCount < 1) {
          return translate("demoAccountSignUp.getVerifyCode")
        } else {
          return translate("demoAccountSignUp.resendVerifyCode")
        }
      } else {
        return translate("common.resend") + " (" + countDownSecondEmail + "s)"
      }
    }
  }

  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
    if (AppStorage.appStorage.userDevice) {
      setAreaCode(AppStorage.appStorage.userDevice.areaCode)
      setPhoneNo(AppStorage.appStorage.userDevice.mobile)
    }
  }

  const handleImageUpload = (typeMaterial, response) => {
    if (response.assets && response.assets.length > 0) {
      if (typeMaterial == 1) {
        setIdCardFrontPhotoName(response.assets[0].fileName)
      } else if (typeMaterial == 2) {
        setIdCardBackPhotoName(response.assets[0].fileName)
      } else if (typeMaterial == 3) {
        setHoldingCardPhotoName(response.assets[0].fileName)
      } else if (typeMaterial == 4) {
        setBankCardFacePhotoName(response.assets[0].fileName)
      }
    }
    AppStorage.showLoading()
    uploadImage(
      Helper.formatBase64ImageForApi(response.assets[0].base64, response.assets[0].fileName),
      (res) => {
        AppStorage.hideLoading()
        console.log(JSON.stringify(res))
        if (res.OA || res.OA.data || res.OA.data.image_code) {
          if (typeMaterial == 1) {
            setIdCardFrontPhoto(res.OA.data.image_code)
          } else if (typeMaterial == 2) {
            setIdCardBackPhoto(res.OA.data.image_code)
          } else if (typeMaterial == 3) {
            setHoldingCardPhoto(res.OA.data.image_code)
          } else if (typeMaterial == 4) {
            setBankCardFacePhoto(res.OA.data.image_code)
          }
          ApiService.showSuccessToast(res.OA.msg)
        } else {
          ApiService.showMessageBox(res.OA.msg)
        }
      },
      (error) => {
        AppStorage.hideLoading()
        console.log(error)
        ApiService.showMessageBox(error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const showImagePicker = (typeMaterial) => {
    // 1 idCardFrontPhoto
    // 2 idCardBackPhoto
    // 3 holdingCardPhoto
    // 4 bankCardFacePhoto
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    console.log("showImagePicker: " + typeMaterial)
    ApiService.showMessageBoxTwoOptionCustomeBtn(
      translate("liveAccountSignUp.selectImageFrom"),
      translate("liveAccountSignUp.camera"),
      translate("liveAccountSignUp.gallery"),
      () => {
        console.log("camera")
        showCamera(typeMaterial)
      },
      () => {
        console.log("gallery")
        showGallery(typeMaterial)
      }
    )
  }

  const showCamera = (typeMaterial) => {
    console.log("showCamera: " + typeMaterial)
    launchCamera(
      {
        mediaType: 'photo',
        includeBase64: true
      },
      (response) => {
        console.log('Response = ', response);
        if (response.didCancel) {
          console.log('User cancelled image picker');
        } else if (response.errorCode) {
          console.log('ImagePicker Error: ', response.errorCode);
        } else if (response.errorMessage) {
          console.log('ImagePicker Error: ', response.errorMessage);
        } else {
          // console.log(response)
          handleImageUpload(typeMaterial, response)
        }
      }
    )
  }

  const showGallery = (typeMaterial) => {
    console.log("showGallery: " + typeMaterial)
    launchImageLibrary(
      {
        mediaType: 'photo',
        includeBase64: true
      },
      (response) => {
        // console.log('Response = ', response);
        if (response.didCancel) {
          console.log('User cancelled image picker');
        } else if (response.errorCode) {
          console.log('ImagePicker Error: ', response.errorCode);
        } else if (response.errorMessage) {
          console.log('ImagePicker Error: ', response.errorMessage);
        } else {
          // console.log(response)
          handleImageUpload(typeMaterial, response)
        }
      }
    )
  }

  const requestPhoneCode = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (phoneNo === "") {
      ApiService.showMessageBox(translate("errors.enterPhone"))
    } else if (countDownSecondPhone < 61) {
      console.log("countDownSecondPhone: " + countDownSecondPhone)
    } else {
      AppStorage.showLoading()
      getRegisterPhoneCode(
        areaCode,
        phoneNo,
        (res) => {
          AppStorage.hideLoading()
          console.log(res)
          // ApiService.showSuccessToast(res.Message)
          setCountDownSecondPhone(60)
          if (resentCodePhoneCount > 0) {
            setPhoneErrorText(translate("boarding.newCodeSent"))
          } else {
            setPhoneErrorText(translate("demoAccountSignUp.verifycodeSent"))
          }
          startCountdown(true)
          setResentCodePhoneCount(resentCodePhoneCount + 1)
        },
        (error) => {
          AppStorage.hideLoading()
          console.log(error)
          ApiService.showMessageBox(error)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  const requestEmailCode = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (email === "") {
      ApiService.showMessageBox(translate("errors.enterEmail"))
    } else if (!Helper.isEmailString(email)) {
        ApiService.showMessageBox(translate("errors.invalidEmail"))      
    } else if (countDownSecondEmail < 31) {
      console.log("countDownSecondEmail: " + countDownSecondEmail)
    } else {
      AppStorage.showLoading()
      getRegisterEmailCode(
        email,
        (res) => {
          AppStorage.hideLoading()
          console.log(res)
          setCountDownSecondEmail(30)
          if (resentCodeEmailCount > 0) {
            setEmailErrorText(translate("boarding.newCodeSent"))
          } else {
            setEmailErrorText(translate("demoAccountSignUp.verifycodeSent"))
          }
          startCountdown(false)
          setResentCodeEmailCount(resentCodeEmailCount + 1)
        },
        (error) => {
          AppStorage.hideLoading()
          console.log(error)
          ApiService.showMessageBox(error)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  const checkFormValidate = (showMessage = true) => {
    if (!videoWatched) {
      if (showMessage) {
        ApiService.showMessageBox(translate("errors.watchInvestRelatedVideo"))
      }
      return false
    } else if (name == ""
      || gender == 0
      || idDocumentType == 0
      || idNumber == ""
      || idCardFrontPhoto == ""
      || idCardBackPhoto == ""
      || holdingCardPhoto == ""
      || bankName == ""
      || branchOpening == ""
      || bankCardNo == ""
      || bankCardFacePhoto == ""
      || bankAccountName == ""
      || phoneNo == ""
      || phoneVerifyCode == ""
      || email == ""
      || emailVerifyCode == ""
    ) {
      if (showMessage) {
        ApiService.showMessageBox(translate("errors.inputAllRequiredValues"))
      }
      return false
    } else {
      return true
    }
  }

  const submitRegistrationForm = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (checkFormValidate()) {
      AppStorage.showLoading()
      const formData = {
        id: AppStorage.appStorage.userId,
        client_name: name,
        gender,
        id_type: idDocumentType,
        id_number: idNumber,
        phone_code: phoneVerifyCode,
        email,
        email_code: emailVerifyCode,
        bank_account_name: bankAccountName,
        bank_name: bankName,
        bank_account: bankCardNo,
        bank_branch: branchOpening,
        id_front_img: idCardFrontPhoto,
        id_back_img: idCardBackPhoto,
        bank_card_img: bankCardFacePhoto,
        id_handheld_img: holdingCardPhoto,
      }
      // agent_number: agent,
      // referral_code: referralCode,
      if (agent !== "") {
        formData.agent_number = agent
      }
      if (referralCode !== "") {
        formData.referral_code = referralCode
      }
      registerLiveAccount(
        formData,
        (res, code) => {
          AppStorage.hideLoading()
          console.log(res)
          if (code != null) {
            if (code == 10029) {
              ApiService.showMessageBox(translate("errors.realRgCode10029"))
            } else if (code == 10033) {
              ApiService.showMessageBox(translate("errors.realRgCode10033"))
            } else if (code == 10031) {
              ApiService.showMessageBox(translate("errors.realRgCode10031"))
            } else if (code == 10032) {
              ApiService.showMessageBox(translate("errors.realRgCode10032"))
            } else if (code == 200) {
              ApiService.showMessageBoxAndBack(
                translate("errors.realRgCode200"),
                () => {
                  _props.navigation.navigate("MainStack",
                    {
                      screen: "Home",
                    }
                  )
                }
              )              
            } else {
              ApiService.showMessageBox(res)
            }
          } else {
            if (res.Message) {
              ApiService.showMessageBoxAndBack(
                res.Message,
                () => {
                  _props.navigation.navigate("MainStack",
                    {
                      screen: "Home",
                    }
                  )
                }
              )     
            } else {
              ApiService.showMessageBoxAndBack(
                translate("common.success"),
                () => {
                  _props.navigation.navigate("MainStack",
                    {
                      screen: "Home",
                    }
                  )
                }
              )     
            }
          }
          // _props.navigation.navigate("MainStack",
          //   {
          //     screen: "Account",
          //   }
          // )
        },
        (errorMessage) => {
          AppStorage.hideLoading()
          console.log(errorMessage)
          ApiService.showMessageBox(errorMessage)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  useEffect(() => {
    countDownSecondPhoneRef.current = countDownSecondPhone
   }, [countDownSecondPhone])
 
   useEffect(() => {
     countDownSecondEmailRef.current = countDownSecondEmail
   }, [countDownSecondEmail])

  useEffect(() => {
    prepareData()
    return () => {
      clearTimer(true)
      clearTimer(false)
    }
  }, [])

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        isTitleCenter={false}
        title={translate("liveAccountSignUp.title")} />
      <View style={SectionContent}>
        <Text style={FSIZE_12} text={translate("account.personalInfo")} />
      </View>
      <Text style={TextLabel} text={translate("liveAccountSignUp.name")} />
      <TextInput
        allowFontScaling={false}
        value={name}
        onChangeText={setName}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.namePlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.gender")} />
      <View style={TextInputContainer}>
        {/* <Text style={TextValue} text={translate("liveAccountSignUp.genderPlaceholder")} />
        <MIcon name="down" size={Dimen.iconSize.sm} /> */}
        <Dropdown
          style={[W_100P, { padding: 3 }]}
          itemTextStyle={[TEXT_BLACK, FSIZE_12]}
          placeholder={translate("liveAccountSignUp.genderPlaceholder")}
          placeholderStyle={[TEXT_BLACK, FSIZE_12, { color: colors.mine.placeHolderText }]}
          selectedTextStyle={[TEXT_BLACK, FSIZE_12]}
          selectedTextProps={{ allowFontScaling: false }}
          containerStyle={W_100P}
          data={genderList}
          labelField="label"
          valueField="value"
          value={gender}
          onChange={item => {
            console.log(item)
            setGender(item.value)
          }}
        />
      </View>

      <Text style={TextLabel} text={translate("liveAccountSignUp.idDocumentType")} />
      <View style={TextInputContainer}>
        {/* <Text style={TextValue} text={translate("liveAccountSignUp.idDocumentTypePlaceholder")} />
        <MIcon name="down" size={Dimen.iconSize.sm} /> */}
        <Dropdown
          style={[W_100P, { padding: 3 }]}
          itemTextStyle={[TEXT_BLACK, FSIZE_12]}
          placeholder={translate("liveAccountSignUp.idDocumentTypePlaceholder")}
          placeholderStyle={[TEXT_BLACK, FSIZE_12, { color: colors.mine.placeHolderText }]}
          selectedTextStyle={[TEXT_BLACK, FSIZE_12]}
          selectedTextProps={{ allowFontScaling: false }}
          containerStyle={W_100P}
          data={typesList}
          labelField="label"
          valueField="value"
          value={idDocumentType}
          onChange={item => {
            console.log(item)
            setIdDocumentType(item.value)
          }}
        />
      </View>

      <Text style={TextLabel} text={translate("liveAccountSignUp.idNumber")} />
      <TextInput
        allowFontScaling={false}
        value={idNumber}
        onChangeText={setIdNumber}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.idNumberPlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.idCardFrontPhoto")} />
      <View style={TextInputContainer}>
        <Text style={[TextValue,{color: colors.mine.placeHolderText}]} text={idCardFrontPhotoName} numberOfLines={1} />
        <TouchableOpacity
          onPress={() => showImagePicker(1)}
          style={BtnUpload}>
          <Text style={BtnUploadText} text={translate("common.upload")} />
        </TouchableOpacity>
      </View>

      <Text style={TextLabel} text={translate("liveAccountSignUp.idCardBackPhoto")} />
      <View style={TextInputContainer}>
        <Text style={[TextValue,{color: colors.mine.placeHolderText}]} text={idCardBackPhotoName} />
        <TouchableOpacity
          onPress={() => showImagePicker(2)}
          style={BtnUpload}>
          <Text style={BtnUploadText} text={translate("common.upload")} />
        </TouchableOpacity>
        {/* <Text onPress={() => showImagePicker(2)} style={BtnUpload} text={translate("common.upload")} /> */}
      </View>

      <Text style={TextLabel} text={translate("liveAccountSignUp.holdingCardPhoto")} />
      <View style={TextInputContainer}>
        <Text style={[TextValue,{color: colors.mine.placeHolderText}]} text={holdingCardPhotoName} />
        <TouchableOpacity
          onPress={() => showImagePicker(3)}
          style={BtnUpload}>
          <Text style={BtnUploadText} text={translate("common.upload")} />
        </TouchableOpacity>
        {/* <Text onPress={() => showImagePicker(3)} style={BtnUpload} text={translate("common.upload")} /> */}
      </View>


      <View style={SectionContent}>
        <Text style={FSIZE_12} text={translate("account.bankInfo")} />
      </View>
      <Text style={TextLabel} text={translate("liveAccountSignUp.bankName")} />
      <TextInput
        allowFontScaling={false}
        value={bankName}
        onChangeText={setBankName}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.bankNamePlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.branchOpening")} />
      <TextInput
        allowFontScaling={false}
        value={branchOpening}
        onChangeText={setBranchOpening}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.branchOpeningPlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.bankAccountName")} />
      <TextInput
        allowFontScaling={false}
        value={bankAccountName}
        onChangeText={setBankAccountName}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.bankAccountNamePlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.bankCardNo")} />
      <TextInput
        allowFontScaling={false}
        value={bankCardNo}
        onChangeText={setBankCardNo}
        keyboardType="number-pad" 
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.bankCardNoPlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.bankCardFacePhoto")} />
      <View style={TextInputContainer}>
        <Text style={[TextValue,{color: colors.mine.placeHolderText}]} text={bankCardFacePhotoName} />
        <TouchableOpacity
          onPress={() => showImagePicker(4)}
          style={BtnUpload}>
          <Text style={BtnUploadText} text={translate("common.upload")} />
        </TouchableOpacity>
        {/* <Text onPress={() => showImagePicker(4)} style={BtnUpload} text={translate("common.upload")} /> */}
      </View>


      <View style={SectionContent}>
        <Text style={FSIZE_12} text={translate("liveAccountSignUp.agentCode")} />
      </View>
      <Text style={TextLabel} text={translate("liveAccountSignUp.agent")} />
      <TextInput
        allowFontScaling={false}
        value={agent}
        onChangeText={setAgent}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.agentPlaceholder")}
        style={TextInputValue} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.referralCode")} />
      <TextInput
        allowFontScaling={false}
        value={referralCode}
        onChangeText={setReferralCode}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.referralCodePlaceholder")}
        style={TextInputValue} />


      <View style={SectionContent}>
        <Text style={FSIZE_12} text={translate("liveAccountSignUp.auth")} />
      </View>
      <Text style={TextLabel} text={translate("liveAccountSignUp.phoneNo")} />
      <View style={TextInputDisabledContainer}>
        {/* <Text 
          onPress={() => {
            _props.navigation.navigate("SelectCountry", {
              onSelect: (item) => {
                console.log("onSelect", JSON.stringify(item))
                setAreaCode(item.value)
              },
            })          
          }}
          style={TEXT_CONTENT} 
          text={areaCode} /> */}
        <Text
          allowFontScaling={false}
          // editable={false}
          // keyboardType="phone-pad"
          text={"+" + areaCode + " " + phoneNo}
          // onChangeText={setPhoneNo}
          // placeholderTextColor={colors.mine.placeHolderText}
          // placeholder={translate("liveAccountSignUp.phoneNoPlaceholder")}
          style={[TEXT_CONTENT, FSIZE_12, FLEX]} />
      </View>
      <View style={TextInputContainer}>
        <TextInput
          allowFontScaling={false}
          keyboardType="number-pad" 
          value={phoneVerifyCode}
          onChangeText={setPhoneVerifyCode}
          placeholderTextColor={colors.mine.placeHolderText}
          placeholder={translate("liveAccountSignUp.verifyCodePlaceholder")}
          style={TextValue} />
        <TouchableOpacity
          onPress={() => requestPhoneCode()}
          style={[BtnUpload, countDownSecondPhone > 60 ? BG_GOLD : BG_GRAY]}>
          <Text style={BtnUploadText} text={getRequestCodeBtnText(true)} />
        </TouchableOpacity>      
      </View>
      <Text style={[TextMessage, TEXT_COLOR_GREEN]} text={phoneErrorText} />

      <Text style={TextLabel} text={translate("liveAccountSignUp.email")} />
      <TextInput
        allowFontScaling={false}
        keyboardType="email-address"
        value={email}
        onChangeText={setEmail}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("liveAccountSignUp.emailPlaceholder")}
        style={TextInputValue} />
      <View style={TextInputContainer}>
        <TextInput
          allowFontScaling={false}
          keyboardType="number-pad" 
          value={emailVerifyCode}
          onChangeText={setEmailVerifyCode}
          placeholderTextColor={colors.mine.placeHolderText}
          placeholder={translate("liveAccountSignUp.verifyCodePlaceholder")}
          style={TextValue} />
        <TouchableOpacity
          onPress={() => requestEmailCode()}
          style={[BtnUpload, countDownSecondEmail > 30 ? BG_GOLD : BG_GRAY]}>
          <Text style={BtnUploadText} text={getRequestCodeBtnText(false)} />
        </TouchableOpacity>
      </View>
      <Text style={[TextMessage, TEXT_COLOR_GREEN]} text={EmailErrorText} />

      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("InvestRelatedVideo", {
            onWatched: () => {
              console.log("onWatched")
              setVideoWatched(true)
            }

          })
        }}
        style={[DF_CONTENT, ROW_CENTER, MARGIN_DF_TOP]}>
        {/* <CheckBox 
          boxType="square"
          onFillColor={colors.mine.green}
          onCheckColor={colors.mine.white}
          onTintColor={colors.mine.gray}
          value={videoWatched} /> */}
        {
          videoWatched ?
            <MIcon name="checkBoxOn" size={Dimen.iconSize.md} /> :
            <MIcon name="checkBoxOff" size={Dimen.iconSize.md} />
        }
        <Text style={[TextValue, MARGIN_S_DF_LEFT]} text={translate("liveAccountSignUp.watchInvestRelatedVideo")} />
        <MIcon name="right" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => submitRegistrationForm()}
        style={[DF_CONTENT, BTN_CONTAINER, MARGIN_DF_TOP, MARGIN_DF_BOTTOM, checkFormValidate(false) ? ENABLED : DISABLED]}>
        <Text style={[TEXT_SECTION, TEXT_WHITE, TEXT_CENTER, W_100P]} text={translate("common.submit")} />
      </TouchableOpacity>
      <View style={{ height: 40 }}></View>
    </Screen>
  )
})