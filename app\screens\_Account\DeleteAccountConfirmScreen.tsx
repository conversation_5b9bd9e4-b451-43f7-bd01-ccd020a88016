import { observer } from "mobx-react-lite"
import React, { FC, useEffect } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BG_GRAY, BTN_CONTAINER, TEXT_CONTENT } from "../../theme/baseStyle"
import { DISABLED, ENABLED, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_TOP, MARGIN_S_DF_LEFT, MARGIN_S_DF_RIGHT, ROW_CENTER, TEXT_CENTER, TEXT_WHITE, W_100P } from "../../theme/mStyle"
import { TouchableOpacity, View, ScrollView } from "react-native"
import { AppStorage } from "app/utils/appStorage"
import CheckBox from "@react-native-community/checkbox"
import { deleteUser } from "app/api/model"
import { ApiService } from "app/api/api"
import { BackNavComponent } from "app/components/BackNavComponent"

interface DeleteAccountConfirmScreenProps extends AppStackScreenProps<"DeleteAccountConfirm"> {}
export const DeleteAccountConfirmScreen: FC<DeleteAccountConfirmScreenProps> = observer(function DeleteAccountConfirmScreen(_props) {
  
  const [canDelete, setCanDelete] = React.useState(false)
  const [readTerm, setReadTerm] = React.useState(false)

  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
  }

  useEffect(() => {
    prepareData()
  }, [])

  const callDeleteUser = async () => {    
    console.log("callDeleteUser", AppStorage.appStorage.userId)
    if (AppStorage.appStorage.userId && AppStorage.appStorage.userId != 0) {
      AppStorage.showLoading()
      deleteUser(
        AppStorage.appStorage.userId,
        (response) => {
          console.log("deleteUser", JSON.stringify(response))
          AppStorage.hideLoading()
          _props.navigation.goBack()
          AppStorage.reset(() => {
            AppStorage.switchUserById(0, () => {
              _props.navigation.reset({
                index: 0,
                routes: [{ name: "BottomTab" }],
              })
            })
          })
        },
        (message) => {
          AppStorage.hideLoading()
          ApiService.showMessageBox(message)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ padding: Dimen.padding.base, flex: 1 }}
      safeAreaEdges={["top", "bottom"]}
    >
       <BackNavComponent
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false} 
        containerStyle={MARGIN_DF_BOTTOM}
        title={translate("deleteAccount.title")} />
      <ScrollView 
        onScroll={({nativeEvent}) => {
          const paddingToBottom = 20;
          if (nativeEvent.layoutMeasurement.height + nativeEvent.contentOffset.y >=
            nativeEvent.contentSize.height - paddingToBottom && !canDelete) {
              setCanDelete(true)
            }
        }}
        scrollEventThrottle={400}
        style={[FLEX, MARGIN_DF_BOTTOM]}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP]} tx="deleteAccount.content" />
        <TouchableOpacity 
          onPress={() => {
            // _props.navigation.goBack()
            setReadTerm(!readTerm)
          }}
          style={[ROW_CENTER, MARGIN_DF_TOP]}>
          <CheckBox value={readTerm}/>
          <Text style={TEXT_CONTENT} text={translate("deleteAccount.confirmDelete")} />        
        </TouchableOpacity>
      </ScrollView>      
      <View style={[ROW_CENTER, MARGIN_DF_BOTTOM]}>
        <Text 
          onPress={() => {
            _props.navigation.goBack()
          }}
          style={[BTN_CONTAINER, BG_GRAY, MARGIN_S_DF_RIGHT, FLEX, {marginHorizontal: 0}]} tx="common.return" />
        <TouchableOpacity 
          onPress={() => {
            if (!AppStorage.isNetworkConnected()) {
              return
            }
            if (readTerm && canDelete)
            callDeleteUser()
          }}
          style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX, canDelete && readTerm ? ENABLED : DISABLED, {marginHorizontal: 0}]}>
          <Text style={[W_100P, TEXT_CENTER, TEXT_CONTENT, TEXT_WHITE]} tx="common.confirm" />
          </TouchableOpacity>      
     </View>
    </Screen>
  )
})