import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "app/components/MIcon"
import { Dimen } from "app/theme/dimen"
import { CardNews } from "../_Home/CardNews"
import { CardMarketingNews } from "../_Home/CardMarketingNews"
import { AppStorage } from "app/utils/appStorage"
import { getPostById } from "app/api/model"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

interface NewsDetailsScreenProps extends AppStackScreenProps<"NewsDetails"> {}

export const NewsDetailsScreen: FC<NewsDetailsScreenProps> = observer(function NewsDetailsScreen(_props) {
  const [postItem, setPostItem] = useState(null)  
  const [type, setType] = useState(1) // 1: news, 2: market news
  const [displayFullContent, setDisplayFullContent] = useState(false)
  
  const getPostItem = async (postId: string) => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    AppStorage.showLoading()
    getPostById(
      postId, 
      (res) => {
        console.log("getPostById res", res)
        AppStorage.hideLoading()
        if (res) {
          setPostItem(() => res)
          setType(1)
        }
      },
      (err) => {
        console.log("getPostById err", err)
        AppStorage.hideLoading()
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        if (_props.route.params && _props.route.params.dataObj) {
          setPostItem(_props.route.params.dataObj)
        }
        if (_props.route.params && _props.route.params.type) {
          if (_props.route.params.type == "MarketNews") {
            setType(2)
          }
        }
        if (_props.route.params && _props.route.params.postId) {
          getPostItem(_props.route.params.postId)
        }
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    if (_props.route.params && _props.route.params.dataObj) {
      setPostItem(_props.route.params.dataObj)
    }
    if (_props.route.params && _props.route.params.type) {
      if (_props.route.params.type == "MarketNews") {
        setType(2)
      }
    }
    if (_props.route.params && _props.route.params.postId) {
      getPostItem(_props.route.params.postId)
    }
  }, [_props.route])

  return (
    <Screen
      preset={"auto"}
      safeAreaEdges={["top", "bottom"]}>
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }}  />          
        {
          type == 1 && postItem &&
          <CardNews 
            data={postItem} 
            showDetails={true}
            // showFullContent={displayFullContent}
            containerStyle={{marginBottom: displayFullContent ? Dimen.padding.xxl : Dimen.padding.base}}
            onFullContentUpdate={(value) => {
              setDisplayFullContent(value)
            }}
          />
        }
        {
          type == 2 && postItem &&
          <CardMarketingNews data={postItem} showDetails={true} />
        }
      
    </Screen>
  )
})
