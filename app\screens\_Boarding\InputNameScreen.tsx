import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BTN_CONTAINER, FSIZE_17, FSIZE_24, TEXT_INPUT, TEXT_SCREEN_TITLE} from "../../theme/baseStyle"
import { BOLD, FLEX, MARGIN_DF_TOP, MARGIN_L_TOP } from "../../theme/mStyle"
import { TextInput, View } from "react-native"
import { ApiService } from "app/api/api"
import { requestRegisterPortalUser } from "app/api/model"
import { AppStorage } from "app/utils/appStorage"

interface InputNameScreenProps extends AppStackScreenProps<"InputName"> {}

export const InputNameScreen: FC<InputNameScreenProps> = observer(function InputNameScreen(_props) {
  const [selectPhoneCountryCode, setSelectPhoneCountryCode] = useState("")
  const [phone, setPhone] = useState("")
  const [userName, setUserName] = useState("")

  const prepareData = () => {
    if (_props.route.params) {
      if (_props.route.params?.phoneCountryCode) {
        setSelectPhoneCountryCode(_props.route.params?.phoneCountryCode)
      }
      if (_props.route.params?.phone) {
        setPhone(_props.route.params?.phone)
      }      
    }
  }

  const requestRegister = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (userName === "") {
      ApiService.showMessageBox(translate("errors.enterName"))
    } else {
      requestRegisterPortalUser(
        selectPhoneCountryCode,         
        phone, 
        (response) => {
          console.log("requestRegisterPortalUser: " + JSON.stringify(response))
          goToOtpScreen()          
        },
        (error) => {
          console.log("requestRegisterPortalUser error: " + JSON.stringify(error))
        }
      )
    }
  }

  const goToOtpScreen = () => {
    _props.navigation.navigate("InputOtp",
    {
      phone: phone,
      phoneCountryCode: selectPhoneCountryCode,
      userName: userName
    
    })
  }

  useEffect(() => {
    prepareData()
  }, [])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ padding: Dimen.padding.base, height: "100%"}}
      KeyboardAvoidingViewProps={{behavior: "padding"}}
      safeAreaEdges={["top", "bottom"]}
    >
      <MIcon 
        onPress={() => {_props.navigation.goBack()}}
        name="closeBlack" 
        size={Dimen.iconSize.md} />
      <Text style={[FSIZE_24, BOLD, MARGIN_L_TOP]} text={translate("boarding.inputYourName")} />
      <TextInput    
        allowFontScaling={false}     
        onChangeText={(text) => setUserName(text)}
        style={TEXT_INPUT} />
      <View style={FLEX}></View>
      <Text 
        onPress={() => {requestRegister()}}
        style={[BTN_CONTAINER, FSIZE_17, {marginHorizontal: 0}]} 
        text={translate("boarding.submitRegister")} />      
    </Screen>
  )
})
