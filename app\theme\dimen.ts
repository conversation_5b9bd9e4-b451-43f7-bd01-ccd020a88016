import { Dimensions, Platform } from "react-native";

export const Dimen = {
    screenWidth: Dimensions.get('window').width,
    screenHeight: Dimensions.get('window').height,
    swipeOpenListItem: -Dimensions.get('window').width * 0.2,
    keyboardOffset: 5,
    borderRadius: 6,
    borderRadiusLarge: 8,
    textInputHeight: {
        base: 20,
    },
    imageSendSize: 1000 ,
    fontSize: {
        ssm: Platform.OS === "ios" ? 12 : 11,
        sm: Platform.OS === "ios" ? 14 : 13,
        base: Platform.OS === "ios" ? 16 : 15,
        md: Platform.OS === "ios" ? 18 : 17,
        lg: Platform.OS === "ios" ? 20: 19,
        xl: Platform.OS === "ios" ? 24: 23,
        xxl: Platform.OS === "ios" ? 28: 27,
        xxxl: Platform.OS === "ios" ? 32: 31
    },
    lineHeight: {
        base: 22,
        sm: 22,
        md: 26,
        lg: 28,
        xl: 32,
        xxl: 36,
        xxxl: 40
    },
    padding: {
        base: 16,
        ssm: 6,
        sm: 12,
        md: 18,
        lg: 24,
        xl: 32,
        xxl: 48,
    },
    iconSize: {
        base: 16,
        sm: 13,
        md: 20,
        lg: 24,
        xl: 32,
        xxl: 48
    }
}