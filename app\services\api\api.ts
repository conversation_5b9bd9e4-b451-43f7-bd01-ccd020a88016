/**
 * This Api class lets you define an API endpoint and methods to request
 * data and process it.
 *
 * See the [Backend API Integration](https://github.com/infinitered/ignite/blob/master/docs/Backend-API-Integration.md)
 * documentation for more details.
 */
import {
  ApisauceInstance,
  create,
} from "apisauce"
import Config from "../../config"
import type {
  ApiConfig,
} from "./api.types"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { translate } from "app/i18n"
import { createDomainFailedLog } from "app/api/model"


console.log('API module initialized');

export const getAvailableServiceDomain = (onAllDomainsFailed?: () => void): string => {
  const serviceDomains = AppStorage.getServiceDomains();

  if (serviceDomains && serviceDomains.length > 0) {
    const firstDomain = serviceDomains[0];
    console.log(`Using first available service domain: ${firstDomain}`);
    return `${firstDomain}`;
  }

  console.log('No available service domains, using default API URL');
  if (onAllDomainsFailed) {
    onAllDomainsFailed();
  }
  return Helper.getAppConfig().API_URL;
}
export const getAvailableWebPortalURL = (onAllDomainsFailed?: () => void): string => {
  const webPortals = AppStorage.getWebPortals();

  if (webPortals && webPortals.length > 0) {
    const firstWebPortal = webPortals[0];
    console.log(`Using first available web portal: ${firstWebPortal}`);
    return `${firstWebPortal}`;
  }

  console.log('No available web portals, using default Web Portal URL');
  if (onAllDomainsFailed) {
    onAllDomainsFailed();
  }
  return Helper.getAppConfig().PORTAL_URL;
}

/**
 * Configuring the apisauce instance.
 */
export const DEFAULT_API_CONFIG: ApiConfig = {
  url: "", // Will be set when Api class is instantiated
  timeout: 10000,
}

/**
 * Manages all requests to the API. You can use this class to build out
 * various requests that you need to call from your backend API.
 */
export class Api {
  apisauce: ApisauceInstance
  config: ApiConfig

  /**
   * Set up our API instance. Keep this lightweight!
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG, private onAllDomainsFailed?: () => void) {
    console.log('Api class constructor called');

    // Get the service domain if not provided in config
    const finalConfig = {
      ...config,
      url: config.url || getAvailableServiceDomain(this.onAllDomainsFailed)
    };

    console.log('Base URL:', finalConfig.url);
    this.config = finalConfig
    this.apisauce = create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        Accept: "application/json",
      },
    })
    console.log('Apisauce instance created with baseURL:', this.apisauce.getBaseURL());
  }

}

// Export the Api class for lazy initialization
export function createApiInstance(config?: ApiConfig) {
  return new Api(config);
}