/**
 * This Api class lets you define an API endpoint and methods to request
 * data and process it.
 *
 * See the [Backend API Integration](https://github.com/infinitered/ignite/blob/master/docs/Backend-API-Integration.md)
 * documentation for more details.
 */
import {
  ApisauceInstance,
  create,
} from "apisauce"
import Config from "../../config"
import type {
  ApiConfig,
} from "./api.types"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { translate } from "app/i18n"
import { createDomainFailedLog } from "app/api/model"


console.log('API module initialized');

let currentServiceDomainIndex = 0;
export let failedDomains = new Set<string>();

export const getAvailableServiceDomain = (onAllDomainsFailed?: () => void) => {
  let serviceDomains: string[] = [];

  // Try to get stored domains if AppStorage is available
  if (AppStorage && typeof AppStorage.getServiceDomains === 'function') {
    serviceDomains = AppStorage.getServiceDomains() || [];
    console.log('AppStorage.getServiceDomains(): ', AppStorage.getServiceDomains());
    console.log('AppStorage.getSecuredDomains(): ', AppStorage.getSecureDomains());
  }

  // If no stored domains or AppStorage not ready, fall back to config
  if (serviceDomains.length === 0) {
    console.log('No stored service domains - falling back to config');
    serviceDomains = [Helper.getAppConfig().API_URL];
  } else {
    console.log('Using stored service domains');
  }

  console.log(`Available service domains: ${serviceDomains.join(', ')}`);
  console.log(`Failed domains: ${Array.from(failedDomains).join(', ')}`);

  // Reset if all domains have failed
  if (failedDomains.size >= serviceDomains.length) {
    failedDomains.clear();
    currentServiceDomainIndex = 0;
    console.log('All domains failed, resetting domain tracking');
    // Call the provided callback if available
    if (onAllDomainsFailed) {
      onAllDomainsFailed();
    }
    return ""; // Return empty string to prevent further requests
  }

  // Find next available domain
  while (currentServiceDomainIndex < serviceDomains.length) {
    const domain = serviceDomains[currentServiceDomainIndex];
    if (!failedDomains.has(domain)) {
      console.log(`Selected domain: ${domain}`);
      return domain;
    }
    currentServiceDomainIndex++;
  }

  // Fallback to first domain
  currentServiceDomainIndex = 0;
  console.log(`Falling back to first domain: ${serviceDomains[0]}`);
  return serviceDomains[0];
};

/**
 * Configuring the apisauce instance.
 */
export const DEFAULT_API_CONFIG: ApiConfig = {
  url: "", // Will be set when Api class is instantiated
  timeout: 10000,
}

/**
 * Manages all requests to the API. You can use this class to build out
 * various requests that you need to call from your backend API.
 */
export class Api {
  apisauce: ApisauceInstance
  config: ApiConfig

  /**
   * Set up our API instance. Keep this lightweight!
   */
  constructor(config: ApiConfig = DEFAULT_API_CONFIG, private onAllDomainsFailed?: () => void) {
    console.log('Api class constructor called');

    // Get the service domain if not provided in config
    const finalConfig = {
      ...config,
      url: config.url || getAvailableServiceDomain(this.onAllDomainsFailed)
    };

    console.log('Base URL:', finalConfig.url);
    this.config = finalConfig
    this.apisauce = create({
      baseURL: this.config.url,
      timeout: this.config.timeout,
      headers: {
        Accept: "application/json",
      },
    })
    console.log('Apisauce instance created with baseURL:', this.apisauce.getBaseURL());

    // Add response interceptor for domain switching
    this.apisauce.addResponseTransform((response) => {
      if ((!response.ok && response.status >= 500) || response.problem === 'NETWORK_ERROR') {
        const currentDomain = this.apisauce.getBaseURL()
        const domainWithoutProtocol = currentDomain.replace('https://', '')
        failedDomains.add(domainWithoutProtocol)

        console.log('creating Domain Failed Log for service domain (addResponseTransform): ', 'https://' + domainWithoutProtocol);
        createDomainFailedLog('https://' + domainWithoutProtocol,
            (response) => {
                console.log('Domain Failed Log response message: ', response.Message);
            },
            (error) => {
                console.log('Domain Failed Log error: ', error);
            }
        )

        console.log(`Domain ${domainWithoutProtocol} failed, marking as unavailable`);

        // Retry with next available domain
        const newDomain = getAvailableServiceDomain(this.onAllDomainsFailed);
        this.apisauce.setBaseURL(newDomain)
        console.log(`Switched to domain: ${newDomain}`);
        
        // Retry the request with new domain
        if (response.config) {
          return this.apisauce.axiosInstance.request({
            ...response.config,
            baseURL: newDomain
          });
        }
      }
      return undefined;
    })
  }

}

// Export the Api class for lazy initialization
export function createApiInstance(config?: ApiConfig) {
  return new Api(config);
}