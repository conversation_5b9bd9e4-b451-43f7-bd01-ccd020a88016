/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#include "FBReactNativeSpecJSI.h"

namespace facebook {
namespace react {

static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_vibrate(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->vibrate(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_vibrateByPattern(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->vibrateByPattern(rt, args[0].asObject(rt).asArray(rt), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeVibrationCxxSpecJSI_cancel(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeVibrationCxxSpecJSI *>(&turboModule)->cancel(rt);
  return jsi::Value::undefined();
}

NativeVibrationCxxSpecJSI::NativeVibrationCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Vibration", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeVibrationCxxSpecJSI_getConstants};
  methodMap_["vibrate"] = MethodMetadata {1, __hostFunction_NativeVibrationCxxSpecJSI_vibrate};
  methodMap_["vibrateByPattern"] = MethodMetadata {2, __hostFunction_NativeVibrationCxxSpecJSI_vibrateByPattern};
  methodMap_["cancel"] = MethodMetadata {0, __hostFunction_NativeVibrationCxxSpecJSI_cancel};
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_setValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->setValues(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeSettingsManagerCxxSpecJSI_deleteValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSettingsManagerCxxSpecJSI *>(&turboModule)->deleteValues(rt, args[0].asObject(rt).asArray(rt));
  return jsi::Value::undefined();
}

NativeSettingsManagerCxxSpecJSI::NativeSettingsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SettingsManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSettingsManagerCxxSpecJSI_getConstants};
  methodMap_["setValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerCxxSpecJSI_setValues};
  methodMap_["deleteValues"] = MethodMetadata {1, __hostFunction_NativeSettingsManagerCxxSpecJSI_deleteValues};
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_connect(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->connect(rt, args[0].asString(rt), args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt).asArray(rt)), args[2].asObject(rt), args[3].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_send(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->send(rt, args[0].asString(rt), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_sendBinary(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->sendBinary(rt, args[0].asString(rt), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_ping(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->ping(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_close(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->close(rt, args[0].asNumber(), args[1].asString(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeWebSocketModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeWebSocketModuleCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeWebSocketModuleCxxSpecJSI::NativeWebSocketModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("WebSocketModule", jsInvoker) {
  methodMap_["connect"] = MethodMetadata {4, __hostFunction_NativeWebSocketModuleCxxSpecJSI_connect};
  methodMap_["send"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleCxxSpecJSI_send};
  methodMap_["sendBinary"] = MethodMetadata {2, __hostFunction_NativeWebSocketModuleCxxSpecJSI_sendBinary};
  methodMap_["ping"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_ping};
  methodMap_["close"] = MethodMetadata {3, __hostFunction_NativeWebSocketModuleCxxSpecJSI_close};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeWebSocketModuleCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportFatalException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportFatalException(rt, args[0].asString(rt), args[1].asObject(rt).asArray(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportSoftException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportSoftException(rt, args[0].asString(rt), args[1].asObject(rt).asArray(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportException(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->reportException(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_updateExceptionMessage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->updateExceptionMessage(rt, args[0].asString(rt), args[1].asObject(rt).asArray(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeExceptionsManagerCxxSpecJSI_dismissRedbox(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeExceptionsManagerCxxSpecJSI *>(&turboModule)->dismissRedbox(rt);
  return jsi::Value::undefined();
}

NativeExceptionsManagerCxxSpecJSI::NativeExceptionsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ExceptionsManager", jsInvoker) {
  methodMap_["reportFatalException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportFatalException};
  methodMap_["reportSoftException"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportSoftException};
  methodMap_["reportException"] = MethodMetadata {1, __hostFunction_NativeExceptionsManagerCxxSpecJSI_reportException};
  methodMap_["updateExceptionMessage"] = MethodMetadata {3, __hostFunction_NativeExceptionsManagerCxxSpecJSI_updateExceptionMessage};
  methodMap_["dismissRedbox"] = MethodMetadata {0, __hostFunction_NativeExceptionsManagerCxxSpecJSI_dismissRedbox};
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_createTimer(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->createTimer(rt, args[0].asNumber(), args[1].asNumber(), args[2].asNumber(), args[3].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_deleteTimer(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->deleteTimer(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeTimingCxxSpecJSI_setSendIdleEvents(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeTimingCxxSpecJSI *>(&turboModule)->setSendIdleEvents(rt, args[0].asBool());
  return jsi::Value::undefined();
}

NativeTimingCxxSpecJSI::NativeTimingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Timing", jsInvoker) {
  methodMap_["createTimer"] = MethodMetadata {4, __hostFunction_NativeTimingCxxSpecJSI_createTimer};
  methodMap_["deleteTimer"] = MethodMetadata {1, __hostFunction_NativeTimingCxxSpecJSI_deleteTimer};
  methodMap_["setSendIdleEvents"] = MethodMetadata {1, __hostFunction_NativeTimingCxxSpecJSI_setSendIdleEvents};
}
static jsi::Value __hostFunction_NativeSegmentFetcherCxxSpecJSI_fetchSegment(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSegmentFetcherCxxSpecJSI *>(&turboModule)->fetchSegment(rt, args[0].asNumber(), args[1].asObject(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeSegmentFetcherCxxSpecJSI_getSegment(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSegmentFetcherCxxSpecJSI *>(&turboModule)->getSegment(rt, args[0].asNumber(), args[1].asObject(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeSegmentFetcherCxxSpecJSI::NativeSegmentFetcherCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SegmentFetcher", jsInvoker) {
  methodMap_["fetchSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherCxxSpecJSI_fetchSegment};
  methodMap_["getSegment"] = MethodMetadata {3, __hostFunction_NativeSegmentFetcherCxxSpecJSI_getSegment};
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_checkPermission(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->checkPermission(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestPermission(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->requestPermission(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_shouldShowRequestPermissionRationale(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->shouldShowRequestPermissionRationale(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestMultiplePermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePermissionsAndroidCxxSpecJSI *>(&turboModule)->requestMultiplePermissions(rt, args[0].asObject(rt).asArray(rt));
}

NativePermissionsAndroidCxxSpecJSI::NativePermissionsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PermissionsAndroid", jsInvoker) {
  methodMap_["checkPermission"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_checkPermission};
  methodMap_["requestPermission"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestPermission};
  methodMap_["shouldShowRequestPermissionRationale"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_shouldShowRequestPermissionRationale};
  methodMap_["requestMultiplePermissions"] = MethodMetadata {1, __hostFunction_NativePermissionsAndroidCxxSpecJSI_requestMultiplePermissions};
}
static jsi::Value __hostFunction_NativeAlertManagerCxxSpecJSI_alertWithArgs(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAlertManagerCxxSpecJSI *>(&turboModule)->alertWithArgs(rt, args[0].asObject(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeAlertManagerCxxSpecJSI::NativeAlertManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AlertManager", jsInvoker) {
  methodMap_["alertWithArgs"] = MethodMetadata {2, __hostFunction_NativeAlertManagerCxxSpecJSI_alertWithArgs};
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_showActionSheetWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->showActionSheetWithOptions(rt, args[0].asObject(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_showShareActionSheetWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->showShareActionSheetWithOptions(rt, args[0].asObject(rt), args[1].asObject(rt).asFunction(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeActionSheetManagerCxxSpecJSI_dismissActionSheet(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeActionSheetManagerCxxSpecJSI *>(&turboModule)->dismissActionSheet(rt);
  return jsi::Value::undefined();
}

NativeActionSheetManagerCxxSpecJSI::NativeActionSheetManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ActionSheetManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeActionSheetManagerCxxSpecJSI_getConstants};
  methodMap_["showActionSheetWithOptions"] = MethodMetadata {2, __hostFunction_NativeActionSheetManagerCxxSpecJSI_showActionSheetWithOptions};
  methodMap_["showShareActionSheetWithOptions"] = MethodMetadata {3, __hostFunction_NativeActionSheetManagerCxxSpecJSI_showShareActionSheetWithOptions};
  methodMap_["dismissActionSheet"] = MethodMetadata {0, __hostFunction_NativeActionSheetManagerCxxSpecJSI_dismissActionSheet};
}
static jsi::Value __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDialogManagerAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_showAlert(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDialogManagerAndroidCxxSpecJSI *>(&turboModule)->showAlert(rt, args[0].asObject(rt), args[1].asObject(rt).asFunction(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeDialogManagerAndroidCxxSpecJSI::NativeDialogManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DialogManagerAndroid", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_getConstants};
  methodMap_["showAlert"] = MethodMetadata {3, __hostFunction_NativeDialogManagerAndroidCxxSpecJSI_showAlert};
}
static jsi::Value __hostFunction_NativeSourceCodeCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeSourceCodeCxxSpecJSI *>(&turboModule)->getConstants(rt);
}

NativeSourceCodeCxxSpecJSI::NativeSourceCodeCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SourceCode", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeSourceCodeCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->show(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_reload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->reload(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_debugRemotely(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->debugRemotely(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_setProfilingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->setProfilingEnabled(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevMenuCxxSpecJSI_setHotLoadingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevMenuCxxSpecJSI *>(&turboModule)->setHotLoadingEnabled(rt, args[0].asBool());
  return jsi::Value::undefined();
}

NativeDevMenuCxxSpecJSI::NativeDevMenuCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevMenu", jsInvoker) {
  methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeDevMenuCxxSpecJSI_show};
  methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevMenuCxxSpecJSI_reload};
  methodMap_["debugRemotely"] = MethodMetadata {1, __hostFunction_NativeDevMenuCxxSpecJSI_debugRemotely};
  methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuCxxSpecJSI_setProfilingEnabled};
  methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevMenuCxxSpecJSI_setHotLoadingEnabled};
}
static jsi::Value __hostFunction_NativeRedBoxCxxSpecJSI_setExtraData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRedBoxCxxSpecJSI *>(&turboModule)->setExtraData(rt, args[0].asObject(rt), args[1].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeRedBoxCxxSpecJSI_dismiss(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeRedBoxCxxSpecJSI *>(&turboModule)->dismiss(rt);
  return jsi::Value::undefined();
}

NativeRedBoxCxxSpecJSI::NativeRedBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("RedBox", jsInvoker) {
  methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeRedBoxCxxSpecJSI_setExtraData};
  methodMap_["dismiss"] = MethodMetadata {0, __hostFunction_NativeRedBoxCxxSpecJSI_dismiss};
}
static jsi::Value __hostFunction_NativeAnimationsDebugModuleCxxSpecJSI_startRecordingFps(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimationsDebugModuleCxxSpecJSI *>(&turboModule)->startRecordingFps(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimationsDebugModuleCxxSpecJSI_stopRecordingFps(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimationsDebugModuleCxxSpecJSI *>(&turboModule)->stopRecordingFps(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeAnimationsDebugModuleCxxSpecJSI::NativeAnimationsDebugModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AnimationsDebugModule", jsInvoker) {
  methodMap_["startRecordingFps"] = MethodMetadata {0, __hostFunction_NativeAnimationsDebugModuleCxxSpecJSI_startRecordingFps};
  methodMap_["stopRecordingFps"] = MethodMetadata {1, __hostFunction_NativeAnimationsDebugModuleCxxSpecJSI_stopRecordingFps};
}
static jsi::Value __hostFunction_NativeLogBoxCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLogBoxCxxSpecJSI *>(&turboModule)->show(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeLogBoxCxxSpecJSI_hide(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLogBoxCxxSpecJSI *>(&turboModule)->hide(rt);
  return jsi::Value::undefined();
}

NativeLogBoxCxxSpecJSI::NativeLogBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("LogBox", jsInvoker) {
  methodMap_["show"] = MethodMetadata {0, __hostFunction_NativeLogBoxCxxSpecJSI_show};
  methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeLogBoxCxxSpecJSI_hide};
}
static jsi::Value __hostFunction_NativeDeviceEventManagerCxxSpecJSI_invokeDefaultBackPressHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDeviceEventManagerCxxSpecJSI *>(&turboModule)->invokeDefaultBackPressHandler(rt);
  return jsi::Value::undefined();
}

NativeDeviceEventManagerCxxSpecJSI::NativeDeviceEventManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DeviceEventManager", jsInvoker) {
  methodMap_["invokeDefaultBackPressHandler"] = MethodMetadata {0, __hostFunction_NativeDeviceEventManagerCxxSpecJSI_invokeDefaultBackPressHandler};
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_reload(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->reload(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_reloadWithReason(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->reloadWithReason(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_onFastRefresh(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->onFastRefresh(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setHotLoadingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setHotLoadingEnabled(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setIsDebuggingRemotely(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setIsDebuggingRemotely(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setProfilingEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setProfilingEnabled(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_toggleElementInspector(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->toggleElementInspector(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_addMenuItem(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->addMenuItem(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevSettingsCxxSpecJSI_setIsShakeToShowDevMenuEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevSettingsCxxSpecJSI *>(&turboModule)->setIsShakeToShowDevMenuEnabled(rt, args[0].asBool());
  return jsi::Value::undefined();
}

NativeDevSettingsCxxSpecJSI::NativeDevSettingsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevSettings", jsInvoker) {
  methodMap_["reload"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_reload};
  methodMap_["reloadWithReason"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_reloadWithReason};
  methodMap_["onFastRefresh"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_onFastRefresh};
  methodMap_["setHotLoadingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setHotLoadingEnabled};
  methodMap_["setIsDebuggingRemotely"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setIsDebuggingRemotely};
  methodMap_["setProfilingEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setProfilingEnabled};
  methodMap_["toggleElementInspector"] = MethodMetadata {0, __hostFunction_NativeDevSettingsCxxSpecJSI_toggleElementInspector};
  methodMap_["addMenuItem"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_addMenuItem};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_removeListeners};
  methodMap_["setIsShakeToShowDevMenuEnabled"] = MethodMetadata {1, __hostFunction_NativeDevSettingsCxxSpecJSI_setIsShakeToShowDevMenuEnabled};
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_sendRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->sendRequest(rt, args[0].asString(rt), args[1].asString(rt), args[2].asNumber(), args[3].asObject(rt).asArray(rt), args[4].asObject(rt), args[5].asString(rt), args[6].asBool(), args[7].asNumber(), args[8].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->abortRequest(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_clearCookies(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->clearCookies(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingAndroidCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingAndroidCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeNetworkingAndroidCxxSpecJSI::NativeNetworkingAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Networking", jsInvoker) {
  methodMap_["sendRequest"] = MethodMetadata {9, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_sendRequest};
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_abortRequest};
  methodMap_["clearCookies"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_clearCookies};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeNetworkingAndroidCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_sendRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->sendRequest(rt, args[0].asObject(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->abortRequest(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_clearCookies(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->clearCookies(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeNetworkingIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeNetworkingIOSCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeNetworkingIOSCxxSpecJSI::NativeNetworkingIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Networking", jsInvoker) {
  methodMap_["sendRequest"] = MethodMetadata {2, __hostFunction_NativeNetworkingIOSCxxSpecJSI_sendRequest};
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_abortRequest};
  methodMap_["clearCookies"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_clearCookies};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeNetworkingIOSCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getSize(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSizeWithHeaders(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->getSizeWithHeaders(rt, args[0].asString(rt), args[1].asObject(rt));
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->prefetchImage(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImageWithMetadata(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->prefetchImageWithMetadata(rt, args[0].asString(rt), args[1].asString(rt), args[2].getNumber());
}
static jsi::Value __hostFunction_NativeImageLoaderIOSCxxSpecJSI_queryCache(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderIOSCxxSpecJSI *>(&turboModule)->queryCache(rt, args[0].asObject(rt).asArray(rt));
}

NativeImageLoaderIOSCxxSpecJSI::NativeImageLoaderIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageLoader", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getConstants};
  methodMap_["getSize"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSize};
  methodMap_["getSizeWithHeaders"] = MethodMetadata {2, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_getSizeWithHeaders};
  methodMap_["prefetchImage"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImage};
  methodMap_["prefetchImageWithMetadata"] = MethodMetadata {3, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_prefetchImageWithMetadata};
  methodMap_["queryCache"] = MethodMetadata {1, __hostFunction_NativeImageLoaderIOSCxxSpecJSI_queryCache};
}
static jsi::Value __hostFunction_NativeImageEditorCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageEditorCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeImageEditorCxxSpecJSI_cropImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageEditorCxxSpecJSI *>(&turboModule)->cropImage(rt, args[0].asString(rt), args[1].asObject(rt), args[2].asObject(rt).asFunction(rt), args[3].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeImageEditorCxxSpecJSI::NativeImageEditorCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageEditingManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageEditorCxxSpecJSI_getConstants};
  methodMap_["cropImage"] = MethodMetadata {4, __hostFunction_NativeImageEditorCxxSpecJSI_cropImage};
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_getBase64ForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->getBase64ForTag(rt, args[0].asString(rt), args[1].asObject(rt).asFunction(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_hasImageForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->hasImageForTag(rt, args[0].asString(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_removeImageForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->removeImageForTag(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageStoreIOSCxxSpecJSI_addImageFromBase64(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreIOSCxxSpecJSI *>(&turboModule)->addImageFromBase64(rt, args[0].asString(rt), args[1].asObject(rt).asFunction(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeImageStoreIOSCxxSpecJSI::NativeImageStoreIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageStoreManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageStoreIOSCxxSpecJSI_getConstants};
  methodMap_["getBase64ForTag"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSCxxSpecJSI_getBase64ForTag};
  methodMap_["hasImageForTag"] = MethodMetadata {2, __hostFunction_NativeImageStoreIOSCxxSpecJSI_hasImageForTag};
  methodMap_["removeImageForTag"] = MethodMetadata {1, __hostFunction_NativeImageStoreIOSCxxSpecJSI_removeImageForTag};
  methodMap_["addImageFromBase64"] = MethodMetadata {3, __hostFunction_NativeImageStoreIOSCxxSpecJSI_addImageFromBase64};
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_abortRequest(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->abortRequest(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSize(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getSize(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSizeWithHeaders(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->getSizeWithHeaders(rt, args[0].asString(rt), args[1].asObject(rt));
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_prefetchImage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->prefetchImage(rt, args[0].asString(rt), args[1].asNumber());
}
static jsi::Value __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_queryCache(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageLoaderAndroidCxxSpecJSI *>(&turboModule)->queryCache(rt, args[0].asObject(rt).asArray(rt));
}

NativeImageLoaderAndroidCxxSpecJSI::NativeImageLoaderAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageLoader", jsInvoker) {
  methodMap_["abortRequest"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_abortRequest};
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getConstants};
  methodMap_["getSize"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSize};
  methodMap_["getSizeWithHeaders"] = MethodMetadata {2, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_getSizeWithHeaders};
  methodMap_["prefetchImage"] = MethodMetadata {2, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_prefetchImage};
  methodMap_["queryCache"] = MethodMetadata {1, __hostFunction_NativeImageLoaderAndroidCxxSpecJSI_queryCache};
}
static jsi::Value __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeImageStoreAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getBase64ForTag(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeImageStoreAndroidCxxSpecJSI *>(&turboModule)->getBase64ForTag(rt, args[0].asString(rt), args[1].asObject(rt).asFunction(rt), args[2].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeImageStoreAndroidCxxSpecJSI::NativeImageStoreAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ImageStoreManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getConstants};
  methodMap_["getBase64ForTag"] = MethodMetadata {3, __hostFunction_NativeImageStoreAndroidCxxSpecJSI_getBase64ForTag};
}
static jsi::Value __hostFunction_NativeDeviceInfoCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDeviceInfoCxxSpecJSI *>(&turboModule)->getConstants(rt);
}

NativeDeviceInfoCxxSpecJSI::NativeDeviceInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DeviceInfo", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeDeviceInfoCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativePlatformConstantsIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsIOSCxxSpecJSI *>(&turboModule)->getConstants(rt);
}

NativePlatformConstantsIOSCxxSpecJSI::NativePlatformConstantsIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PlatformConstants", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsIOSCxxSpecJSI_getConstants};
}
static jsi::Value __hostFunction_NativeDevLoadingViewCxxSpecJSI_showMessage(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevLoadingViewCxxSpecJSI *>(&turboModule)->showMessage(rt, args[0].asString(rt), args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asNumber()), args[2].isNull() || args[2].isUndefined() ? std::nullopt : std::make_optional(args[2].asNumber()));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeDevLoadingViewCxxSpecJSI_hide(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeDevLoadingViewCxxSpecJSI *>(&turboModule)->hide(rt);
  return jsi::Value::undefined();
}

NativeDevLoadingViewCxxSpecJSI::NativeDevLoadingViewCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevLoadingView", jsInvoker) {
  methodMap_["showMessage"] = MethodMetadata {3, __hostFunction_NativeDevLoadingViewCxxSpecJSI_showMessage};
  methodMap_["hide"] = MethodMetadata {0, __hostFunction_NativeDevLoadingViewCxxSpecJSI_hide};
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_getColorScheme(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  auto result = static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->getColorScheme(rt);
  return result ? jsi::Value(std::move(*result)) : jsi::Value::null();
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppearanceCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppearanceCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeAppearanceCxxSpecJSI::NativeAppearanceCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Appearance", jsInvoker) {
  methodMap_["getColorScheme"] = MethodMetadata {0, __hostFunction_NativeAppearanceCxxSpecJSI_getColorScheme};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppearanceCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppearanceCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getAndroidID(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePlatformConstantsAndroidCxxSpecJSI *>(&turboModule)->getAndroidID(rt);
}

NativePlatformConstantsAndroidCxxSpecJSI::NativePlatformConstantsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PlatformConstants", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getConstants};
  methodMap_["getAndroidID"] = MethodMetadata {0, __hostFunction_NativePlatformConstantsAndroidCxxSpecJSI_getAndroidID};
}
static jsi::Value __hostFunction_NativeDevSplitBundleLoaderCxxSpecJSI_loadBundle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDevSplitBundleLoaderCxxSpecJSI *>(&turboModule)->loadBundle(rt, args[0].asString(rt));
}

NativeDevSplitBundleLoaderCxxSpecJSI::NativeDevSplitBundleLoaderCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DevSplitBundleLoader", jsInvoker) {
  methodMap_["loadBundle"] = MethodMetadata {1, __hostFunction_NativeDevSplitBundleLoaderCxxSpecJSI_loadBundle};
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getHeight(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->getHeight(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setNetworkActivityIndicatorVisible(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setNetworkActivityIndicatorVisible(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setStyle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setStyle(rt, count < 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt)), args[1].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setHidden(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerIOSCxxSpecJSI *>(&turboModule)->setHidden(rt, args[0].asBool(), args[1].asString(rt));
  return jsi::Value::undefined();
}

NativeStatusBarManagerIOSCxxSpecJSI::NativeStatusBarManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("StatusBarManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getConstants};
  methodMap_["getHeight"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_getHeight};
  methodMap_["setNetworkActivityIndicatorVisible"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setNetworkActivityIndicatorVisible};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_removeListeners};
  methodMap_["setStyle"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setStyle};
  methodMap_["setHidden"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerIOSCxxSpecJSI_setHidden};
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setColor(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setColor(rt, args[0].asNumber(), args[1].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setTranslucent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setTranslucent(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setStyle(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setStyle(rt, count < 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asString(rt)));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setHidden(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeStatusBarManagerAndroidCxxSpecJSI *>(&turboModule)->setHidden(rt, args[0].asBool());
  return jsi::Value::undefined();
}

NativeStatusBarManagerAndroidCxxSpecJSI::NativeStatusBarManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("StatusBarManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_getConstants};
  methodMap_["setColor"] = MethodMetadata {2, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setColor};
  methodMap_["setTranslucent"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setTranslucent};
  methodMap_["setStyle"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setStyle};
  methodMap_["setHidden"] = MethodMetadata {1, __hostFunction_NativeStatusBarManagerAndroidCxxSpecJSI_setHidden};
}
static jsi::Value __hostFunction_NativeDatePickerAndroidCxxSpecJSI_open(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeDatePickerAndroidCxxSpecJSI *>(&turboModule)->open(rt, args[0].asObject(rt));
}

NativeDatePickerAndroidCxxSpecJSI::NativeDatePickerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("DatePickerAndroid", jsInvoker) {
  methodMap_["open"] = MethodMetadata {1, __hostFunction_NativeDatePickerAndroidCxxSpecJSI_open};
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentBoldTextState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentBoldTextState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentGrayscaleState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentGrayscaleState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentInvertColorsState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentInvertColorsState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceMotionState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentReduceMotionState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentPrefersCrossFadeTransitionsState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentPrefersCrossFadeTransitionsState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceTransparencyState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentReduceTransparencyState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentVoiceOverState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->getCurrentVoiceOverState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityContentSizeMultipliers(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->setAccessibilityContentSizeMultipliers(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityFocus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->setAccessibilityFocus(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibility(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->announceForAccessibility(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibilityWithOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityManagerCxxSpecJSI *>(&turboModule)->announceForAccessibilityWithOptions(rt, args[0].asString(rt), args[1].asObject(rt));
  return jsi::Value::undefined();
}

NativeAccessibilityManagerCxxSpecJSI::NativeAccessibilityManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AccessibilityManager", jsInvoker) {
  methodMap_["getCurrentBoldTextState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentBoldTextState};
  methodMap_["getCurrentGrayscaleState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentGrayscaleState};
  methodMap_["getCurrentInvertColorsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentInvertColorsState};
  methodMap_["getCurrentReduceMotionState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceMotionState};
  methodMap_["getCurrentPrefersCrossFadeTransitionsState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentPrefersCrossFadeTransitionsState};
  methodMap_["getCurrentReduceTransparencyState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentReduceTransparencyState};
  methodMap_["getCurrentVoiceOverState"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_getCurrentVoiceOverState};
  methodMap_["setAccessibilityContentSizeMultipliers"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityContentSizeMultipliers};
  methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_setAccessibilityFocus};
  methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibility};
  methodMap_["announceForAccessibilityWithOptions"] = MethodMetadata {2, __hostFunction_NativeAccessibilityManagerCxxSpecJSI_announceForAccessibilityWithOptions};
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isReduceMotionEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isReduceMotionEnabled(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isTouchExplorationEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isTouchExplorationEnabled(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isAccessibilityServiceEnabled(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->isAccessibilityServiceEnabled(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_setAccessibilityFocus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->setAccessibilityFocus(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_announceForAccessibility(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->announceForAccessibility(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAccessibilityInfoCxxSpecJSI_getRecommendedTimeoutMillis(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAccessibilityInfoCxxSpecJSI *>(&turboModule)->getRecommendedTimeoutMillis(rt, args[0].asNumber(), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}

NativeAccessibilityInfoCxxSpecJSI::NativeAccessibilityInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AccessibilityInfo", jsInvoker) {
  methodMap_["isReduceMotionEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isReduceMotionEnabled};
  methodMap_["isTouchExplorationEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isTouchExplorationEnabled};
  methodMap_["isAccessibilityServiceEnabled"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_isAccessibilityServiceEnabled};
  methodMap_["setAccessibilityFocus"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_setAccessibilityFocus};
  methodMap_["announceForAccessibility"] = MethodMetadata {1, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_announceForAccessibility};
  methodMap_["getRecommendedTimeoutMillis"] = MethodMetadata {2, __hostFunction_NativeAccessibilityInfoCxxSpecJSI_getRecommendedTimeoutMillis};
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_show(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->show(rt, args[0].asString(rt), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravity(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->showWithGravity(rt, args[0].asString(rt), args[1].asNumber(), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravityAndOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeToastAndroidCxxSpecJSI *>(&turboModule)->showWithGravityAndOffset(rt, args[0].asString(rt), args[1].asNumber(), args[2].asNumber(), args[3].asNumber(), args[4].asNumber());
  return jsi::Value::undefined();
}

NativeToastAndroidCxxSpecJSI::NativeToastAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ToastAndroid", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeToastAndroidCxxSpecJSI_getConstants};
  methodMap_["show"] = MethodMetadata {2, __hostFunction_NativeToastAndroidCxxSpecJSI_show};
  methodMap_["showWithGravity"] = MethodMetadata {3, __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravity};
  methodMap_["showWithGravityAndOffset"] = MethodMetadata {5, __hostFunction_NativeToastAndroidCxxSpecJSI_showWithGravityAndOffset};
}
static jsi::Value __hostFunction_NativeSoundManagerCxxSpecJSI_playTouchSound(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeSoundManagerCxxSpecJSI *>(&turboModule)->playTouchSound(rt);
  return jsi::Value::undefined();
}

NativeSoundManagerCxxSpecJSI::NativeSoundManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("SoundManager", jsInvoker) {
  methodMap_["playTouchSound"] = MethodMetadata {0, __hostFunction_NativeSoundManagerCxxSpecJSI_playTouchSound};
}
static jsi::Value __hostFunction_NativeKeyboardObserverCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeKeyboardObserverCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeKeyboardObserverCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeKeyboardObserverCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeKeyboardObserverCxxSpecJSI::NativeKeyboardObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("KeyboardObserver", jsInvoker) {
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeKeyboardObserverCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_getString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->getString(rt);
}
static jsi::Value __hostFunction_NativeClipboardCxxSpecJSI_setString(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeClipboardCxxSpecJSI *>(&turboModule)->setString(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}

NativeClipboardCxxSpecJSI::NativeClipboardCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("Clipboard", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeClipboardCxxSpecJSI_getConstants};
  methodMap_["getString"] = MethodMetadata {0, __hostFunction_NativeClipboardCxxSpecJSI_getString};
  methodMap_["setString"] = MethodMetadata {1, __hostFunction_NativeClipboardCxxSpecJSI_setString};
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_getCurrentAppState(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->getCurrentAppState(rt, args[0].asObject(rt).asFunction(rt), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAppStateCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAppStateCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeAppStateCxxSpecJSI::NativeAppStateCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("AppState", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeAppStateCxxSpecJSI_getConstants};
  methodMap_["getCurrentAppState"] = MethodMetadata {2, __hostFunction_NativeAppStateCxxSpecJSI_getCurrentAppState};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAppStateCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAppStateCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativePerformanceObserverCxxSpecJSI_startReporting(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceObserverCxxSpecJSI *>(&turboModule)->startReporting(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceObserverCxxSpecJSI_stopReporting(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceObserverCxxSpecJSI *>(&turboModule)->stopReporting(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePerformanceObserverCxxSpecJSI_getPendingEntries(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePerformanceObserverCxxSpecJSI *>(&turboModule)->getPendingEntries(rt);
}
static jsi::Value __hostFunction_NativePerformanceObserverCxxSpecJSI_setOnPerformanceEntryCallback(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePerformanceObserverCxxSpecJSI *>(&turboModule)->setOnPerformanceEntryCallback(rt, count < 0 || args[0].isNull() || args[0].isUndefined() ? std::nullopt : std::make_optional(args[0].asObject(rt).asFunction(rt)));
  return jsi::Value::undefined();
}

NativePerformanceObserverCxxSpecJSI::NativePerformanceObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativePerformanceObserverCxx", jsInvoker) {
  methodMap_["startReporting"] = MethodMetadata {1, __hostFunction_NativePerformanceObserverCxxSpecJSI_startReporting};
  methodMap_["stopReporting"] = MethodMetadata {1, __hostFunction_NativePerformanceObserverCxxSpecJSI_stopReporting};
  methodMap_["getPendingEntries"] = MethodMetadata {0, __hostFunction_NativePerformanceObserverCxxSpecJSI_getPendingEntries};
  methodMap_["setOnPerformanceEntryCallback"] = MethodMetadata {1, __hostFunction_NativePerformanceObserverCxxSpecJSI_setOnPerformanceEntryCallback};
}
static jsi::Value __hostFunction_NativeBugReportingCxxSpecJSI_startReportAProblemFlow(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBugReportingCxxSpecJSI *>(&turboModule)->startReportAProblemFlow(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBugReportingCxxSpecJSI_setExtraData(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBugReportingCxxSpecJSI *>(&turboModule)->setExtraData(rt, args[0].asObject(rt), args[1].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBugReportingCxxSpecJSI_setCategoryID(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBugReportingCxxSpecJSI *>(&turboModule)->setCategoryID(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}

NativeBugReportingCxxSpecJSI::NativeBugReportingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("BugReporting", jsInvoker) {
  methodMap_["startReportAProblemFlow"] = MethodMetadata {0, __hostFunction_NativeBugReportingCxxSpecJSI_startReportAProblemFlow};
  methodMap_["setExtraData"] = MethodMetadata {2, __hostFunction_NativeBugReportingCxxSpecJSI_setExtraData};
  methodMap_["setCategoryID"] = MethodMetadata {1, __hostFunction_NativeBugReportingCxxSpecJSI_setCategoryID};
}
static jsi::Value __hostFunction_NativeJSCHeapCaptureCxxSpecJSI_captureComplete(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeJSCHeapCaptureCxxSpecJSI *>(&turboModule)->captureComplete(rt, args[0].asString(rt), args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asString(rt)));
  return jsi::Value::undefined();
}

NativeJSCHeapCaptureCxxSpecJSI::NativeJSCHeapCaptureCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("JSCHeapCapture", jsInvoker) {
  methodMap_["captureComplete"] = MethodMetadata {2, __hostFunction_NativeJSCHeapCaptureCxxSpecJSI_captureComplete};
}
static jsi::Value __hostFunction_NativeJSCSamplingProfilerCxxSpecJSI_operationComplete(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeJSCSamplingProfilerCxxSpecJSI *>(&turboModule)->operationComplete(rt, args[0].asNumber(), args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asString(rt)), args[2].isNull() || args[2].isUndefined() ? std::nullopt : std::make_optional(args[2].asString(rt)));
  return jsi::Value::undefined();
}

NativeJSCSamplingProfilerCxxSpecJSI::NativeJSCSamplingProfilerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("JSCSamplingProfiler", jsInvoker) {
  methodMap_["operationComplete"] = MethodMetadata {3, __hostFunction_NativeJSCSamplingProfilerCxxSpecJSI_operationComplete};
}
static jsi::Value __hostFunction_NativeModalManagerCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeModalManagerCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeModalManagerCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeModalManagerCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeModalManagerCxxSpecJSI::NativeModalManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ModalManager", jsInvoker) {
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeModalManagerCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeModalManagerCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsDataURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFileReaderModuleCxxSpecJSI *>(&turboModule)->readAsDataURL(rt, args[0].asObject(rt));
}
static jsi::Value __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsText(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeFileReaderModuleCxxSpecJSI *>(&turboModule)->readAsText(rt, args[0].asObject(rt), args[1].asString(rt));
}

NativeFileReaderModuleCxxSpecJSI::NativeFileReaderModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("FileReaderModule", jsInvoker) {
  methodMap_["readAsDataURL"] = MethodMetadata {1, __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsDataURL};
  methodMap_["readAsText"] = MethodMetadata {2, __hostFunction_NativeFileReaderModuleCxxSpecJSI_readAsText};
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_addNetworkingHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->addNetworkingHandler(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_addWebSocketHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->addWebSocketHandler(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_removeWebSocketHandler(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->removeWebSocketHandler(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_sendOverSocket(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->sendOverSocket(rt, args[0].asObject(rt), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_createFromParts(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->createFromParts(rt, args[0].asObject(rt).asArray(rt), args[1].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeBlobModuleCxxSpecJSI_release(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeBlobModuleCxxSpecJSI *>(&turboModule)->release(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}

NativeBlobModuleCxxSpecJSI::NativeBlobModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("BlobModule", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeBlobModuleCxxSpecJSI_getConstants};
  methodMap_["addNetworkingHandler"] = MethodMetadata {0, __hostFunction_NativeBlobModuleCxxSpecJSI_addNetworkingHandler};
  methodMap_["addWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_addWebSocketHandler};
  methodMap_["removeWebSocketHandler"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_removeWebSocketHandler};
  methodMap_["sendOverSocket"] = MethodMetadata {2, __hostFunction_NativeBlobModuleCxxSpecJSI_sendOverSocket};
  methodMap_["createFromParts"] = MethodMetadata {2, __hostFunction_NativeBlobModuleCxxSpecJSI_createFromParts};
  methodMap_["release"] = MethodMetadata {1, __hostFunction_NativeBlobModuleCxxSpecJSI_release};
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setGlobalOptions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->setGlobalOptions(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setContext(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->setContext(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_beginScroll(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->beginScroll(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeFrameRateLoggerCxxSpecJSI_endScroll(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeFrameRateLoggerCxxSpecJSI *>(&turboModule)->endScroll(rt);
  return jsi::Value::undefined();
}

NativeFrameRateLoggerCxxSpecJSI::NativeFrameRateLoggerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("FrameRateLogger", jsInvoker) {
  methodMap_["setGlobalOptions"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setGlobalOptions};
  methodMap_["setContext"] = MethodMetadata {1, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_setContext};
  methodMap_["beginScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_beginScroll};
  methodMap_["endScroll"] = MethodMetadata {0, __hostFunction_NativeFrameRateLoggerCxxSpecJSI_endScroll};
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_allowRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->allowRTL(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_forceRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->forceRTL(rt, args[0].asBool());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeI18nManagerCxxSpecJSI_swapLeftAndRightInRTL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeI18nManagerCxxSpecJSI *>(&turboModule)->swapLeftAndRightInRTL(rt, args[0].asBool());
  return jsi::Value::undefined();
}

NativeI18nManagerCxxSpecJSI::NativeI18nManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("I18nManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeI18nManagerCxxSpecJSI_getConstants};
  methodMap_["allowRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_allowRTL};
  methodMap_["forceRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_forceRTL};
  methodMap_["swapLeftAndRightInRTL"] = MethodMetadata {1, __hostFunction_NativeI18nManagerCxxSpecJSI_swapLeftAndRightInRTL};
}
static jsi::Value __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskFinished(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeHeadlessJsTaskSupportCxxSpecJSI *>(&turboModule)->notifyTaskFinished(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskRetry(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeHeadlessJsTaskSupportCxxSpecJSI *>(&turboModule)->notifyTaskRetry(rt, args[0].asNumber());
}

NativeHeadlessJsTaskSupportCxxSpecJSI::NativeHeadlessJsTaskSupportCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("HeadlessJsTaskSupport", jsInvoker) {
  methodMap_["notifyTaskFinished"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskFinished};
  methodMap_["notifyTaskRetry"] = MethodMetadata {1, __hostFunction_NativeHeadlessJsTaskSupportCxxSpecJSI_notifyTaskRetry};
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startOperationBatch(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_finishOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->finishOperationBatch(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_createAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->createAnimatedNode(rt, args[0].asNumber(), args[1].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_updateAnimatedNodeConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->updateAnimatedNodeConfig(rt, args[0].asNumber(), args[1].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_getValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->getValue(rt, args[0].asNumber(), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startListeningToAnimatedNodeValue(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->stopListeningToAnimatedNodeValue(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodes(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodes(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startAnimatingNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->startAnimatingNode(rt, args[0].asNumber(), args[1].asNumber(), args[2].asObject(rt), args[3].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopAnimation(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->stopAnimation(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeValue(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeOffset(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_flattenAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->flattenAnimatedNodeOffset(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_extractAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->extractAnimatedNodeOffset(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodeToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodeToView(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodeFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodeFromView(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_restoreDefaultValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->restoreDefaultValues(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_dropAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->dropAnimatedNode(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addAnimatedEventToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->addAnimatedEventToView(rt, args[0].asNumber(), args[1].asString(rt), args[2].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeAnimatedEventFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->removeAnimatedEventFromView(rt, args[0].asNumber(), args[1].asString(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_queueAndExecuteBatchedOperations(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedTurboModuleCxxSpecJSI *>(&turboModule)->queueAndExecuteBatchedOperations(rt, args[0].asObject(rt).asArray(rt));
  return jsi::Value::undefined();
}

NativeAnimatedTurboModuleCxxSpecJSI::NativeAnimatedTurboModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeAnimatedTurboModule", jsInvoker) {
  methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startOperationBatch};
  methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_finishOperationBatch};
  methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_createAnimatedNode};
  methodMap_["updateAnimatedNodeConfig"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_updateAnimatedNodeConfig};
  methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_getValue};
  methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startListeningToAnimatedNodeValue};
  methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopListeningToAnimatedNodeValue};
  methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodes};
  methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodes};
  methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_startAnimatingNode};
  methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_stopAnimation};
  methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeValue};
  methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_setAnimatedNodeOffset};
  methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_flattenAnimatedNodeOffset};
  methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_extractAnimatedNodeOffset};
  methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_connectAnimatedNodeToView};
  methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_disconnectAnimatedNodeFromView};
  methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_restoreDefaultValues};
  methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_dropAnimatedNode};
  methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addAnimatedEventToView};
  methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeAnimatedEventFromView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_removeListeners};
  methodMap_["queueAndExecuteBatchedOperations"] = MethodMetadata {1, __hostFunction_NativeAnimatedTurboModuleCxxSpecJSI_queueAndExecuteBatchedOperations};
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startOperationBatch(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_finishOperationBatch(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->finishOperationBatch(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_createAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->createAnimatedNode(rt, args[0].asNumber(), args[1].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_updateAnimatedNodeConfig(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->updateAnimatedNodeConfig(rt, args[0].asNumber(), args[1].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_getValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->getValue(rt, args[0].asNumber(), args[1].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startListeningToAnimatedNodeValue(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopListeningToAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->stopListeningToAnimatedNodeValue(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodes(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodes(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodes(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_startAnimatingNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->startAnimatingNode(rt, args[0].asNumber(), args[1].asNumber(), args[2].asObject(rt), args[3].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopAnimation(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->stopAnimation(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeValue(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeValue(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->setAnimatedNodeOffset(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_flattenAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->flattenAnimatedNodeOffset(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_extractAnimatedNodeOffset(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->extractAnimatedNodeOffset(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodeToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->connectAnimatedNodeToView(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodeFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->disconnectAnimatedNodeFromView(rt, args[0].asNumber(), args[1].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_restoreDefaultValues(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->restoreDefaultValues(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_dropAnimatedNode(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->dropAnimatedNode(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_addAnimatedEventToView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->addAnimatedEventToView(rt, args[0].asNumber(), args[1].asString(rt), args[2].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeAnimatedEventFromView(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->removeAnimatedEventFromView(rt, args[0].asNumber(), args[1].asString(rt), args[2].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeAnimatedModuleCxxSpecJSI_queueAndExecuteBatchedOperations(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeAnimatedModuleCxxSpecJSI *>(&turboModule)->queueAndExecuteBatchedOperations(rt, args[0].asObject(rt).asArray(rt));
  return jsi::Value::undefined();
}

NativeAnimatedModuleCxxSpecJSI::NativeAnimatedModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("NativeAnimatedModule", jsInvoker) {
  methodMap_["startOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startOperationBatch};
  methodMap_["finishOperationBatch"] = MethodMetadata {0, __hostFunction_NativeAnimatedModuleCxxSpecJSI_finishOperationBatch};
  methodMap_["createAnimatedNode"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_createAnimatedNode};
  methodMap_["updateAnimatedNodeConfig"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_updateAnimatedNodeConfig};
  methodMap_["getValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_getValue};
  methodMap_["startListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startListeningToAnimatedNodeValue};
  methodMap_["stopListeningToAnimatedNodeValue"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopListeningToAnimatedNodeValue};
  methodMap_["connectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodes};
  methodMap_["disconnectAnimatedNodes"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodes};
  methodMap_["startAnimatingNode"] = MethodMetadata {4, __hostFunction_NativeAnimatedModuleCxxSpecJSI_startAnimatingNode};
  methodMap_["stopAnimation"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_stopAnimation};
  methodMap_["setAnimatedNodeValue"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeValue};
  methodMap_["setAnimatedNodeOffset"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_setAnimatedNodeOffset};
  methodMap_["flattenAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_flattenAnimatedNodeOffset};
  methodMap_["extractAnimatedNodeOffset"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_extractAnimatedNodeOffset};
  methodMap_["connectAnimatedNodeToView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_connectAnimatedNodeToView};
  methodMap_["disconnectAnimatedNodeFromView"] = MethodMetadata {2, __hostFunction_NativeAnimatedModuleCxxSpecJSI_disconnectAnimatedNodeFromView};
  methodMap_["restoreDefaultValues"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_restoreDefaultValues};
  methodMap_["dropAnimatedNode"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_dropAnimatedNode};
  methodMap_["addAnimatedEventToView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleCxxSpecJSI_addAnimatedEventToView};
  methodMap_["removeAnimatedEventFromView"] = MethodMetadata {3, __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeAnimatedEventFromView};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_removeListeners};
  methodMap_["queueAndExecuteBatchedOperations"] = MethodMetadata {1, __hostFunction_NativeAnimatedModuleCxxSpecJSI_queueAndExecuteBatchedOperations};
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_onFinishRemoteNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->onFinishRemoteNotification(rt, args[0].asString(rt), args[1].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_setApplicationIconBadgeNumber(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->setApplicationIconBadgeNumber(rt, args[0].asNumber());
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getApplicationIconBadgeNumber(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getApplicationIconBadgeNumber(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_requestPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->requestPermissions(rt, args[0].asObject(rt));
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_abandonPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->abandonPermissions(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_checkPermissions(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->checkPermissions(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_presentLocalNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->presentLocalNotification(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_scheduleLocalNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->scheduleLocalNotification(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelAllLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->cancelAllLocalNotifications(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->cancelLocalNotifications(rt, args[0].asObject(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getInitialNotification(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getInitialNotification(rt);
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getScheduledLocalNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getScheduledLocalNotifications(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeAllDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeAllDeliveredNotifications(rt);
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeDeliveredNotifications(rt, args[0].asObject(rt).asArray(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getDeliveredNotifications(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getDeliveredNotifications(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getAuthorizationStatus(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->getAuthorizationStatus(rt, args[0].asObject(rt).asFunction(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativePushNotificationManagerIOSCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativePushNotificationManagerIOSCxxSpecJSI::NativePushNotificationManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("PushNotificationManager", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getConstants};
  methodMap_["onFinishRemoteNotification"] = MethodMetadata {2, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_onFinishRemoteNotification};
  methodMap_["setApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_setApplicationIconBadgeNumber};
  methodMap_["getApplicationIconBadgeNumber"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getApplicationIconBadgeNumber};
  methodMap_["requestPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_requestPermissions};
  methodMap_["abandonPermissions"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_abandonPermissions};
  methodMap_["checkPermissions"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_checkPermissions};
  methodMap_["presentLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_presentLocalNotification};
  methodMap_["scheduleLocalNotification"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_scheduleLocalNotification};
  methodMap_["cancelAllLocalNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelAllLocalNotifications};
  methodMap_["cancelLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_cancelLocalNotifications};
  methodMap_["getInitialNotification"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getInitialNotification};
  methodMap_["getScheduledLocalNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getScheduledLocalNotifications};
  methodMap_["removeAllDeliveredNotifications"] = MethodMetadata {0, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeAllDeliveredNotifications};
  methodMap_["removeDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeDeliveredNotifications};
  methodMap_["getDeliveredNotifications"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getDeliveredNotifications};
  methodMap_["getAuthorizationStatus"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_getAuthorizationStatus};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativePushNotificationManagerIOSCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_getInitialURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->getInitialURL(rt);
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_canOpenURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->canOpenURL(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_openURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->openURL(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_openSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->openSettings(rt);
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_addListener(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->addListener(rt, args[0].asString(rt));
  return jsi::Value::undefined();
}
static jsi::Value __hostFunction_NativeLinkingManagerCxxSpecJSI_removeListeners(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  static_cast<NativeLinkingManagerCxxSpecJSI *>(&turboModule)->removeListeners(rt, args[0].asNumber());
  return jsi::Value::undefined();
}

NativeLinkingManagerCxxSpecJSI::NativeLinkingManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("LinkingManager", jsInvoker) {
  methodMap_["getInitialURL"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerCxxSpecJSI_getInitialURL};
  methodMap_["canOpenURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_canOpenURL};
  methodMap_["openURL"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_openURL};
  methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeLinkingManagerCxxSpecJSI_openSettings};
  methodMap_["addListener"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_addListener};
  methodMap_["removeListeners"] = MethodMetadata {1, __hostFunction_NativeLinkingManagerCxxSpecJSI_removeListeners};
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_getInitialURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->getInitialURL(rt);
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_canOpenURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->canOpenURL(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_openURL(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->openURL(rt, args[0].asString(rt));
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_openSettings(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->openSettings(rt);
}
static jsi::Value __hostFunction_NativeIntentAndroidCxxSpecJSI_sendIntent(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeIntentAndroidCxxSpecJSI *>(&turboModule)->sendIntent(rt, args[0].asString(rt), args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asObject(rt).asArray(rt)));
}

NativeIntentAndroidCxxSpecJSI::NativeIntentAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("IntentAndroid", jsInvoker) {
  methodMap_["getInitialURL"] = MethodMetadata {0, __hostFunction_NativeIntentAndroidCxxSpecJSI_getInitialURL};
  methodMap_["canOpenURL"] = MethodMetadata {1, __hostFunction_NativeIntentAndroidCxxSpecJSI_canOpenURL};
  methodMap_["openURL"] = MethodMetadata {1, __hostFunction_NativeIntentAndroidCxxSpecJSI_openURL};
  methodMap_["openSettings"] = MethodMetadata {0, __hostFunction_NativeIntentAndroidCxxSpecJSI_openSettings};
  methodMap_["sendIntent"] = MethodMetadata {2, __hostFunction_NativeIntentAndroidCxxSpecJSI_sendIntent};
}
static jsi::Value __hostFunction_NativeShareModuleCxxSpecJSI_getConstants(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeShareModuleCxxSpecJSI *>(&turboModule)->getConstants(rt);
}
static jsi::Value __hostFunction_NativeShareModuleCxxSpecJSI_share(jsi::Runtime &rt, TurboModule &turboModule, const jsi::Value* args, size_t count) {
  return static_cast<NativeShareModuleCxxSpecJSI *>(&turboModule)->share(rt, args[0].asObject(rt), count < 1 || args[1].isNull() || args[1].isUndefined() ? std::nullopt : std::make_optional(args[1].asString(rt)));
}

NativeShareModuleCxxSpecJSI::NativeShareModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker)
  : TurboModule("ShareModule", jsInvoker) {
  methodMap_["getConstants"] = MethodMetadata {0, __hostFunction_NativeShareModuleCxxSpecJSI_getConstants};
  methodMap_["share"] = MethodMetadata {2, __hostFunction_NativeShareModuleCxxSpecJSI_share};
}


} // namespace react
} // namespace facebook
