import React, { useEffect, useState } from "react"
import {
  TouchableOpacity,
  View,
  Modal as RNModal,
  Platform,
  LogBox,
  NativeModules,
  AppState,
} from "react-native"
import { Dimen } from "app/theme/dimen"
import { MIcon } from "app/components/MIcon"
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome'
import { faTimes } from '@fortawesome/free-solid-svg-icons/faTimes'
import { Text as TextComponent } from ".."
import { translate } from "../../i18n"
import {
  MARGIN_S_DF_TOP,
  TEXT_CENTER,
} from "../../theme/mStyle"
import moment from "moment"
import { Helper } from "../../utils/helper"
import { observer } from "mobx-react-lite"
import { AppStorage } from "app/utils/appStorage"
import { updateDeviceId } from "app/api/model"
import Price from './Price';
import Text from './Text';
import * as AliyunPush from "aliyun-react-native-push";
import { AppLinkStorage } from "app/utils/appLinkStorage"
LogBox.ignoreLogs(['new NativeEventEmitter']);

const opened = [];

export const Modal = observer(function () {
  const [devId, setDevId] = useState('');
  const [reminders, setReminders] = useState([]);

  const addReminder = async (event): Promise<void> => {
    if (Platform.OS === 'android' && !(await AliyunPush.isAndroidNotificationEnabled())) {
      return;
    }

    let { title, body, summary, aps, extra, launch = false } = event;
    if (aps) {
      ({ title, body, summary, aps, ...extra } = event);
    }

    if (extra) {
      const { type, itemType, item, condition, price = 0 } = typeof extra === 'string' ? JSON.parse(extra) : extra;

      if (itemType || item) {
        setReminders(reminders => ([
          ...reminders,
          {
            price: {
              itemType: itemType || item,
              price,
              condition,
              type,
            },
            blank: launch,
          },
        ]));
      } else {
        // setReminders(reminders => ([
        //   ...reminders,
        //   {
        //     text: {
        //       text: body || summary,
        //     },
        //   },
        // ]));
      }
    }
  }

  useEffect(() => {
    AliyunPush.removePushCallback();

    const subscription = AppState.addEventListener("change", async nextAppState => {
      if (nextAppState === "active") {
        const latestNotification = await NativeModules.AliyunPushExtensionModule.getLatestNotification();
        if (latestNotification) {
          let { aps, extra, i } = latestNotification;
          if (aps) {
            ({ aps, ...extra } = latestNotification);
          }
          extra = typeof extra === 'string' ? JSON.parse(extra) : extra;
          const { _ALIYUN_NOTIFICATION_MSG_ID_ } = extra;
          if (opened.indexOf(_ALIYUN_NOTIFICATION_MSG_ID_ || i) !== -1) return;
          AppLinkStorage.handleDeepLinkInsideApp(extra.deeplink, AppLinkStorage.appLinkStorage.navigation);
          NativeModules.AliyunPushExtensionModule.clearLatestNotification();
          opened.push(_ALIYUN_NOTIFICATION_MSG_ID_ || i);
        }
      }
    });

    (async () => {
      try {
        const latestNotification = await NativeModules.AliyunPushExtensionModule.getLatestNotification();
        if (latestNotification) {
          latestNotification.launch = true;
          addReminder(latestNotification);
        }

        const result = await (Platform.OS === 'ios' ? AliyunPush.initPush(Helper.getAppConfig().EMAS_IOS_APPKEY, Helper.getAppConfig().EMAS_IOS_APPSECRET) : AliyunPush.initPush());
        let code = result.code;
        if (code !== AliyunPush.kAliyunPushSuccessCode) {
          const errorMsg = result.errorMsg?.toString();
          console.log(`Failed to Init Android AliyunPush, errorMsg: ${errorMsg}`);
        }
        AliyunPush.getDeviceId().then(setDevId);
      } catch (error) {
        console.error(error);
      }

      AliyunPush.createAndroidChannel({
        id: 'price-reminder',
        name: 'Price Reminder',
        importance: 3,
        desc: 'Price Reminder',
        vibration: true,
      });
      AliyunPush.createAndroidChannel({
        id: 'new-post',
        name: 'New Post',
        importance: 3,
        desc: 'New Post',
        vibration: true,
      });

      AliyunPush.addNotificationCallback(addReminder);
      AliyunPush.addNotificationOpenedCallback(addReminder);

      AliyunPush.addMessageCallback(addReminder);
      if (Platform.OS === 'ios') {
        try {
          const fResult = await AliyunPush.showNoticeWhenForeground(true);
          let code = fResult.code;
          if (code !== AliyunPush.kAliyunPushSuccessCode) {
            const errorMsg = fResult.errorMsg?.toString();
            console.log(`设置前台显示通知失败, error: ${errorMsg}`);
          }
        } catch (error) {
          console.error(error);
        }
      }
    })();

    return () => {
      subscription.remove();
    }
  }, [])

  useEffect(() => {
    setTimeout(() => {
      if (devId && Helper.isLoggeIn()) {
        try {
          updateDeviceId(
            devId,
            AppStorage.appStorage.userId,
            (response) => {
              AppStorage.setAccessToken(response.token);
            },
            (error) => {
              console.log("updateDeviceId error", JSON.stringify(error))
            },
            () => {
              console.log("updateDeviceId wrong token")
            }
          )
        } catch (error) {
          console.error(error);
        }
      }
    }, 2000);
  }, [devId, Helper.isLoggeIn()])

  if (Platform.OS === 'android' && !AppStorage.appStorage.notificationPromptShown && AppStorage.appStorage.showNotificationPrompt) {
    return <RNModal
      animationType="fade"
      transparent={true}
      visible={true}
      presentationStyle="overFullScreen"
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0 0 0 / .05)",
        }}
      >
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "white",
            borderRadius: 10,
            padding: 30,
            marginBottom: 20,
            minWidth: 300,
            marginHorizontal: 50,
          }}
        >
          <TextComponent text={translate("priceModal.askNotification")} />
          <View style={{
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "center",
            marginTop: 20,
          }}>
            <TextComponent
              text={translate("priceModal.enableNotification")}
              onPress={() => {
                AppStorage.setNotificationPromptShown(true);
                AppStorage.saveToStorage();
              }}
            />
            <View style={{
              width: 40,
              marginTop: 30,
            }}
            />
            <TextComponent
              text={translate("priceModal.disableNotification")}
              onPress={() => {
                AppStorage.setNotificationPromptShown(true);
                AppStorage.saveToStorage();
                AliyunPush.jumpToAndroidNotificationSettings();
              }}
            />
          </View>
        </View>
      </View>
    </RNModal>
  }

  return (reminders.map((reminder, idx) => {
    return (
      <RNModal
        key={idx}
        animationType="fade"
        transparent={true}
        visible={true}
        presentationStyle="overFullScreen"
      >
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: reminder.blank ? 'white' : "rgba(0 0 0 / .05)",
          }}
        >
          <View
            style={{
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "white",
              borderRadius: 10,
              padding: 15,
              marginBottom: 20,
              minWidth: 300,
              marginHorizontal: 50,
            }}
          >
            <MIcon name="bell" size={Dimen.iconSize.xxl} style={{ marginTop: 20 }} />
            {reminder.price &&
              <Price
                itemType={reminder.price.itemType}
                condition={reminder.price.condition}
                type={reminder.price.type}
                price={reminder.price.price}
              />
            }
            {reminder.text &&
              <Text
                text={reminder.text.text}
              />
            }
            <TextComponent
              style={[TEXT_CENTER, MARGIN_S_DF_TOP, { fontSize: Dimen.fontSize.ssm }]}
              text={translate("priceModal.notificationDate") + ": " + moment().format("YYYY-MM-DD")}
            />
          </View>
          <TouchableOpacity
            onPress={() => {
              setReminders(reminders => {
                reminders.pop();
                return [...reminders];
              });
            }}
            style={{
              padding: 5,
              borderRadius: 100,
              backgroundColor: 'white',
            }}
          >
            <FontAwesomeIcon icon={faTimes} size={20} color="#ccc" />
          </TouchableOpacity>
        </View>
      </RNModal>
    )
  }));
});
