import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BG_GOLD, BG_GRAY, BTN_CONTAINER, DF_CONTENT, FSIZE_12, TEXT_CONTENT, TEXT_SCREEN_TITLE, TEXT_SECTION, TEXT_SUCCESS } from "../../theme/baseStyle"
import { AI_CENTER, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_LEFT, MARGIN_DF_TOP, ROW, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_GREEN, TEXT_WHITE, W_100P } from "../../theme/mStyle"
import { TextInput, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { AppStorage } from "app/utils/appStorage"
import { colors } from "app/theme"
import { ApiService } from "app/api/api"
import { getRegisterDemoEmailCode, getRegisterDemoPhoneCode, registerDemoAccount } from "app/api/model"
import { BackNavComponent } from "app/components/BackNavComponent"
import { Helper } from "app/utils/helper"

const SectionContent: ViewStyle = {
  ...ROW,
  width: Dimen.screenWidth,
  padding: Dimen.padding.base,
  marginTop: Dimen.padding.base,
  alignItems: "center",
  backgroundColor: colors.mine.bgGrey
}

const TextLabel: TextStyle = {
  ...TEXT_CONTENT,
  ...FSIZE_12,
  marginLeft: Dimen.padding.base,
  marginTop: Dimen.padding.base
}

const TextMessage: TextStyle = {
  ...TEXT_CONTENT,
  ...FSIZE_12,
  marginLeft: Dimen.padding.base
}

const TextValue: TextStyle = {
  ...TEXT_CONTENT,
  ...FLEX,
  ...FSIZE_12,
  lineHeight: 16,
}

const TextInputValue: TextStyle = {
  ...TEXT_CONTENT,
  ...ROW,
  ...FSIZE_12,
  lineHeight: 16,
  height: 44,
  width: Dimen.screenWidth - Dimen.padding.base * 2,
  textAlignVertical: "center",
  marginHorizontal: Dimen.padding.base,
  marginTop: Dimen.padding.ssm,
  paddingHorizontal: Dimen.padding.sm,
  borderRadius: Dimen.borderRadiusLarge,
  borderColor: colors.mine.textInputBorder,
  borderWidth: 1,
}

const TextInputContainer: TextStyle = {
  ...ROW,
  ...AI_CENTER,
  width: Dimen.screenWidth - Dimen.padding.base * 2,
  height: 45,
  marginHorizontal: Dimen.padding.base,
  marginTop: Dimen.padding.ssm,
  paddingLeft: 8,
  paddingRight: 6,
  borderRadius: Dimen.borderRadiusLarge,
  borderColor: colors.mine.textInputBorder,
  borderWidth: 1,
}

const TextInputDisabledContainer: TextStyle = {
  ...TextInputContainer,
  backgroundColor: colors.mine.bgGrey
}

const BtnUpload: ViewStyle = {
  width: 100,
  height: "85%",
  backgroundColor: colors.mine.primary,
  borderRadius: Dimen.borderRadiusLarge,
  overflow: "hidden",  
  marginRight: -3,
  justifyContent: "center",
  alignItems: "center",
}

const BtnUploadText: TextStyle = {
  ...FSIZE_12,
  textAlign: "center",
  color: colors.mine.white
}

interface DemoAccountRegistrationScreenProps extends AppStackScreenProps<"DemoAccountRegistration"> {}
export const DemoAccountRegistrationScreen: FC<DemoAccountRegistrationScreenProps> = observer(function DemoAccountRegistrationScreen(_props) {
  
  const [name, setName] = useState("")  
  const [phoneNo, setPhoneNo] = useState("")
  const [phoneVerifyCode, setPhoneVerifyCode] = useState("")
  const [email, setEmail] = useState("")
  const [emailVerifyCode, setEmailVerifyCode] = useState("")

  const [countDownSecondPhone, setCountDownSecondPhone] = useState(61)
  const [resentCodePhoneCount, setResentCodePhoneCount ] = useState(0)
  const [phoneErrorText, setPhoneErrorText] = useState("")
  const timerPhoneRef = useRef(null)
  const countDownSecondPhoneRef = useRef(countDownSecondPhone);

  const [countDownSecondEmail, setCountDownSecondEmail] = useState(31)
  const [resentCodeEmailCount, setResentCodeEmailCount ] = useState(0)
  const [EmailErrorText, setEmailErrorText] = useState("")
  const timerEmailRef = useRef(null)
  const countDownSecondEmailRef = useRef(countDownSecondEmail);
  
  const [areaCode, setAreaCode] = useState("+852")

  const startCountdown = (isPhone: boolean) => {
    if (isPhone) {
      if (timerPhoneRef.current == null) {
        timerPhoneRef.current = setInterval(() => {
          // console.log("countDownSecond: " + countDownSecondRef.current);
          if (countDownSecondPhoneRef.current > 0) {
            setCountDownSecondPhone(countDownSecondPhoneRef.current - 1);
          } else {
            setPhoneErrorText("")
            setCountDownSecondPhone(61);
            clearInterval(timerPhoneRef.current);
            timerPhoneRef.current = null;
            countDownSecondPhoneRef.current = 60;
          }
        }, 1000)
      }
    } else {
      if (timerEmailRef.current == null) {
        timerEmailRef.current = setInterval(() => {
          // console.log("countDownSecond: " + countDownSecondRef.current);
          if (countDownSecondEmailRef.current > 0) {
            setCountDownSecondEmail(countDownSecondEmailRef.current - 1);
          } else {
            setEmailErrorText("")
            setCountDownSecondEmail(31);
            clearInterval(timerEmailRef.current);
            timerEmailRef.current = null;
            countDownSecondEmailRef.current = 30;
          }
        }, 1000)
      }
    }    
  }

  const clearTimer = (isPhone: boolean) => {
    if (isPhone) {
      if (timerPhoneRef.current != null) {
        setPhoneErrorText("")
        clearInterval(timerPhoneRef.current)
        setCountDownSecondPhone(61)
        setResentCodePhoneCount(0)
        timerPhoneRef.current = null
      }
    } else {
      if (timerEmailRef.current != null) {
        setEmailErrorText("")
        clearInterval(timerEmailRef.current)
        setCountDownSecondEmail(61)
        setResentCodeEmailCount(0)
        timerEmailRef.current = null
      }
    }
  }

  const getRequestCodeBtnText = (isPhone: boolean) => {
    if (isPhone) {
      if (countDownSecondPhone > 60) {
        if (resentCodePhoneCount < 1) {
          return translate("demoAccountSignUp.getVerifyCode")
        } else {
          return translate("demoAccountSignUp.resendVerifyCode")
        }
      } else {
        return translate("common.resend") + " (" + countDownSecondPhone + "s)"
      }
    } else {
      if (countDownSecondEmail > 30) {
        if (resentCodeEmailCount < 1) {
          return translate("demoAccountSignUp.getVerifyCode")
        } else {
          return translate("demoAccountSignUp.resendVerifyCode")
        }
      } else {
        return translate("common.resend") + " (" + countDownSecondEmail + "s)"
      }
    }
  }



  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
    if (AppStorage.appStorage.userDevice) {
      setAreaCode(AppStorage.appStorage.userDevice.areaCode)
      setPhoneNo(AppStorage.appStorage.userDevice.mobile)
    }
  }

  const requestPhoneCode = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (phoneNo === "") {
      ApiService.showMessageBox(translate("errors.enterPhone"))
    } else if (countDownSecondPhone < 61) {
      console.log("countDownSecondPhone: " + countDownSecondPhone)
    } else {
      AppStorage.showLoading()
      getRegisterDemoPhoneCode(
        areaCode,
        phoneNo,
        (res) => {
          AppStorage.hideLoading()
          console.log(res)
          // ApiService.showSuccessToast(res.Message)
          setCountDownSecondPhone(60)
          if (resentCodePhoneCount > 0) {
            setPhoneErrorText(translate("boarding.newCodeSent"))
          } else {
            setPhoneErrorText(translate("demoAccountSignUp.verifycodeSent"))
          }
          startCountdown(true)
          setResentCodePhoneCount(resentCodePhoneCount + 1)
        },
        (error) => {
          AppStorage.hideLoading()
          console.log(error)
          ApiService.showMessageBox(error)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  const requestEmailCode = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (email === "") {
      ApiService.showMessageBox(translate("errors.enterEmail"))
    } else if (!Helper.isEmailString(email)) {
      ApiService.showMessageBox(translate("errors.invalidEmail"))
    } else if (countDownSecondEmail < 31) {
      console.log("countDownSecondEmail: " + countDownSecondEmail)
    } else {
      AppStorage.showLoading()
      getRegisterDemoEmailCode(
        email,
        (res) => {
          AppStorage.hideLoading()
          console.log(res)
          setCountDownSecondEmail(30)
          if (resentCodeEmailCount > 0) {
            setEmailErrorText(translate("boarding.newCodeSent"))
          } else {
            setEmailErrorText(translate("demoAccountSignUp.verifycodeSent"))
          }
          startCountdown(false)
          setResentCodeEmailCount(resentCodeEmailCount + 1)
        },
        (error) => {
          AppStorage.hideLoading()
          console.log(error)
          ApiService.showMessageBox(error)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        }
      )
    }
  }

  const checkFormValidate = () => {
    if (name == ""       
      || phoneNo == ""
      || phoneVerifyCode == ""
      || email == ""
      || emailVerifyCode == ""      
    ) {
      ApiService.showMessageBox(translate("errors.inputAllRequiredValues"))
      return false
    } else {
      return true
    }
  }

  const submitRegistrationForm = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (checkFormValidate()) {
      AppStorage.showLoading()
      const formData = {
        id: AppStorage.appStorage.userId,
        client_name: name,        
        phone_code: phoneVerifyCode,
        email,
        email_code: emailVerifyCode        
      }
      registerDemoAccount(
        formData,
        (res, code) => {
          AppStorage.hideLoading()
          console.log(res)
          if (code != null) {
            if (code == 10017) {
              ApiService.showMessageBox(translate("errors.demoRgCode10017"))
            } else if (code == 10018) {
              ApiService.showMessageBox(translate("errors.demoRgCode10018"))
            } else if (code == 200) {
              ApiService.showMessageBoxAndBack(
                translate("errors.demoRgCode200"),
                () => {
                  _props.navigation.navigate("MainStack",
                    {
                      screen: "Home",
                    }
                  )
                }
              )              
            } else {
              ApiService.showMessageBox(res)
            }
          } else {
            if (res.Message) {
              ApiService.showSuccessToast(res.Message)
            } else {
              ApiService.showSuccessToast(translate("common.success"))
            }
            _props.navigation.navigate("MainStack",
              {
                screen: "Home",
              }
            )
          }
        },
        (errorMessage) => {
          AppStorage.hideLoading()
          console.log(errorMessage)
          ApiService.showMessageBox(errorMessage)
        },
        () => {
          AppStorage.hideLoading()
          _props.navigation.navigate("Logout")
        } 
      )
    }
  }

  useEffect(() => {
   countDownSecondPhoneRef.current = countDownSecondPhone
  }, [countDownSecondPhone])

  useEffect(() => {
    countDownSecondEmailRef.current = countDownSecondEmail
  }, [countDownSecondEmail])

  useEffect(() => {
    prepareData()
    return () => {
      clearTimer(true)
      clearTimer(false)
    }
  }, [])

  return (
    <Screen
      preset="auto"
      safeAreaEdges={["top", "bottom"]}
      keyboardOffset={Dimen.keyboardOffset}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        isTitleCenter={false}
        title={translate("demoAccountSignUp.title")} />
      <View style={SectionContent}>
        <Text style={FSIZE_12} text={translate("account.personalInfo")} />
      </View>
      <Text style={TextLabel} text={translate("demoAccountSignUp.name")} />
      <TextInput
        allowFontScaling={false}
        value={name}        
        onChangeText={setName}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("demoAccountSignUp.namePlaceholder")}
        style={TextInputValue} />
        
      <Text style={TextLabel} text={translate("demoAccountSignUp.phoneNo")} />
      <View style={TextInputDisabledContainer}>
        {/* <Text 
          onPress={() => {
            _props.navigation.navigate("SelectCountry", {
              onSelect: (item) => {
                console.log("onSelect", JSON.stringify(item))
                setAreaCode(item.value)
              },
            })          
          }}
          style={TEXT_CONTENT} 
          text={areaCode} /> */}
        <Text
          allowFontScaling={false}
          // editable={false}
          // keyboardType="phone-pad"
          text={"+" + areaCode + " " + phoneNo}
          // onChangeText={setPhoneNo}
          // placeholderTextColor={colors.mine.placeHolderText}
          // placeholder={translate("demoAccountSignUp.phoneNoPlaceholder")}
          style={[TEXT_CONTENT, FSIZE_12, FLEX]} />
      </View>
      <View style={TextInputContainer}>
        <TextInput
          allowFontScaling={false}
          keyboardType="number-pad" 
          value={phoneVerifyCode}
          onChangeText={setPhoneVerifyCode}
          placeholderTextColor={colors.mine.placeHolderText}
          placeholder={translate("demoAccountSignUp.verifyCodePlaceholder")}
          style={TextValue} />
        <TouchableOpacity 
          onPress={() => requestPhoneCode()} 
          style={[BtnUpload, countDownSecondPhone > 60 ? BG_GOLD : BG_GRAY]}>
          <Text style={BtnUploadText} text={getRequestCodeBtnText(true)} />
        </TouchableOpacity>
      </View>
      <Text style={[TextMessage, TEXT_COLOR_GREEN]} text={phoneErrorText} />

      <Text style={TextLabel} text={translate("demoAccountSignUp.email")} />
      <TextInput
        allowFontScaling={false}
        keyboardType="email-address"
        value={email}
        onChangeText={setEmail}
        placeholderTextColor={colors.mine.placeHolderText}
        placeholder={translate("demoAccountSignUp.emailPlaceholder")}
        style={TextInputValue} />
      <View style={TextInputContainer}>
        <TextInput
          allowFontScaling={false}
          keyboardType="number-pad" 
          value={emailVerifyCode}
          onChangeText={setEmailVerifyCode}
          placeholderTextColor={colors.mine.placeHolderText}
          placeholder={translate("demoAccountSignUp.verifyCodePlaceholder")}
          style={TextValue} />
        <TouchableOpacity 
          onPress={() => requestEmailCode()} 
          style={[BtnUpload, countDownSecondEmail > 30 ? BG_GOLD : BG_GRAY]}>
          <Text style={BtnUploadText} text={getRequestCodeBtnText(false)} />
        </TouchableOpacity>
      </View>
      <Text style={[TextMessage, TEXT_COLOR_GREEN]} text={EmailErrorText} />

      <View style={FLEX}></View>        
      <TouchableOpacity 
        onPress={() => submitRegistrationForm()}
        style={[DF_CONTENT, BTN_CONTAINER, MARGIN_DF_TOP, MARGIN_DF_BOTTOM]}>
        <Text style={[TEXT_SECTION ,TEXT_WHITE, TEXT_CENTER, W_100P]} text={translate("common.submit")} />        
      </TouchableOpacity>
      <View style={{height: 40}}></View>
    </Screen>
  )
})