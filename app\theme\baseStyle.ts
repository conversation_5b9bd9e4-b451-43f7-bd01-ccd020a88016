import { Platform, TextStyle, ViewStyle } from "react-native";
import { colors } from "./colors";
import { typography } from "./typography";
import { Dimen } from "./dimen";
import { AI_CENTER, BOLD, FLEX, ROW, SHADOW, W_100P } from "./mStyle";

export const ROOT_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth,
    minHeight: Dimen.screenHeight,
    backgroundColor: colors.mine.mainBg
}

export const CONT_BG_IMG: any = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight,
}

export const CONTAINER_BORDER: ViewStyle = {
    ...SHADOW,
    padding: Dimen.padding.ssm,
    borderRadius: Dimen.borderRadius,
    backgroundColor: colors.palette.white,
}

export const GREY_BORDER: ViewStyle = {
    padding: Dimen.padding.ssm,
    borderRadius: Dimen.borderRadius,
    backgroundColor: colors.mine.gray
}

export const GREY_BORDER_B: ViewStyle = {
    borderBottomColor: colors.mine.border,
    borderBottomWidth: 1,
}

export const TEXT_SECTION: TextStyle = {
    fontSize: Dimen.fontSize.base,
    fontWeight: Platform.OS === 'ios' ? '900' : '600'
}

export const TEXT_VALUE_GREEN: TextStyle = {
    fontSize: Dimen.fontSize.base,
    color: colors.mine.green
}

export const TEXT_VALUE_RED: TextStyle = {
    fontSize: Dimen.fontSize.base,
    color: colors.mine.red
}

export const TEXT_LINK: TextStyle = {
    textDecorationStyle: "solid",
    textDecorationLine: "underline",
    textDecorationColor: colors.mine.text,
}

export const TEXT_SMALL: TextStyle = {
    fontSize: Dimen.fontSize.sm,
    fontWeight: 'normal',
    marginStart: Dimen.padding.ssm,
    color: colors.mine.text,
}

export const TEXT_SMALL_0: TextStyle = {
    fontSize: Dimen.fontSize.sm,
}

export const TEXT_SMALLEST: TextStyle = {
    fontSize: Dimen.fontSize.ssm,
    marginStart: Dimen.padding.ssm,
}

export const TEXT_SCREEN_TITLE: TextStyle = {
    fontSize: Dimen.fontSize.md,
    // fontFamily: typography.primary.semiBold,    
    fontWeight: Platform.OS === 'ios' ? '900' : '600',
}

export const TEXT_HEADER_TITLE: TextStyle = {
    fontSize: Dimen.fontSize.md,
    marginStart: Dimen.padding.ssm,
    fontWeight: Platform.OS === 'ios' ? '900' : '600',
}

export const TEXT_NEWS_TITLE: TextStyle = {
    fontSize: Dimen.fontSize.md,
    fontFamily: Platform.OS === 'ios' ? typography.primary.normal : typography.primary.semiBold,
    fontWeight: Platform.OS === 'ios' ? '900' : '600',    
    marginStart: Dimen.padding.ssm,
}

export const COLOR_WHITE: TextStyle = {
    color: colors.palette.white
}

export const BTN_CONTAINER: TextStyle = {
    ...ROW,
    padding: Dimen.padding.sm,
    borderRadius: Dimen.borderRadiusLarge,
    backgroundColor: colors.mine.primary,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    marginHorizontal: Dimen.padding.base,
    overflow: 'hidden',
}

export const BG_GRAY: ViewStyle = {
    backgroundColor: colors.mine.gray,
}

export const BTN_GRAY_CONTAINER: TextStyle = {
    ...ROW,
    paddingVertical: 2,
    paddingHorizontal: Dimen.padding.sm,
    borderRadius: Dimen.borderRadius,
    backgroundColor: colors.mine.gray,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    overflow: 'hidden',
}

export const BTN_GREEN_CONTAINER: TextStyle = {
    ...ROW,
    padding: Dimen.padding.ssm,
    borderRadius: Dimen.borderRadiusLarge,
    backgroundColor: colors.mine.green,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    alignSelf: "flex-start",
    overflow: 'hidden',
}

export const BTN_RED_CONTAINER: TextStyle = {
    ...ROW,
    padding: Dimen.padding.ssm,
    borderRadius: Dimen.borderRadiusLarge,
    backgroundColor: colors.mine.red,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    alignSelf: "flex-start",
    overflow: 'hidden',
}

export const BTN_IN_CARD: TextStyle = {
    ...ROW,
    borderRadius: 6,
    backgroundColor: colors.mine.primary,
    paddingHorizontal: Dimen.padding.sm,
    paddingVertical: 2,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    textAlignVertical: "center",
    overflow: 'hidden',
}

export const BTN_IN_CARD_GREY: TextStyle = {
    ...ROW,
    borderRadius: 6,
    backgroundColor: colors.mine.gray,
    paddingHorizontal: Dimen.padding.sm,
    paddingVertical: 2,
    color: colors.palette.white,
    fontSize: Dimen.fontSize.sm,
    textAlign: "center",
    textAlignVertical: "center",
    overflow: 'hidden',
}

export const SETTING_ROW: ViewStyle = {
    ...ROW,
    ...AI_CENTER,
    width: "100%",
    marginTop: Dimen.padding.base,
  }

export const TEXT_INPUT: TextStyle = {
    width: '100%',
    marginVertical: Dimen.padding.base,
    fontFamily: typography.primary.medium,
    fontSize: Dimen.fontSize.base,
    color: colors.mine.text,
    padding: Dimen.padding.sm,
    backgroundColor: colors.mine.bgGrey,
    borderRadius: Dimen.borderRadiusLarge
}

export const TEXT_INPUT_FLEX: TextStyle = {
    flex: 1,
    marginVertical: Dimen.padding.base,
    fontFamily: typography.primary.medium,
    fontSize: Dimen.fontSize.base,
    color: colors.mine.text,
    padding: Dimen.padding.sm,
    backgroundColor: colors.mine.bgGrey,
    borderRadius: Dimen.borderRadiusLarge
}

export const CONT_INPUT: ViewStyle = {
    width: '100%',
    marginVertical: Dimen.padding.base,
    padding: Dimen.padding.sm,
    backgroundColor: colors.mine.bgGrey,
    borderRadius: Dimen.borderRadiusLarge
}

export const CONT_INPUT_2: ViewStyle = {
    width: Dimen.screenWidth - 2*Dimen.padding.base,
    margin: Dimen.padding.base,
    padding: Dimen.padding.sm,
    borderWidth: 1,
    borderColor: colors.mine.border,
    borderRadius: 8,
    backgroundColor: colors.palette.white,
}

export const TEXT_CONTENT: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Dimen.fontSize.base,
    lineHeight: Dimen.lineHeight.base,
    color: colors.mine.text,
    padding: 3
}

export const TEXT_CONTENT_MEDIUM: TextStyle = {
    fontFamily: typography.primary.medium,
    fontWeight: Platform.OS === 'ios' ? '700' : '700',
    fontSize: Dimen.fontSize.base,
    lineHeight: Dimen.lineHeight.base,
    color: colors.mine.text,
    padding: Dimen.padding.ssm
}

export const TEXT_SUCCESS: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Dimen.fontSize.base,
    color: colors.mine.primary,
    padding: Dimen.padding.ssm
}


export const TEXT_H1: TextStyle = {
    fontFamily: typography.primary.bold,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    fontSize: Dimen.fontSize.xxxl,    
    color: colors.mine.text
}

export const TEXT_H2: TextStyle = {
    fontFamily: typography.primary.bold,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    fontSize: Dimen.fontSize.xxl,    
    color: colors.mine.text
}

export const TEXT_H4: TextStyle = {
    fontFamily: typography.primary.bold,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    fontSize: Dimen.fontSize.lg,    
    color: colors.mine.text
}

export const TEXT_H4_M: TextStyle = {
    fontFamily: typography.primary.medium,
    fontWeight: Platform.OS === 'ios' ? '600' : 'normal',
    fontSize: Dimen.fontSize.lg,    
    color: colors.mine.text
}

export const TEXT_H4_REGULAR: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Dimen.fontSize.lg,    
    color: colors.mine.text
}

export const TEXT_B1: TextStyle = {
    fontFamily: typography.primary.bold,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    fontSize: Dimen.fontSize.base,
    color: colors.mine.text
}

export const TEXT_B1_REGULAR: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Dimen.fontSize.base,
    color: colors.mine.text
}

export const TEXT_B2: TextStyle = {
    fontFamily: typography.primary.bold,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    fontSize: Dimen.fontSize.sm,
    color: colors.mine.text
}

export const TEXT_B2_REGULAR: TextStyle = {
    fontFamily: typography.primary.normal,    
    fontSize: Dimen.fontSize.sm,
    color: colors.mine.text
}

export const TEXT_C1: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Platform.OS === "ios" ? 11 : 10,
    color: colors.mine.text
}

export const TEXT_UPLOAD_FILE_TITLE: TextStyle = {
    fontSize: Platform.OS === "ios" ? 17 : 16,
    fontFamily: typography.primary.normal,
    fontWeight: Platform.OS === 'ios' ? '900' : 'normal',
    color: colors.palette.black,
    textAlign: "center",
}

export const TEXT_UPLOAD_FILE_MESSAGE: TextStyle = {
    fontFamily: typography.primary.normal,
    fontSize: Dimen.fontSize.base,    
    color: colors.palette.black,
    textAlign: "center",
}

export const TEXT_UPLOAD_FILE_OPT: TextStyle = {
    width: '100%',
    fontFamily: typography.primary.normal,
    fontSize: Platform.OS === "ios" ? 17 : 16,
    color: "#007AFF",
    textAlign: "center",
    paddingVertical: Dimen.padding.sm,
    borderTopColor: "rgba(60, 60, 67, 0.36)",
    borderTopWidth: 1,
}

export const TEXT_APPOINTMENT_PLACE: TextStyle = {
    width: 70,
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    textAlign: 'left',
    fontFamily: typography.primary.normal,
    fontWeight: Platform.OS === 'ios' ? '500' : 'normal',
    fontSize: Platform.OS === "ios" ? 11 : 10,
    color: "#2F80ED",
    marginHorizontal: Dimen.padding.ssm,
    paddingVertical: 2,
    paddingHorizontal: 4,
    backgroundColor: "rgba(47, 128, 237, 0.1)",
    borderRadius: 4,
}

export const TEXT_CHAT_BUBBLE: TextStyle = {
    width: 20,
    height: 20,
    borderRadius: 10,
    fontFamily: typography.primary.normal,
    fontSize: Platform.OS === "ios" ? 11 : 10,
    textAlign: "center",
    marginHorizontal: Dimen.padding.ssm,
    padding: 2,
    color: colors.palette.white,
    backgroundColor: colors.mine.chatBubbleBg,

}

export const TOP_HEADER: ViewStyle = {
    ...ROW,
    ...AI_CENTER,
    ...W_100P,
    paddingHorizontal: Dimen.padding.base,
    paddingVertical: Dimen.padding.sm,
}

export const DF_CONTENT: ViewStyle = {
    width: Dimen.screenWidth - 2*Dimen.padding.base,
    marginHorizontal: Dimen.padding.base,
}

export const SNACKBAR_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth - 2*Dimen.padding.base,
    marginHorizontal: Dimen.padding.base,
    position: "absolute",    
    top: 50,
    left: 0,
    // minHeight: 100,
    backgroundColor: "#1E293B",
    borderRadius: 8,
    padding: Dimen.padding.base,
    zIndex: 9999999,
    flexDirection: "row",
    alignItems: "center",
}

export const MODAL_BACKDROP: ViewStyle = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight + 20,
    position: "absolute",
    top: 0,
    left: 0,
    zIndex: 999999,
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.mine.modalBg,
}

export const CONFIRM_BOX_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth - 2.5*Dimen.padding.base,
    backgroundColor: colors.palette.white,    
    borderRadius: 8,
    paddingVertical: Dimen.padding.xl,
    flexDirection: "column",
    alignItems: "center",
    alignSelf: "center",
}

export const MODAL_BOX_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth - 2.5*Dimen.padding.base,
    maxHeight: 0.7*Dimen.screenHeight,    
    backgroundColor: colors.palette.white,
    borderWidth: 3,
    borderColor: colors.mine.black,
    padding: Dimen.padding.ssm,
    flexDirection: "column",
    alignItems: "center",
    alignSelf: "center",
}

export const UPLOAD_BOX_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth*0.7,
    backgroundColor: colors.mine.modalUploadFileBg,
    borderRadius: 14,
    paddingVertical: Dimen.padding.md,
    flexDirection: "column",
    alignItems: "center",
    alignSelf: "center",
}

export const PDF_MODAL_BACKDROP: ViewStyle = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight + 50,
    backgroundColor: colors.mine.modalBg,
    position: "absolute",
    top: 0,
    left: 0,
    zIndex: 999999,
    flexDirection: "column",
    alignItems: "center"

}

export const PDF_BOX_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight*0.92,
    marginTop: Dimen.screenHeight*0,
    backgroundColor: colors.mine.modalUploadFileBg,
    // borderRadius: 14,
    paddingVertical: Dimen.padding.md,
    flexDirection: "column",
    alignItems: "center",
    alignSelf: "center",
}

export const HIDE_PRELOAD_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight,
    position: "absolute",
    top: 0,
    left: 0,
    backgroundColor: colors.mine.white,
}

export const HIDE_PRELOAD_HAS_M_TOP_CONTAINER: ViewStyle = {
    width: Dimen.screenWidth,
    height: Dimen.screenHeight,
    position: "absolute",
    top: Dimen.padding.xl,
    left: 0,
    backgroundColor: colors.mine.white,
}

export const VISIBLE: ViewStyle = {
    display: "flex"
}

export const INVISIBLE: ViewStyle = {
    display: "none"
}

export const LIST_APPOINTMENT_ROW: ViewStyle = {
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
    paddingVertical: 10,
    borderBottomColor: colors.mine.border,
    borderBottomWidth: 1
}

export const LIST_APPOINTMENT_COL1: ViewStyle = {
    width: "35%",
    flexDirection: "column",
}

export const LIST_APPOINTMENT_COL2: ViewStyle = {
    width: "65%",
    flexDirection: "row",
    alignItems: "center",
}

export const LIST_APPOINTMENT_COL1_NARROW: ViewStyle = {
    width: "25%",
    flexDirection: "column",
}

export const LIST_APPOINTMENT_COL2_FLEX: ViewStyle = {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
}

export const CIRCLE_PRIMARY: ViewStyle = {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.mine.primary,
    justifyContent: "center",
    alignItems: "center",
}

export const ROW_INFO: ViewStyle = {
    width: Dimen.screenWidth - 2*Dimen.padding.base,
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Dimen.padding.sm,
    borderBottomColor: colors.mine.border,
    borderBottomWidth: 1,
  }

export const  FSIZE_12: TextStyle = {
    fontSize: Dimen.fontSize.ssm
}

export const  FSIZE_12_L: TextStyle = {
    fontSize: Dimen.fontSize.ssm,
    fontFamily: typography.primary.light,
    fontWeight: Platform.OS == "ios" ? "300" : "normal"
}

export const  FSIZE_13: TextStyle = {
    fontSize: Platform.OS === "ios" ? 13 : 12
}

export const  FSIZE_15: TextStyle = {
    fontSize: Platform.OS === "ios" ? 15 : 14
}

export const  FSIZE_17: TextStyle = {
    fontSize: Platform.OS === "ios" ? 17 : 16
}

export const  FSIZE_18: TextStyle = {
    fontSize: Dimen.fontSize.md
}

export const  FSIZE_22: TextStyle = {
    fontSize: Platform.OS === "ios" ? 22 : 21
}

export const  FSIZE_24: TextStyle = {
    fontSize: Dimen.fontSize.xl,
    lineHeight: 30
}

export const COLOR_8B: TextStyle = {
    color: "#8B8B8B"
}

export const COLOR_84: TextStyle = {
    color: "#848484"
}

export const COLOR_88: TextStyle = {
    color: "#888888"
}

export const BG_GOLD: ViewStyle = {
    backgroundColor: colors.mine.primary
}