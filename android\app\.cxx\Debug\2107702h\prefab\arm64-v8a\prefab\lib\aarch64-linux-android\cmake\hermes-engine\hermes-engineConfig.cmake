if(NOT TARGET hermes-engine::libhermes)
add_library(hermes-engine::libhermes SHARED IMPORTED)
set_target_properties(hermes-engine::libhermes PROPERTIES
    IMPORTED_LOCATION "/Users/<USER>/.gradle/caches/transforms-3/660f20f7bcd52a7fb8a54861af5482df/transformed/jetified-hermes-android-0.71.7-debug/prefab/modules/libhermes/libs/android.arm64-v8a/libhermes.so"
    INTERFACE_INCLUDE_DIRECTORIES "/Users/<USER>/.gradle/caches/transforms-3/660f20f7bcd52a7fb8a54861af5482df/transformed/jetified-hermes-android-0.71.7-debug/prefab/modules/libhermes/include"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

