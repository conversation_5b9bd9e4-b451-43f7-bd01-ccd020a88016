0000000000000000000000000000000000000000 b9a25b8471afeae292edd18e16918898e41928ad <PERSON> <<EMAIL>> 1740314975 +0800	initial pull
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad <PERSON> <<EMAIL>> 1740315131 +0800	checkout: moving from master to bugfix
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad <PERSON> <<EMAIL>> 1740315163 +0800	reset: moving to head
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740315170 +0800	checkout: moving from bugfix to master
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad <PERSON> <<EMAIL>> 1740315839 +0800	checkout: moving from master to bugfix
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740315928 +0800	checkout: moving from bugfix to master
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740315972 +0800	checkout: moving from master to bugfix
b9a25b8471afeae292edd18e16918898e41928ad 388e9cd302cdd781f2355727849a84d1f0445e75 Carson <<EMAIL>> 1740316264 +0800	commit (merge): merged conflict
388e9cd302cdd781f2355727849a84d1f0445e75 b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740316365 +0800	checkout: moving from bugfix to master
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740316384 +0800	checkout: moving from master to bugfix
b9a25b8471afeae292edd18e16918898e41928ad d84e12a7826910d9628d9dd67291238596be2237 Carson <<EMAIL>> 1740316433 +0800	pull --rebase origin bugfix (start): checkout d84e12a7826910d9628d9dd67291238596be2237
d84e12a7826910d9628d9dd67291238596be2237 71e00d0bb2f81dc028c5193630e1dca4b91af23f Carson <<EMAIL>> 1740316611 +0800	commit: fixed conflict
71e00d0bb2f81dc028c5193630e1dca4b91af23f d84e12a7826910d9628d9dd67291238596be2237 Carson <<EMAIL>> 1740316680 +0800	reset: moving to head~1
d84e12a7826910d9628d9dd67291238596be2237 b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740316740 +0800	rebase (abort): returning to refs/heads/bugfix
b9a25b8471afeae292edd18e16918898e41928ad d84e12a7826910d9628d9dd67291238596be2237 Carson <<EMAIL>> 1740316764 +0800	pull --rebase origin bugfix (start): checkout d84e12a7826910d9628d9dd67291238596be2237
d84e12a7826910d9628d9dd67291238596be2237 dbf157c74cec7c20760300a21dd5a48e010fda15 Carson <<EMAIL>> 1740316895 +0800	commit: complete merge
dbf157c74cec7c20760300a21dd5a48e010fda15 d84e12a7826910d9628d9dd67291238596be2237 Carson <<EMAIL>> 1740316960 +0800	reset: moving to d84e12a78
d84e12a7826910d9628d9dd67291238596be2237 a964a285b4cbe36fadb19f599a6cdd66ac306111 Carson <<EMAIL>> 1740316980 +0800	commit (amend): 英文版，早中晚讯标签显示为中文简体
a964a285b4cbe36fadb19f599a6cdd66ac306111 a964a285b4cbe36fadb19f599a6cdd66ac306111 Carson <<EMAIL>> 1740317014 +0800	rebase (finish): returning to refs/heads/bugfix
a964a285b4cbe36fadb19f599a6cdd66ac306111 b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740317033 +0800	checkout: moving from bugfix to master
b9a25b8471afeae292edd18e16918898e41928ad b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1740317064 +0800	reset: moving to head
b9a25b8471afeae292edd18e16918898e41928ad a964a285b4cbe36fadb19f599a6cdd66ac306111 Carson <<EMAIL>> 1740317075 +0800	checkout: moving from master to bugfix
a964a285b4cbe36fadb19f599a6cdd66ac306111 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1748748583 +0800	pull origin bugfix: Merge made by the 'ort' strategy.
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1748963554 +0800	reset: moving to HEAD
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 b9a25b8471afeae292edd18e16918898e41928ad Carson <<EMAIL>> 1748963559 +0800	checkout: moving from bugfix to master
b9a25b8471afeae292edd18e16918898e41928ad f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1748963687 +0800	checkout: moving from master to bugfix
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1748967575 +0800	reset: moving to HEAD
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1751465137 +0800	pull origin bugfix: updating HEAD
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1751465199 +0800	pull origin bugfix: updating HEAD
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 f27d6a0e566e0d950270cd9178adfabfaf67e2b4 Carson <<EMAIL>> 1751465221 +0800	reset: moving to HEAD
f27d6a0e566e0d950270cd9178adfabfaf67e2b4 e00e46cfea87607529ac01b7a92378e2acf7f7f3 Carson <<EMAIL>> 1751465229 +0800	pull origin bugfix: Merge made by the 'ort' strategy.
e00e46cfea87607529ac01b7a92378e2acf7f7f3 e00e46cfea87607529ac01b7a92378e2acf7f7f3 Carson <<EMAIL>> 1753338758 +0800	checkout: moving from bugfix to phase_2
e00e46cfea87607529ac01b7a92378e2acf7f7f3 c90596fef2ca52037e22ff458abaf9bb5e91e1cc Carson <<EMAIL>> 1753339226 +0800	commit: init commit update
