
import { observer } from "mobx-react-lite"
import React, { FC, useEffect } from "react"

import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "app/components/MIcon"
import { BTN_CONTAINER, TEXT_CONTENT, TEXT_LINK, TEXT_SCREEN_TITLE, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "app/theme/baseStyle"
import { FLEX, FLEX_2, MARGIN_DF_BOTTOM, MARGIN_DF_TOP, TEXT_CENTER, TEXT_GRAY, TEXT_WHITE, W_100P } from "app/theme/mStyle"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { Dimen } from "app/theme/dimen"
import { AppStorage } from "app/utils/appStorage"
import { storedUserList } from "app/api/userlist"

interface LogoutScreenProps extends AppStackScreenProps<"Logout"> {}
export const LogoutScreen: FC<LogoutScreenProps> = observer(function LogoutScreen(_props) {

  useEffect(() => {
    console.log("LogoutScreen")
    AppStorage.hideLoading()
  },[])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ padding: Dimen.padding.base, flex: 1, alignItems: "center"}}
      safeAreaEdges={["top", "bottom"]}
    >
      <View style={FLEX}></View>
      <Text style={[TEXT_SCREEN_TITLE, TEXT_CENTER, W_100P, MARGIN_DF_BOTTOM]} tx="accountLogout.message" />
      <MIcon name="tokenExpire" size={Dimen.screenWidth*0.7} />
      <View style={FLEX_2}></View>
      <TouchableOpacity 
        style={[BTN_CONTAINER, MARGIN_DF_TOP]} 
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          AppStorage.switchUserById(storedUserList[0].id, () => {
            AppStorage.reset(() => {
              _props.navigation.goBack()
              _props.navigation.reset({
                index: 0,
                routes: [{ name: "BottomTab" }],              
              })              
            })
          })
        }}>
        <Text style={[TEXT_SECTION, TEXT_WHITE, TEXT_CENTER, W_100P]} tx="common.confirm" />
      </TouchableOpacity>
    </Screen>
  )
})