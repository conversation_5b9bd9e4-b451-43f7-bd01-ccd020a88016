import * as React from "react"
import { ImageStyle, StyleProp, TouchableOpacity, ViewStyle, View } from "react-native"
import tabHomeActive from "../../assets/icons/tab-icon-home-active.svg"
import tabHomeInactive from "../../assets/icons/tab-icon-home-inactive.svg"
import tabInboxActive from "../../assets/icons/tab-icon-inbox-active.svg"
import tabInboxInactive from "../../assets/icons/tab-icon-inbox-inactive.svg"
import tabNewsActive from "../../assets/icons/tab-icon-news-active.svg"
import tabNewsInactive from "../../assets/icons/tab-icon-news-inactive.svg"
import tabProfileActive from "../../assets/icons/tab-icon-profile-active.svg"
import tabProfileInactive from "../../assets/icons/tab-icon-profile-inactive.svg"
import tabTrade from "../../assets/icons/tab-icon-trade.svg"
import search from "../../assets/icons/icon-search.svg"
import notification from "../../assets/icons/icon-notification.svg"
import quickNews from "../../assets/icons/icon-quick-news.svg"
import calendar from "../../assets/icons/icon-calendar.svg"
import information from "../../assets/icons/icon-information.svg"
import plus from "../../assets/icons/icon-plus.svg"
import back from "../../assets/icons/icon-back.svg"
import next from "../../assets/icons/icon-next.svg"
import closeBlack from "../../assets/icons/icon-close-black.svg"
import closeWhite from "../../assets/icons/icon-close-white.svg"
import closeSolid from "../../assets/icons/icon-close-solid.svg"
import down from "../../assets/icons/icon-arrow-down.svg"
import downWhite from "../../assets/icons/icon-arrow-down_white.svg"
import up from "../../assets/icons/icon-arrow-up.svg"
import upWhite from "../../assets/icons/icon-arrow-up_white.svg"
import right from "../../assets/icons/icon-arrow-right.svg"
import deposit from "../../assets/icons/icon-cash-deposit.svg"
import wthdrawal from "../../assets/icons/icon-cash-withdrawal.svg"  
import displayRed from "../../assets/icons/icon-red-up-green-down.svg"
import displayGreen from "../../assets/icons/icon-green-up-red-down.svg"
import checkGreen from "../../assets/icons/icon-check-green.svg"
import flagChina from "../../assets/icons/flag-china.svg"
import flagHongKong from "../../assets/icons/flag-hong-kong.svg"
import flagUsa from "../../assets/icons/flag-usa.svg"
import flagEuro from "../../assets/icons/euro-flag.svg"
import tokenExpire from "../../assets/icons/token-expire.svg"
import bell from "../../assets/icons/icon-bell.svg"
import eyeOn from "../../assets/icons/icon-eye-on.svg"
import eyeOff from "../../assets/icons/icon-eye-off.svg"
import checkBoxOn from "../../assets/icons/checkbox-on.svg"
import checkBoxOff from "../../assets/icons/checkbox-off.svg"
import { Dimen } from "../theme/dimen"
import { SvgXml } from "react-native-svg" // Add missing import statement

interface MIconProps {
  isSvg?: boolean
  name: string,
  size?: number,
  style?: StyleProp<ImageStyle>,
  containerStyle?: StyleProp<ViewStyle>,
  onPress?: () => void
}

const ROOT: ImageStyle = {
    resizeMode: "stretch"
  }

export function MIcon (props: MIconProps) {
    const {
        name, 
        size = Dimen.iconSize.lg,
        style: styleOverride, 
        containerStyle = {},
        onPress,
    } = props
    if (onPress) {
      return (
        <TouchableOpacity onPress={onPress} style={containerStyle}>
          <SvgXml
            style={[ROOT, styleOverride]} 
            width={size} 
            height={size} 
            xml={svgIcons[name]} />
        </TouchableOpacity>
      )
    } else {
      return (
        <View style={containerStyle}>
          <SvgXml
            style={[ROOT, styleOverride]} 
            width={size} 
            height={size} 
            xml={svgIcons[name]} />
        </View>
      )
    }
}

const svgIcons = {
  tabHomeActive, tabHomeInactive, 
  tabInboxActive, tabInboxInactive, 
  tabNewsActive, tabNewsInactive, 
  tabProfileActive, tabProfileInactive, 
  tabTrade,
  search, notification, quickNews, calendar, information, plus, back, next,
  closeBlack, closeSolid, closeWhite, down, downWhite, up, upWhite, right, bell, eyeOn, eyeOff,
  deposit, wthdrawal, displayRed, displayGreen, checkGreen,
  flagChina,  flagHongKong, flagUsa, flagEuro,
  tokenExpire, checkBoxOn, checkBoxOff
}
