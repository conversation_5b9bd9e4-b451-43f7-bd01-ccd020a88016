import { observer } from "mobx-react-lite"
import React, { FC, useEffect } from "react"
import { Screen } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { Image, NativeModules, Platform, DevSettings } from "react-native"
import { Dimen } from "app/theme/dimen"
import { AppStorage } from "app/utils/appStorage"
import { getAppDownloadLink, getAppVersion, createDomainFailedLog } from "app/api/model"
import DeviceInfo from 'react-native-device-info';
import { ApiService } from "../../api/api"
import { Helper } from "app/utils/helper"
import { translate } from "app/i18n"
import { AppConfig, SECURE_DOMAINS, SERVICE_DOMAINS } from "app/config/appConfig"
import { changeIcon } from "rn-dynamic-app-icon"
import moment from "moment"
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';
import { getGraphLinkSinoSound } from 'app/api/model'
import { Api } from 'app/api/api'

interface SplashScreenProps extends AppStackScreenProps<"Splash"> { }
export const SplashScreen: FC<SplashScreenProps> = observer(function SplashScreen(_props) {

  const checkAppVersion = async () => {
    AppStorage.showLoading()

    getAppVersion(
      (res) => {
        // Put graph link function here to ensure after the app check api call succeeds, the valid domain parameter could be parsed to graph link api.
        console.log('updating graph link...')
        Helper.updateGraphLink()
        AppStorage.hideLoading()
        const newAppVersion = res.value
        const currentVersion = DeviceInfo.getVersion()
        console.log("newAppVersion", newAppVersion)
        console.log("currentVersion", currentVersion)
        if (parseFloat(currentVersion) < parseFloat(newAppVersion)) {
          // alert("Please update to the latest version")
          showDownloadapp()
        } else {
          goNext()
        }
      },
      (err) => {
        AppStorage.hideLoading()
        console.log("getAppVersion error:", err)
        
        // Check if error is due to network issues
        if (typeof err === 'string' && err.includes(translate("errors.noInternet"))) {
          // Don't proceed to next screen if there's no internet
          // The retry dialog will be handled by the API service
          console.log("Network error detected, not proceeding to next screen")
          return;
        }
        
        // For other errors, proceed to next screen
        goNext()
      }
    )
  }

  const { DeviceIconModule } = NativeModules;
  const setUpdateFirstParams = async () => {
    if (Platform.OS === 'android') {
      try {
        if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId && !AppStorage.isIconChanged()) {
          AppStorage.setIconChanged(true)
          const deviceName = await DeviceIconModule.changeAppIcon("uat");
          console.log("DeviceIconModule: " + deviceName);
          DevSettings.reload();
        } else {
          if (DeviceInfo.getBundleId() != AppConfig.uat.bundleId) {
            AppStorage.setIconChanged(true)
          }
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      console.log("changeIcon ios " + DeviceInfo.getBundleId())
      console.log("changeIcon ios " + (DeviceInfo.getBundleId() == AppConfig.uat.bundleId))
      console.log("changeIcon ios " + !AppStorage.isIconChanged())
      if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId && !AppStorage.isIconChanged()) {
        AppStorage.setIconChanged(true)
        await changeIcon("uat")
        // const UIApplication = require('react-native').NativeModules.UIApplication;
        // try {
        //   await UIApplication.setAlternateIconName('uat');
        //   console.log(`Icon changed to ${'uat'}`);
        // } catch (error) {
        //   console.error('Error changing icon: ', error);
        // }
      } else {
        if (DeviceInfo.getBundleId() != AppConfig.uat.bundleId) {
          AppStorage.setIconChanged(true)
        }
      }
      // Helper.updateMomentLocale(AppStorage.appStorage.setting.language)
    }

    // let resultStr = 0
    // if (DeviceInfo.getBundleId() == AppConfig.prod.bundleId) {
    //   console.log("changeIcon prod")
    //   resultStr = await changeIcon("prod")
    // } else if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId) {
    //   console.log("changeIcon uat")
    //   resultStr = await changeIcon("uat")
    // }    
    // console.log("changeIcon resultStr: ", resultStr)
  }


  const showDownloadapp = () => {
    AppStorage.showLoading()
    getAppDownloadLink(
      (res) => {
        AppStorage.hideLoading()
        const downloadLink = res.value
        console.log("downloadLink", downloadLink)
        ApiService.showMessageBoxAndBack(
          translate("homePage.updateApp"),
          () => {
            Helper.openLink(downloadLink)
          }
        )
      },
      (err) => {
        AppStorage.hideLoading()
        console.log("getAppDownloadLink error:", err)
        
        // Check if error is due to network issues
        if (typeof err === 'string' && err.includes(translate("errors.noInternet"))) {
          // Don't proceed to next screen if there's no internet
          // The retry dialog will be handled by the API service
          console.log("Network error detected, not proceeding to next screen")
          return;
        }
        
        // For other errors, proceed to next screen
        goNext()
      }
    )
  }

  const goNext = () => {
    setTimeout(() => {
      _props.navigation.reset(
        {
          index: 0,
          routes: [{ name: "BottomTab" }],
        }
      )
    }, 1000)
  }

  const checkSecureDomains = async (): Promise<boolean> => {
    try {
      // Ensure AppStorage is ready
      if (!AppStorage || typeof AppStorage.getSecureDomains !== 'function') {
        console.log('AppStorage not ready - using config domains');
        return false;
      }

      // First check if we have valid stored domains
      let secureDomains = AppStorage.getSecureDomains() || [];
      let serviceDomains = AppStorage.getServiceDomains() || [];
      let source = 'Storage';

      // Fall back to config domains if none in storage
      if (secureDomains.length === 0) {
        secureDomains = [...SECURE_DOMAINS]; // Copy config domains
        serviceDomains = [...SERVICE_DOMAINS]; // Copy config service domains
        source = 'Config';

        if (!secureDomains || !Array.isArray(secureDomains)) {
          console.error('No valid secure domains to verify');
          return false;
        }
      }

      console.log(`Verifying domains from ${source}`);
      console.log('Secure domains:', secureDomains.join(', '));
      console.log('Service domains:', serviceDomains.join(', '));


      for (const domain of secureDomains) {
        try {
          const url = `https://${domain}`;

          const response = await fetch(url, {
            signal: ApiService.loadCustomeAbortSignal(2000)
          });

          // clearTimeout(timeoutId);
          if (response.ok) {
            const text = await response.text();
            // Parse response to get secureDomain and serviceDomains
            const lines = text.split('\n').filter(line => line.trim() !== '');

            // Find the [serviceDomain] marker index
            const serviceDomainIndex = lines.findIndex(line => line.trim() === '[serviceDomain]');

            if (serviceDomainIndex === -1) {
              console.error('Invalid domain response format - missing [serviceDomain] marker');
              continue;
            }

            // Parse secure domains (all lines after [secureDomain] until [serviceDomain])
            const newSecureDomains = lines
              .slice(1, serviceDomainIndex) // Skip first line ([secureDomain])
              .map(d => d.trim())
              .filter(d => d); // Remove empty lines

            // Parse service domains (all lines after [serviceDomain])
            const newServiceDomains = lines
              .slice(serviceDomainIndex + 1) // Skip [serviceDomain] line
              .map(d => d.trim())
              .filter(d => d); // Remove empty lines

            // Update storage
            AppStorage.setSecureDomains(newSecureDomains);
            AppStorage.setServiceDomains(newServiceDomains);

            console.log('Domain verification successful:');
            console.log(`Active secure domain: ${domain}`);
            console.log('New secure domains:', newSecureDomains.join(', '));
            console.log('New service domains:', newServiceDomains.join(', '));

            return true;
          }
        } catch (error) {
          console.log(`Domain ${domain} check failed`, error);
        }

        console.log('creating Domain Failed Log for secured domain: ', 'https://' + domain);
        createDomainFailedLog('https://' + domain,
          (response) => {
            console.log('Domain Failed Log response message: ', response.Message);
          },
          (error) => {
            console.log('Domain Failed Log error: ', error);
          }
        )
      }

      console.error('All domain checks failed');
      AppStorage.setAwaitingUserRetry(true);
      AppStorage.showMessage(translate("errors.noInternet"));
      return false;
    } catch (error) {
      console.error('Error in checkSecureDomains:', error);
      return false;
    }
  };

  const initializeApp = async () => {
    console.log('[SplashScreen] Initializing...');

    AppStorage.setAwaitingUserRetry(false);
    
    // Initialize moment locale
    moment.updateLocale('zh-hk', {
      months: Helper.getMonthNames()
    });

    // Initialize UI state
    AppStorage.hidePopup();
    AppStorage.resetPopupShownCount();
    AppStorage.hideLoading();
    AppStorage.hideMessage();

    // Set app icon params
    setUpdateFirstParams();

    // Main initialization sequence
    console.log('[SplashScreen] Starting domain verification...');
    try {
      // First ensure domain verification is fully complete
      const domainVerified = await checkSecureDomains();
      if (!domainVerified) {
        throw new Error('Domain verification failed');
      }

      console.log('[SplashScreen] Domain verification complete');

      console.log('checking app version...')
      checkAppVersion();

    } catch (error) {
      console.error('[SplashScreen] Initialization error:', error);
      // Proceed with version check even if domain check fails
      checkAppVersion();
    }
  };

  useEffect(() => {
    initializeApp();
  }, [])

  return (
    <Screen
      preset="fixed"
    // safeAreaEdges={["top", "bottom"]}
    >
      <Image
        resizeMode="cover"
        source={require("../../../assets/images/bg-splash.png")}
        style={{ width: Dimen.screenWidth, height: Dimen.screenHeight + 40 }} />
    </Screen>
  )
})