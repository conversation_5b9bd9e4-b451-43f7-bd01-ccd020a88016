import { observer } from "mobx-react-lite"
import React, { FC, useEffect } from "react"
import { Screen } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { Image, NativeModules, Platform, DevSettings } from "react-native"
import { Dimen } from "app/theme/dimen"
import { AppStorage } from "app/utils/appStorage"
import { getAppDownloadLink, getAppVersion, createDomainFailedLog } from "app/api/model"
import DeviceInfo from 'react-native-device-info';
import { ApiService } from "../../api/api"
import { Helper } from "app/utils/helper"
import { translate } from "app/i18n"
import { AppConfig, SECURE_DOMAINS } from "app/config/appConfig"
import { changeIcon } from "rn-dynamic-app-icon"
import moment from "moment"
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';

interface SplashScreenProps extends AppStackScreenProps<"Splash"> { }
export const SplashScreen: FC<SplashScreenProps> = observer(function SplashScreen(_props) {

  const checkAppVersion = async () => {
    AppStorage.showLoading()

    getAppVersion(
      (res) => {
        // Put graph link function here to ensure after the app check api call succeeds, the valid domain parameter could be parsed to graph link api.
        console.log('updating graph link...')
        Helper.updateGraphLink()
        AppStorage.hideLoading()
        const newAppVersion = res.value
        const currentVersion = DeviceInfo.getVersion()
        console.log("newAppVersion", newAppVersion)
        console.log("currentVersion", currentVersion)
        if (parseFloat(currentVersion) < parseFloat(newAppVersion)) {
          // alert("Please update to the latest version")
          showDownloadapp()
        } else {
          goNext()
        }
      },
      (err) => {
        AppStorage.hideLoading()
        console.log("getAppVersion error:", err)

        // Check if error is due to network issues
        if (typeof err === 'string' && err.includes(translate("errors.noInternet"))) {
          // Don't proceed to next screen if there's no internet
          // The retry dialog will be handled by the API service
          console.log("Network error detected, not proceeding to next screen")
          return;
        }

        // For other errors, proceed to next screen
        goNext()
      }
    )
  }

  const { DeviceIconModule } = NativeModules;
  const setUpdateFirstParams = async () => {
    if (Platform.OS === 'android') {
      try {
        if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId && !AppStorage.isIconChanged()) {
          AppStorage.setIconChanged(true)
          const deviceName = await DeviceIconModule.changeAppIcon("uat");
          console.log("DeviceIconModule: " + deviceName);
          DevSettings.reload();
        } else {
          if (DeviceInfo.getBundleId() != AppConfig.uat.bundleId) {
            AppStorage.setIconChanged(true)
          }
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      console.log("changeIcon ios " + DeviceInfo.getBundleId())
      console.log("changeIcon ios " + (DeviceInfo.getBundleId() == AppConfig.uat.bundleId))
      console.log("changeIcon ios " + !AppStorage.isIconChanged())
      if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId && !AppStorage.isIconChanged()) {
        AppStorage.setIconChanged(true)
        await changeIcon("uat")
        // const UIApplication = require('react-native').NativeModules.UIApplication;
        // try {
        //   await UIApplication.setAlternateIconName('uat');
        //   console.log(`Icon changed to ${'uat'}`);
        // } catch (error) {
        //   console.error('Error changing icon: ', error);
        // }
      } else {
        if (DeviceInfo.getBundleId() != AppConfig.uat.bundleId) {
          AppStorage.setIconChanged(true)
        }
      }
      // Helper.updateMomentLocale(AppStorage.appStorage.setting.language)
    }

    // let resultStr = 0
    // if (DeviceInfo.getBundleId() == AppConfig.prod.bundleId) {
    //   console.log("changeIcon prod")
    //   resultStr = await changeIcon("prod")
    // } else if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId) {
    //   console.log("changeIcon uat")
    //   resultStr = await changeIcon("uat")
    // }    
    // console.log("changeIcon resultStr: ", resultStr)
  }


  const showDownloadapp = () => {
    AppStorage.showLoading()
    getAppDownloadLink(
      (res) => {
        AppStorage.hideLoading()
        const downloadLink = res.value
        console.log("downloadLink", downloadLink)
        ApiService.showMessageBoxAndBack(
          translate("homePage.updateApp"),
          () => {
            Helper.openLink(downloadLink)
          }
        )
      },
      (err) => {
        AppStorage.hideLoading()
        console.log("getAppDownloadLink error:", err)

        // Check if error is due to network issues
        if (typeof err === 'string' && err.includes(translate("errors.noInternet"))) {
          // Don't proceed to next screen if there's no internet
          // The retry dialog will be handled by the API service
          console.log("Network error detected, not proceeding to next screen")
          return;
        }

        // For other errors, proceed to next screen
        goNext()
      }
    )
  }

  const goNext = () => {
    setTimeout(() => {
      _props.navigation.reset(
        {
          index: 0,
          routes: [{ name: "BottomTab" }],
        }
      )
    }, 1000)
  }

  let failedDomains: string[] | undefined = [];

  const checkDomains = async (): Promise<boolean> => {
    try {

      let secureDomains = AppStorage.getSecureDomains() || [];

      console.log('getting secureDomains from AppStorage:', secureDomains);

      if (!secureDomains || secureDomains.length === 0) {
        secureDomains = SECURE_DOMAINS; // Fallback to default
        console.log('Falling back to default secure domains');
      }

      console.log('Secure domains in storage:', secureDomains.join(', '));
      for (const domain of secureDomains) {
        try {
          const url = `https://${domain}`;
          const response = await fetch(url, {
            signal: ApiService.loadCustomAbortSignal(2000),
            headers: {
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          if (response.ok) {
            console.log(`Secure Domain ${domain} is accessible`);
            let text = await response.text();
            console.log(`response text: ${text}`);
            const lines = text.split('\n').filter(line => line.trim() !== '');
            const secureDomainIndex = lines.findIndex(line => line.trim() === '[secureDomain]');
            const serviceDomainIndex = lines.findIndex(line => line.trim() === '[serviceDomain]');
            const webUrlIndex = lines.findIndex(line => line.trim() === '[webUrl]');

            if (secureDomainIndex === -1 || serviceDomainIndex === -1 || webUrlIndex === -1) {
              console.log('Invalid domain response format - missing section markers');
              continue;
            }

            const newSecureDomains = lines.slice(secureDomainIndex + 1, serviceDomainIndex).map(d => d.trim()).filter(d => d);
            const newServiceDomains = lines.slice(serviceDomainIndex + 1, webUrlIndex).map(d => d.trim()).filter(d => d);
            const newWebPortals = lines.slice(webUrlIndex + 1).map(d => d.trim()).filter(d => d);
            //const newWebPortals = ['gold-2u.com:8448','mygold2u.com:22443','gold2u.app']

            const validatedSecureDomains = await validateSecureDomains(newSecureDomains);

            AppStorage.setSecureDomains(validatedSecureDomains);

            // Validate new web portals
            const validatedWebPortal = await validateWebPortals(newWebPortals);
            if (validatedWebPortal != null) {
              AppStorage.setWebPortals([validatedWebPortal]);
              console.log('Validated web portal:', validatedWebPortal);
            }
            
            // Validate new service domains
            const validatedServiceDomain = await validateSeviceDomain(newServiceDomains);
            if (validatedServiceDomain != null) {
              AppStorage.setServiceDomains([validatedServiceDomain]);
              console.log('Validated service domain:', validatedServiceDomain);
              return true;
            }
          } else {
            console.log(`Secure Domain ${domain} is not accessible`);
            failedDomains.push(domain);
          }
        } catch (error) {
          console.log(`Secure Domain ${domain} check failed`, error);
          failedDomains.push(domain);
        }
      }

      console.log('All domain checks failed');
      AppStorage.setAwaitingUserRetry(true);
      AppStorage.showMessage(translate("errors.noInternet"));
      return false;
    } catch (error) {
      console.log('Error in checkDomains:', error);
      AppStorage.setAwaitingUserRetry(true);
      AppStorage.showMessage(translate("errors.noInternet"));
      return false;
    }
  };

  // Function to validate service domains
  async function validateSecureDomains(domains: string[]): Promise<string[]> {
    const validatedDomain: string[] = [];

    for (const domain of domains) {
      try {
        const url = `https://${domain}`;
        const response = await fetch(url, {
          signal: ApiService.loadCustomAbortSignal(2000),
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          validatedDomain.push(domain);
        } else {
          console.log(`[validateSecureDomains] Secure domain ${domain} is not accessible, response not OK`);
          failedDomains.push(domain);
        }
      } catch (error) {
        console.log(`[validateSecureDomains] Secure domain ${domain} is not accessible`, error);
        failedDomains.push(domain);
      }
    }

    if (validatedDomain.length === 0) {
      console.log('[validateServiceDomains] All service domains failed validation');
    }

    return validatedDomain;
  }

  async function validateWebPortals(webUrls: string[]): Promise<string> {
    for (const webUrl of webUrls) {
      try {
        const url = `https://${webUrl}`;
        const response = await fetch(url, {
          signal: ApiService.loadCustomAbortSignal(2000),
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          return webUrl
        } else {
          console.log(`[validateWebPortals] Web portal ${webUrl} is not accessible, response not OK`);
          failedDomains.push(webUrl);
        }
      } catch (error) {
        console.log(`[validateWebPortals] Web portal ${webUrl} is not accessible`, error);
        failedDomains.push(webUrl);
      }
    }
    console.log('[validateWebPortals] All Web portals failed validation');
    return null;
  }

  async function validateSeviceDomain(domains: string[]): Promise<string> {
    for (const domain of domains) {
      try {
        const url = `https://${domain}`;
        const response = await fetch(url, {
          signal: ApiService.loadCustomAbortSignal(2000),
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok || response.status === 404) {
          return domain
        } else {
          console.log(`[validateSeviceDomain] Service domain ${domain} is not accessible, response not OK`);
          failedDomains.push(domain);
        }
      } catch (error) {
        console.log(`[validateSeviceDomain] Service domain ${domain} is not accessible`, error);
        failedDomains.push(domain);
      }
    }
    console.log('[validateSeviceDomain] All service domains failed validation');
    return null;
  }

  const initializeApp = async () => {
    console.log('[SplashScreen] Initializing...');

    AppStorage.setAwaitingUserRetry(false);

    // Initialize moment locale
    moment.updateLocale('zh-hk', {
      months: Helper.getMonthNames()
    });

    // Initialize UI state
    AppStorage.hidePopup();
    AppStorage.resetPopupShownCount();
    AppStorage.hideLoading();
    AppStorage.hideMessage();

    // Set app icon params
    setUpdateFirstParams();

    // Main initialization sequence
    console.log('[initializeApp] Starting domain verification...');
    try {
      // First ensure domain verification is fully complete
      const domainVerified = await checkDomains();
      if (!domainVerified) {
        throw new Error('Domain verification failed');
      }

      for (const failedDomain of failedDomains) {
        console.log('[initializeApp] creating Domain Failed Log for failed domain: ', 'https://' + failedDomain);
        createDomainFailedLog('https://' + failedDomain,
          (response) => {
            console.log('Domain Failed Log response message: ', response.Message);
          },
          (error) => {
            console.log('Domain Failed Log error: ', error);
          }
        );
      }

      console.log('[initializeApp] Domain verification complete');

      console.log('checking app version...')
      checkAppVersion();

    } catch (error) {
      console.error('[initializeApp] Initialization error:', error);
      // Proceed with version check even if domain check fails
      checkAppVersion();
    }
  };

  useEffect(() => {
    initializeApp();
  }, [])

  return (
    <Screen
      preset="fixed"
    // safeAreaEdges={["top", "bottom"]}
    >
      <Image
        resizeMode="cover"
        source={require("../../../assets/images/bg-splash.png")}
        style={{ width: Dimen.screenWidth, height: Dimen.screenHeight + 40 }} />
    </Screen>
  )
})