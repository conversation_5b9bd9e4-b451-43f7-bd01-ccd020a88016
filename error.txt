yarn android:prod3
yarn run v1.22.22
$ react-native bundle --dev false --platform android --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res/ && cd android && ./gradlew assembleRelease && cd .. && cp -f android/app/build/outputs/apk/release/app-release.apk "G:/我的雲端硬碟/projects/sino"
                Welcome to Metro v0.73.10
              Fast - Scalable - Integrated


info Writing bundle output to:, android/app/src/main/assets/index.android.bundle
info Done writing bundle output
info Copying 150 asset files
info Done copying assets
Starting a Gradle Daemon, 1 incompatible and 1 stopped Daemons could not be reused, use --status for details

> Configure project :app
hotfix_service service disabled!
ha-adapter_service service disabled!
feedback_service service disabled!
tlog_service service disabled!
httpdns_service service disabled!
apm_service service disabled!
man_service service disabled!
registerResGeneratingTask is deprecated, use registerGeneratedResFolders(FileCollection)
registerResGeneratingTask is deprecated, use registerGeneratedResFolders(FileCollection)

> Configure project :expo-application
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-constants
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-device
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-file-system
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-font
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-keep-awake
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-localization
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-modules-core
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo-splash-screen
WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :expo

Using expo modules
  - expo-application (5.1.1)
  - expo-constants (14.2.1)
  - expo-device (5.2.1)
  - expo-file-system (15.2.2)
  - expo-font (11.1.1)
  - expo-keep-awake (12.0.1)
  - expo-localization (14.1.1)
  - expo-modules-core (1.2.7)
  - expo-splash-screen (0.18.2)

WARNING:Software Components will not be created automatically for Maven publishing from Android Gradle Plugin 8.0. To opt-in to the future behavior, set the Gradle property android.disableAutomaticComponentCreation=true in the `gradle.properties` file or use the new publishing DSL.      

> Configure project :react-native-reanimated
AAR for react-native-reanimated has been found
D:\sino\node_modules\react-native-reanimated\android\react-native-reanimated-71-hermes.aar
Warning: SDK processing. This version only understands SDK XML versions up to 3 but an SDK XML file of version 4 was encountered. This can happen if you use versions of Android Studio and the command-line tools that were released at different times.
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 34

This Android Gradle plugin (7.4.2) was tested up to compileSdk = 33

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=34
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 34
WARNING:We recommend using a newer Android Gradle plugin to use compileSdk = 34

This Android Gradle plugin (7.4.2) was tested up to compileSdk = 33

This warning can be suppressed by adding
    android.suppressUnsupportedCompileSdk=34
to this project's gradle.properties

The build will continue, but you are strongly encouraged to update your project to
use a newer Android Gradle Plugin that has been tested with compileSdk = 34

> Task :app:createBundleReleaseJsAndAssets
warning: the transform cache was reset.
                Welcome to Metro v0.73.10
              Fast - Scalable - Integrated


node:internal/fs/watchers:255
    throw error;
    ^

Error: ENOENT: no such file or directory, watch 'D:\sino\node_modules\react-native-image-picker\android\build\intermediates\incremental\release\mergeReleaseResources\merged.dir\values'
    at FSWatcher.<computed> (node:internal/fs/watchers:247:19)
    at Object.watch (node:fs:2550:36)
    at NodeWatcher._watchdir (D:\sino\node_modules\@react-native-community\cli-plugin-metro\node_modules\metro-file-map\src\watchers\NodeWatcher.js:145:24)
    at D:\sino\node_modules\@react-native-community\cli-plugin-metro\node_modules\metro-file-map\src\watchers\NodeWatcher.js:47:14
    at Walker.<anonymous> (D:\sino\node_modules\@react-native-community\cli-plugin-metro\node_modules\metro-file-map\src\watchers\common.js:135:31)
    at Walker.emit (node:events:518:28)
    at D:\sino\node_modules\walker\lib\walker.js:69:16
    at FSReqCallback.oncomplete (node:fs:188:23) {
  errno: -4058,
  syscall: 'watch',
  code: 'ENOENT',
  path: 'D:\\sino\\node_modules\\react-native-image-picker\\android\\build\\intermediates\\incremental\\release\\mergeReleaseResources\\merged.dir\\values',
  filename: 'D:\\sino\\node_modules\\react-native-image-picker\\android\\build\\intermediates\\incremental\\release\\mergeReleaseResources\\merged.dir\\values'
}

Node.js v22.14.0

> Task :app:createBundleReleaseJsAndAssets FAILED

FAILURE: Build completed with 2 failures.

1: Task failed with an exception.
-----------
* What went wrong:
Execution failed for task ':app:createBundleReleaseJsAndAssets'.
> Process 'command 'cmd'' finished with non-zero exit value 1

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

2: Task failed with an exception.
-----------
* What went wrong:
java.lang.StackOverflowError (no error message)

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
==============================================================================

* Get more help at https://help.gradle.org

Deprecated Gradle features were used in this build, making it incompatible with Gradle 8.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.       

See https://docs.gradle.org/7.5.1/userguide/command_line_interface.html#sec:command_line_warnings

BUILD FAILED in 1m 19s
912 actionable tasks: 100 executed, 38 from cache, 774 up-to-date
error Command failed with exit code 1.
info Visit https://yarnpkg.com/en/docs/cli/run for documentation about this command.