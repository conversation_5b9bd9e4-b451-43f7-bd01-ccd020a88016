import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BG_GRAY, BTN_CONTAINER, COLOR_8B, FSIZE_15, SETTING_ROW, TEXT_INPUT, TEXT_SCREEN_TITLE, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { AI_CENTER, COLUMN, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_LEFT, MARGIN_DF_TOP, MARGIN_S_DF_LEFT, MARGIN_S_DF_RIGHT, MARGIN_S_DF_TOP, PADDING_S_DF, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_ALERT, TEXT_COLOR_GREEN, TEXT_GRAY } from "../../theme/mStyle"
import { Switch, TextInput, TouchableOpacity, View } from "react-native"
import { Api, ApiService } from "app/api/api"
import { requestLogin } from "app/api/model"
import { AppStorage } from "app/utils/appStorage"
import { Dialog } from "react-native-simple-dialogs"
import { Helper } from "app/utils/helper"

import DeviceInfo from 'react-native-device-info';
import { BackNavComponent } from "app/components/BackNavComponent"

interface LoginScreenProps extends AppStackScreenProps<"Login"> { }

export const LoginScreen: FC<LoginScreenProps> = observer(function LoginScreen(_props) {
  const [selectPhoneCountryCode, setSelectPhoneCountryCode] = useState("")
  const [selectPhoneCountryDisplay, setSelectPhoneCountryDisplay] = useState("")
  const [phoneLengthFrom, setPhoneLengthFrom] = useState(0)
  const [phoneLengthTo, setPhoneLengthTo] = useState(0)
  const [phone, setPhone] = useState("")
  // const [screenOn, setScreenOn] = useState(false)
  const [showDisplayModeBox, setShowDisplayModeBox] = useState(false)
  const [showClearCacheConfirm, setShowClearCacheConfirm] = useState(false)
  const [refresh, setRefresh] = useState(false)

  const prepareData = () => {
    console.log("prepareData")
    setRefresh(!refresh)
  }

  const goToOtpScreen = () => {
    if (phone === "") {
      ApiService.showMessageBox(translate("errors.enterPhone"))
    } else {
      _props.navigation.navigate("InputOtp",
        {
          phone: phone,
          phoneCountryCode: selectPhoneCountryCode

        })
    }
  }

  const goToUsernameScreen = () => {
    if (phone === "") {
      ApiService.showMessageBox(translate("errors.enterPhone"))
    } else {
      _props.navigation.navigate("InputName",
        {
          phone: phone,
          phoneCountryCode: selectPhoneCountryCode

        })
    }
  }

  const startRequestLogin = async () => {    
    if (selectPhoneCountryCode === "") {
      ApiService.showMessageBox(translate("errors.selectCountry"))
    } else if (phone === "" || !(phone.length >= phoneLengthFrom && phone.length <= phoneLengthTo)) {
      ApiService.showMessageBox(translate("errors.phoneFormatWrong"))
    } else {
      requestLogin(selectPhoneCountryCode, phone,
        () => {
          goToOtpScreen()
        },
        () => {
          goToUsernameScreen()
        },
        (error) => {
          ApiService.showMessageBox(error)
        }
      )
    }

  }

  useEffect(() => {
    prepareData()
  }, [])

  useEffect(() => {
    console.log("language changed")
    prepareData()
  }, [AppStorage.appStorage.setting.language])

  const handleInputChange = (text :string) => {    
    if (!/^\d*$/.test(text)) {      
      console.log('Input is not a number');
      ApiService.showMessageBox(translate("errors.canTypeSymbolForPhone"))      
    } else {      
      setPhone(text);
    }
  };

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }}
        hasPadding={false} />
      <Text style={[TEXT_SCREEN_TITLE, MARGIN_DF_TOP]} text={translate("boarding.register") + "/" + translate("boarding.login")} />
      {
        selectPhoneCountryCode == "" ?
          <TouchableOpacity
            onPress={() => {
              _props.navigation.navigate("SelectCountry", {
                onSelect: (item) => {
                  console.log("onSelect", JSON.stringify(item))
                  setSelectPhoneCountryCode(item.value)
                  setSelectPhoneCountryDisplay(item.name)
                  setPhoneLengthFrom(item.phone_length_from)
                  setPhoneLengthTo(item.phone_length_to)
                },
              })
            }}
            style={[ROW_CENTER, MARGIN_S_DF_TOP]}>
            <Text style={TEXT_SMALL_0} tx="boarding.selectCountry" />
            <MIcon name="down" size={Dimen.iconSize.sm} style={MARGIN_S_DF_LEFT} />
          </TouchableOpacity>
          :
          <TouchableOpacity
            onPress={() => {
              _props.navigation.navigate("SelectCountry", {
                onSelect: (item) => {
                  setSelectPhoneCountryCode(item.value)
                  setSelectPhoneCountryDisplay(item.name)
                  setPhoneLengthFrom(item.phone_length_from)
                  setPhoneLengthTo(item.phone_length_to)
                },
              })
            }}
            style={[ROW_CENTER, MARGIN_S_DF_TOP]}>
            <Text style={TEXT_SMALL_0} tx={selectPhoneCountryDisplay} />
            <Text style={TEXT_SMALL_0} text={" (" + selectPhoneCountryCode + ")"} />
            <MIcon name="down" size={Dimen.iconSize.sm} style={MARGIN_S_DF_LEFT} />
          </TouchableOpacity>
      }
      <TextInput
        allowFontScaling={false}
        keyboardType="number-pad"
        onChangeText={(text) => handleInputChange(text)}
        value={phone}
        style={TEXT_INPUT} />
      <Text
        onPress={() => {
          // goToOtpScreen()
          // goToUsernameScreen()
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          startRequestLogin()
        }}
        style={[BTN_CONTAINER, { marginHorizontal: 0 }]}
        text={translate("boarding.register") + "/" + translate("boarding.login")} />
      <Text style={[TEXT_SECTION, MARGIN_DF_TOP]} tx="boarding.setting" />
      <View style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.screenAlwaysOn" />
        <View style={FLEX} />
        <Text style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]} text={AppStorage.appStorage.setting.screenOn ? translate("boarding.on") : translate("boarding.off")} />
        <Switch
          value={AppStorage.appStorage.setting.screenOn}
          onValueChange={(value) => {
            // setScreenOn(value)
            AppStorage.updateScreenOn(value)
          }}
        />
      </View>
      <TouchableOpacity
        onPress={() => {
          setShowDisplayModeBox(true)
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.displaySetting" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT,
            !Helper.isDisplayModeRed() ? TEXT_COLOR_GREEN : TEXT_COLOR_ALERT]}
          text={!Helper.isDisplayModeRed() ? translate("boarding.displayModeGreen") : translate("boarding.displayModeRed")} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          _props.navigation.navigate("ChooseLanguage")
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.language" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]}
          text={AppStorage.getLanguageName()} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          setShowClearCacheConfirm(true)
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.clearCache" />
        <View style={FLEX} />
        <Text
          style={[TEXT_SMALL, MARGIN_S_DF_RIGHT]} tx="boarding.clear" />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>

      <Text style={[TEXT_SECTION, MARGIN_DF_TOP]} tx="boarding.aboutHan" />
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("WebviewInformation", {
            title: translate("boarding.companyProfile"),
            webUrl: Helper.getCompanyProfileUrl()
          })
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.companyProfile" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("MajorEvent")
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.majorEventOfHan" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => {
          if (!AppStorage.isNetworkConnected()) {
            return
          }
          _props.navigation.navigate("WebviewInformation", {
            title: translate("boarding.hansJournal"),
            webUrl: Helper.getJournalUrl()
          })
        }}
        style={SETTING_ROW}>
        <Text style={[FSIZE_15, COLOR_8B, MARGIN_DF_LEFT]} tx="boarding.hansJournal" />
        <View style={FLEX} />
        <MIcon name="next" size={Dimen.iconSize.sm} />
      </TouchableOpacity>
      <Dialog
        visible={showDisplayModeBox}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowDisplayModeBox(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <MIcon name={Helper.isDisplayModeRed() ? "displayGreen" : "displayRed"} size={60} />
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={Helper.isDisplayModeRed() ? translate("account.displayGreenConfirm") : translate("account.displayRedConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowDisplayModeBox(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={async () => {
                setShowDisplayModeBox(false)
                AppStorage.updateDisplayMode(Helper.isDisplayModeRed() ? Api.displayModes.green : Api.displayModes.red)
                Helper.updateGraphLink()
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <Dialog
        visible={showClearCacheConfirm}
        dialogStyle={{ borderRadius: Dimen.borderRadiusLarge }}
        onTouchOutside={() => setShowClearCacheConfirm(false)}>
        <View style={[PADDING_S_DF, COLUMN, AI_CENTER]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={translate("account.clearCacheConfirm")} />
          <View style={[ROW_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                setShowClearCacheConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_RIGHT, FLEX, BG_GRAY]}
              tx="common.cancel" />
            <Text
              onPress={() => {
                setShowClearCacheConfirm(false)
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, FLEX]}
              tx="common.confirm" />
          </View>
        </View>
      </Dialog>
      <View style={[{ marginTop: 5, height: 70 }]}>
        <Text style={[TEXT_CENTER, TEXT_GRAY]} text={'version: ' + DeviceInfo.getVersion()} />
      </View>
    </Screen>
  )
})
