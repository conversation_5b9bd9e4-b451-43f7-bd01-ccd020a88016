import { observer } from "mobx-react-lite"
import React, { FC, useCallback, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { Helper } from "app/utils/helper"
import { translate } from "app/i18n"
import { AI_CENTER, COLUMN, JC_CENTER, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_S_DF_BOTTOM, MARGIN_S_DF_TOP, ROW, TEXT_CENTER } from "app/theme/mStyle"
import { HIDE_PRELOAD_CONTAINER, TEXT_SECTION, TEXT_SMALL_0 } from "app/theme/baseStyle"
import { FlatList, TextStyle, TouchableOpacity, View, ViewStyle } from "react-native"
import { MIcon } from "app/components/MIcon"
import { Dimen } from "app/theme/dimen"
import moment from "moment"
import { colors } from "app/theme"
import { MOptionalText } from "app/components/MOptionalText"
import { CardCalendarNews } from "../_Home/CardCalendarNews"
import { getCalendarSinoSound } from "app/api/model"
import { AppStorage } from "app/utils/appStorage"
import { Dialog } from "react-native-simple-dialogs"
import {Calendar, LocaleConfig} from 'react-native-calendars';
import { ApiService } from "app/api/api"
import { useNetInfo } from "@react-native-community/netinfo"
import { TopMenuIndex } from "app/navigators/TopMenu"

const WeekLine: ViewStyle = {
  width: '100%',
  height: 1,
  backgroundColor: "#f5f5f5",
  marginVertical: 5
}

const WeekRowContainer: ViewStyle = {
  width: '100%',
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingHorizontal: 10,
  paddingVertical: 5,
  backgroundColor: "#f5f5f5"
}

const CurrentDate: TextStyle = {
  ...TEXT_SMALL_0,
  textAlign: "center",
  color: colors.palette.white,
  backgroundColor: colors.mine.primary,
  borderRadius: 15,
  textAlignVertical: "center",
  width: 30,
  height: 30,
  lineHeight: 30,
  overflow: "hidden"
}

const NormalDate: TextStyle = {
  ...TEXT_SMALL_0,
  textAlign: "center",
  backgroundColor: colors.transparent,  
  borderRadius: 30,
  textAlignVertical: "center",
  width: 30,
  height: 30,
  lineHeight: 30,
}


const TextEN: TextStyle = {
  paddingBottom: 5
}

interface CalendarcreenProps extends AppStackScreenProps<"Trend"> {}
export const Calendarcreen: FC<CalendarcreenProps> = observer(function Calendarcreen(_props) {
  const initialData = {
    "economics": [],
    "event": [],
    "holiday": []  
  }
  const [currentWeekDates, setCurrentWeekDates] = useState([])
  const [showCalendarBox, setShowCalendarBox] = useState(false)
  const [startDateOfCurrentWeek, setStartDateOfCurrentWeek] = useState(moment().startOf('week'))
  const [selectedMonth, setSelectedMonth] = useState(moment().get('month') + "")
  const [selectedDate, setSelectedDate] = useState(moment())
  const dayNamesInweek = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"]
  const [datas, setDatas] = useState(initialData)
  const [displayData, setDisplayData] = useState([])
  const [currentDataTab, setCurrentDataTab] = useState(0)
  const isLoadingMore = useRef(false)
  const pageSize = 10
  Helper.updateCalendarLocale()
  

  const prepareData = () => {
    updateTimeBar(selectedDate)
  }

  const loadCalendarData = () => {
    console.log("loadCalendarData")
    if (!AppStorage.isNetworkConnected()) {
      AppStorage.hidePreload(TopMenuIndex.calendar)
      return
    }
    AppStorage.showLoading()
    getCalendarSinoSound(
      selectedDate.format(Helper.dateFormateList.dateApi),
      (response) => {
        AppStorage.hidePreload(TopMenuIndex.calendar)
        setDatas(() => response.data)
        updateTimeBar(selectedDate)
      },
      (error) => {
        AppStorage.hidePreload(TopMenuIndex.calendar)
        ApiService.showMessageBox(error)
        setDatas(initialData)
        updateTimeBar(selectedDate)
      }
    )
  }

  const switchWeek = (isNext) => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    let startDate = moment(currentWeekDates[0])
    if (isNext) {
      startDate = startDate.add(1, 'week')
    } else {
      startDate = startDate.subtract(1, 'week')
    }
    updateTimeBar(startDate)
  }

  const updateTimeBar = (dateStart) => {
    if (currentWeekDates.length > 0) {
      if (dateStart.isSameOrAfter(currentWeekDates[0]) && dateStart.isSameOrBefore(currentWeekDates[currentWeekDates.length-1])) {
        console.log('updateTimeBar return')
        return
      }
    }    
    const startOfWeek = moment(dateStart).startOf('week');
    const endOfWeek = moment(dateStart).endOf('week');
    let day = startOfWeek;
    const days = [];
    while (day <= endOfWeek) {
      days.push(day);
      day = day.clone().add(1, 'd');
    }
    if (!dateStart.isSame(startDateOfCurrentWeek, "date")) {
      setStartDateOfCurrentWeek(() => dateStart)
    }
    if (selectedDate.isSame(dateStart, 'week')) {
      updateMonth(selectedDate)
    } else {
      updateMonth(dateStart)
    }
    setCurrentWeekDates(() => days)
  }

  const getContentData = (mData = datas) => {
    console.log("getContentData", currentDataTab)
    if (currentDataTab == 0) {
      return mData.economics ? mData.economics.slice(0, pageSize) : []
    } else if (currentDataTab == 1) {
      return mData.event ? mData.event.slice(0, pageSize) : []
    } else {
      return mData.holiday ? mData.holiday.slice(0, pageSize) : []
    }  
  }

  const loadMoreContentData = async () => {
    console.log("loadMoreContentData")
    if (isLoadingMore.current) {
      return
    }
    isLoadingMore.current = true
    if (currentDataTab == 0) {
      AppStorage.showLoading()
      if (displayData.length < datas.economics.length) {
        const newData = datas.economics.slice(displayData.length, displayData.length + pageSize)
        setDisplayData([...displayData, ...newData])
      } else {
        isLoadingMore.current = false
      }
      AppStorage.hideLoading()
    } else if (currentDataTab == 1) {
      AppStorage.showLoading()
      if (displayData.length < datas.event.length) {
        const newData = datas.event.slice(displayData.length, displayData.length + pageSize)
        setDisplayData([...displayData, ...newData])
      } else {
        isLoadingMore.current = false
      }
      AppStorage.hideLoading()
    } else {
      AppStorage.showLoading()
      if (displayData.length < datas.holiday.length) {
        const newData = datas.holiday.slice(displayData.length, displayData.length + pageSize)
        setDisplayData([...displayData, ...newData])
      } else {
        isLoadingMore.current = false
      }
      AppStorage.hideLoading()
    }    
  }

  const renderItem = ({item, index}) => {
    return (
      <CardCalendarNews 
        data={item}
        previousTime={currentDataTab == 0 && index > 0 && datas.economics[index-1] ? datas.economics[index-1].pub_time : ""}
        type={currentDataTab} />
    )
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)

  useEffect(() => {
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
        loadCalendarData()
      }
    }
  }, [netInfo.isConnected])

  // useEffect(() => {
  //   // prepareData()
  //   AppStorage.showLoading()
  //   loadCalendarData()
  // },[])

  useEffect(() => {
    isLoadingMore.current = false    
  }, [displayData])

  useEffect(() => {
    AppStorage.showLoading()
    prepareData()
    updateMonth(selectedDate)
    loadCalendarData()
  }, [selectedDate])

  useEffect(() => {
    setDisplayData(() => getContentData())
  }, [currentDataTab, datas])


  const updateMonth = (selectedDate) => {
    console.log('updateMonth lang:', AppStorage.appStorage.setting.language)
    if (AppStorage.appStorage.setting.language == 'EN') {
      setSelectedMonth(Helper.getCurrentMonthShortEng(selectedDate).toUpperCase()  )
    } else {
      setSelectedMonth(Helper.getCurrentMonthShort(selectedDate) + " " + translate("common.month"))
    }
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["bottom"]}>
      <View style={WeekLine} />
      <Text 
        onPress={() => {setShowCalendarBox(true)}}
        style={[TEXT_CENTER, MARGIN_S_DF_BOTTOM, (AppStorage.appStorage.setting.language == 'EN') ? TextEN : TEXT_SECTION]} 
        text={ selectedMonth } />
      <View style={WeekRowContainer}>
        <MIcon 
          onPress={() => switchWeek(false)}
          name="back" 
          size={Dimen.iconSize.base} />
        {
          currentWeekDates.map((item, index) => {
            return (
              <TouchableOpacity 
                  onPress={() => {
                    if (!AppStorage.isNetworkConnected()) {
                      return
                    }
                    setSelectedDate(item)
                    updateTimeBar(item)
                    // setShowCalendarBox(false)
                  }}
                  key={"d:" + index} 
                  style={[COLUMN, AI_CENTER]} >
                <Text style={TEXT_SMALL_0} text={translate("common." + dayNamesInweek[index])} />
                <Text style={selectedDate.isSame(item, "day") ? CurrentDate : NormalDate} text={Helper.formatDate(item, "DD")} />
              </TouchableOpacity>
            )
          })
        }
        <MIcon 
          onPress={() => switchWeek(true)}
          name="next" 
          size={Dimen.iconSize.base} />
      </View>
      <View style={[ROW, MARGIN_S_DF_TOP, JC_CENTER, { paddingBottom: 5}]}>
        <MOptionalText 
          onPress={() => setCurrentDataTab(0)}
          text={translate("trendPage.events")} 
          isSelected={currentDataTab==0} />
        <MOptionalText 
          onPress={() => setCurrentDataTab(1)}
          containerStyle={[MARGIN_DF_LEFT, MARGIN_DF_RIGHT]}
          text={translate("trendPage.financialEvents")} 
          isSelected={currentDataTab==1}/>
        <MOptionalText 
          onPress={() => setCurrentDataTab(2)}
          text={translate("trendPage.holidayClosure")} 
          isSelected={currentDataTab==2}/>
      </View>
      {
        getContentData().length == 0 && !AppStorage.isLoadingShow() && 
        <Text style={[TEXT_CENTER, {marginTop: 20}]} text={translate("errors.noRelatedData")} />
      }
      {getContentData().length != 0 && 
      <FlatList
        data={displayData}
        contentContainerStyle={{paddingBottom: 220, marginTop: 10}}
        keyExtractor={(item, index) => "data" + currentDataTab + index}
        onEndReached={() => {
          console.log("onEndReached", displayData.length)
          loadMoreContentData()
        }}
        onEndReachedThreshold={1}
        renderItem={renderItem}
      />}
      {AppStorage.isLoadingShow() && <View style={HIDE_PRELOAD_CONTAINER}></View> }
      <Dialog
        visible={showCalendarBox}
        dialogStyle={{borderRadius: Dimen.borderRadiusLarge}}
        onTouchOutside={() => setShowCalendarBox(false)}>
          <Calendar 
            enableSwipeMonths
            onDayPress={(date) => {
              setSelectedDate(moment(date.dateString))
              updateMonth(moment(date.dateString))
              setShowCalendarBox(false)
            }}
            monthNames={Helper.getMonthNames().map(month => translate(month))}
            dayNames={Helper.getDayNames().map(day => translate(day))}
          />
      </Dialog>
    </Screen>
  )
})
