
import axios from 'axios'
import { Alert, Platform } from "react-native"
import Toast from 'react-native-toast-message'
import { AppStorage } from '../utils/appStorage'
import { translate } from 'app/i18n'
import { getAvailableServiceDomain, getAvailableWebPortalURL } from 'app/services/api/api'

export const Api = {
    getUrl: function () {
        console.log('Getting API URL from AppStorage');
        if (AppStorage.isAwaitingUserRetry()) {
            console.log('Awaiting user retry - returning null');
            return null;
        }

        const domain = getAvailableServiceDomain(() => {
            AppStorage.setAwaitingUserRetry(true);
            AppStorage.showMessage(translate("errors.noInternet"));
        });
        console.log('Selected API domain:', domain);
        return 'https://' + domain;
    },
    getWebPortalUrl: function () {
        console.log('Getting Web Portal URL from AppStorage');
        if (AppStorage.isAwaitingUserRetry()) {
            console.log('Awaiting user retry - returning null');
            return null;
        }

        const webPortalURL = getAvailableWebPortalURL(() => {
            AppStorage.setAwaitingUserRetry(true);
            AppStorage.showMessage(translate("errors.noInternet"));
        });
        console.log('Selected Web Portal URL:', webPortalURL);
        return 'https://' + webPortalURL;
    },
    loadCsUrl: function () {
        console.log('Getting CS URL from service domains');
        if (AppStorage.isAwaitingUserRetry()) {
            console.log('Awaiting user retry - returning null');
            return null;
        }

        const domain = getAvailableServiceDomain(() => {
            AppStorage.setAwaitingUserRetry(true);
            AppStorage.showMessage(translate("errors.noInternet"));
        });
        console.log('Selected CS domain:', domain);
        return 'https://' + domain + "/parameters/get/cs_url";
    },
    mt4Link: "Metatrader4://trade/usdrub",
    timeOut: 10000,
    reloadTime: 5 * 60,
    reloadIpTime: 5 * 60 * 1000,
    reloadNewsCalendarNumberInHome: 60, // = 1 minutes
    reloadBalance: 60, // = 1 minutes
    transactionHistoryPageSize: 10,
    postsPerPage: 20,
    feedPerPageInHome: 10,
    // relatedVideoUrl: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4",
    priceReminderItem: [
        {
            value: "GOLD",
            label: translate("newPriceReminderScreen.londonGold")
        },
        {
            value: "SILVER",
            label: translate("newPriceReminderScreen.londonSilver")
        },
        {
            value: "USDINDEX",
            label: translate("newPriceReminderScreen.dollarIndex")
        }
    ],
    priceReminderCondition: ["rise", "drop"],
    priceReminderDwm: ["D", "M"],
    displayModes: {
        red: "Red",
        green: "Green",
    },
    endpoint: {
        banner: {
            getBanners: "/banners",
        },
        posts: {
            getAllPosts: "/post",
            getPostById: "/post",
            search: "/post/search",
        },
        sinoSound: {
            getMarketNews: "/sinosound/getMarketNewsSinoSound",
            getCalendar: "/sinosound/getCalendarSinoSound",
            getTransactionHistory: "/sinosound/ticketStatementSinoSound",
            subscribe: "/sinosound/subscribeSinoSound",
            unsubscribe: "/sinosound/unsubscribeSinoSound",
            getExchangeRate: "/sinosound/getRateSinoSound",
            getCurrentPrice: "/sinosound/getCurrentPriceSinoSound",
            getRegisterPhoneCode: "/sinosound/getRegisterPhoneCode",
            getRegisterEmailCode: "/sinosound/getRegisterEmailCode",
            getRegisterDemoPhoneCode: "/sinosound/getDemoRegisterPhoneCode",
            getRegisterDemoEmailCode: "/sinosound/getDemoRegisterEmailCode",
            getLinkSinoSound: "/sinosound/getLinkSinoSound",
            getClientLinkSinoSound: "/sinosound/getClientLinkSinoSound",
            getRegLinkSinoSound: "/sinosound/getRegLinkSinoSound",
            getGraphLinkSinoSound: "/sinosound/getGraphLinkSinoSound",
            createDomainFailedLog: "/sinosound/createDomainFailedLog",
        },
        boarding: {
            requestLogin: "/users/requestLogin",
            login: "/users/userLogin",
            requestRegisterPortalUser: "/users/requestRegister",
            createUser: "/users"
        },
        user: {
            getUserWithBanner: "/users/getUserWithoutImage",
            getMySimpleInfo: "/users/me",
            updateUser: "/users",
            deleteUser: "/users",
            uploadImage: "/users/uploadImg",
            registerLiveAccount: "/users/registerLiveAccount",
            registerDemoAccount: "/users/registerDemoAccount",
            updateDeviceId: "/users/updateDeviceId",
        },
        priceReminder: {
            create: "/priceReminders",
            get: "/priceReminders",
            delete: "/priceReminders"
        },
        inbox: {
            getAll: "/inboxMessage",
            updateInboxBatch: "/inboxMessage/updateBatch",
            updateInboxRead: "/inboxMessage/updateRead",
            getAllInboxBatchCount: "/inboxMessage/batchCount",
        },
        popup: {
            getAll: "/popups",
        },
        params: {
            androidVersion: "/parameters/get/android_ver",
            iosVersion: "/parameters/get/ios_ver",
            androidDownloadLink: "/parameters/get/android_download",
            iosDownloadLink: "/parameters/get/ios_download",
            priceReminderMinLink: "/parameters/get/price_reminder_min",
            priceReminderMaxLink: "/parameters/get/price_reminder_max",
        }
    },
    requestKey: "#request",
    token: "#token",
    dateFormat: "YYYY-MM-DD HH:mm:ss",
    dateFormatShort: "YYYY-MM-DD",
    dateBirthdayFormat: "DD/MM/YYYY",
    dateMonthYear: "MMMM YYYY",
    dateTimelineFormat: "MMMM D, YYYY",
    dateTimelineFormatShot: "MMM D, YYYY",
    dateAtions: "MMMM DD",
}

export const ApiService = {
    handleNetworkError: function (
        error: any,
        endpoint: string,
        retryCount: number,
        maxRetries: number,
        retryFunction: Function,
        args: any[],
        failureCallback: Function,
        showMessage: boolean = true
    ) {
        failureCallback(translate("errors.noInternet"));
        return;
    },

    isIOS: function () {
        return Platform.OS === "ios"
    },
    showMessageBox: function (message) {
        Alert.alert(
            "",
            message,
            [
                {
                    text: translate("common.ok"),
                    onPress: () => {
                        console.log("alert is close")
                    }
                },
            ]
        )
    },
    showMessageBoxAndBack: function (message, callback) {
        Alert.alert(
            "",
            message,
            [
                {
                    text: translate("common.ok"),
                    onPress: () => {
                        callback()
                    }
                },
            ]
        )
    },
    showMessageBoxAndCustomTextBack: function (message, btnText, callback) {
        Alert.alert(
            "",
            message,
            [
                {
                    text: btnText,
                    onPress: () => {
                        callback()
                    }
                },
            ]
        )
    },
    showMessageBoxTwoOption: function (message, callbackYes, callbackNo) {
        Alert.alert(
            "",
            message,
            [
                {
                    text: translate("common.yes"),
                    onPress: () => {
                        callbackYes()
                    }
                },
                {
                    text: translate("common.no"),
                    onPress: () => {
                        callbackNo()
                    }
                },
            ]
        )
    },
    showMessageBoxTwoOptionCustomeBtn: function (message, btnYesText, btnNoText, callbackYes, callbackNo) {
        Alert.alert(
            "",
            message,
            [
                {
                    text: btnYesText,
                    onPress: () => {
                        callbackYes()
                    }
                },
                {
                    text: btnNoText,
                    onPress: () => {
                        callbackNo()
                    }
                },
            ]
        )
    },
    showSuccessToast: function (message) {
        Toast.show({
            type: 'success',
            text1: message,
            position: "bottom",
            visibilityTime: 3000,
        });
        // Toast.showSuccess(message, {
        //     duration: 3000,
        //     position: Toast.position.CENTER
        // })
    },
    showShortToast: function (message) {
        Toast.show({
            type: 'success',
            text1: message,
            position: "bottom",
            visibilityTime: 3000,
        });
    },
    generateGetUrl: function (endpoint, requestObject) {
        let urlRq = Api.getUrl() + endpoint
        if (requestObject) {
            let paramStr = ""
            Object.keys(requestObject).forEach(function (key) {
                if (paramStr === "") {
                    paramStr = paramStr + key + "=" + requestObject[key]
                } else {
                    paramStr = paramStr + "&" + key + "=" + requestObject[key]
                }
            })
            if (paramStr !== "") {
                urlRq = urlRq + "?" + paramStr
            }
        }
        return urlRq
    },
    callGetRequestNoToken: function (endpoint, data, successCallback, failureCallback, showMessage = true, retryCount = 0) {
        const maxRetries = 1;
        console.log("endpoint: ", endpoint)
        const getRequestUri = this.generateGetUrl(endpoint, data)
        console.log("getRequestUri", getRequestUri)
        const config = {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: Api.timeOut,
            signal: ApiService.loadAbortSignal()
        }
        axios.get(getRequestUri, config)
            .then(response => {
                successCallback(response.data)
            })
            .catch(error => {
                ApiService.handleNetworkError(
                    error,
                    endpoint,
                    retryCount,
                    maxRetries,
                    this.callGetRequestNoToken.bind(this),
                    [endpoint, data, successCallback, failureCallback, showMessage],
                    failureCallback,
                    showMessage
                );
            })
    },
    callGetRequestDirectly: function (endpoint, data, successCallback, failureCallback) {
        const maxRetries = 0; // No retry for direct requests
        const retryCount = 0;
        const config = {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: Api.timeOut,
            signal: ApiService.loadAbortSignal()
        }
        axios.get(endpoint, config)
            .then(response => {
                successCallback(response.data)
            })
            .catch(error => {
                ApiService.handleNetworkError(
                    error,
                    endpoint,
                    retryCount,
                    maxRetries,
                    this.callGetRequestDirectly.bind(this),
                    [endpoint, data, successCallback, failureCallback],
                    failureCallback,
                    true
                );
            })
    },
    callPostRequestNoToken: function (endpoint, data, successCallback, failureCallback, showMessage = true, retryCount = 0) {
        const maxRetries = 1;
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        const config = {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: Api.timeOut,
            signal: ApiService.loadAbortSignal()
        }
        axios.post(Api.getUrl() + endpoint, data, config)
            .then(response => {
                successCallback(response.data)
            })
            .catch(error => {
                ApiService.handleNetworkError(
                    error,
                    endpoint,
                    retryCount,
                    maxRetries,
                    this.callPostRequestNoToken.bind(this),
                    [endpoint, data, successCallback, failureCallback, showMessage],
                    failureCallback,
                    showMessage
                );
            })
    },
    callPostRequest: async function (endpoint, data, successCallback, failureCallback, wrongTokenCallback) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            ApiService.onApiError(-1, endpoint)
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for token-based requests
            const retryCount = 0;
            const config = {
                headers: {
                    Authorization: `Bearer ${AppStorage.appStorage.accessToken}`,
                },
                timeout: Api.timeOut,
                signal: ApiService.loadAbortSignal()
            };
            axios.post(Api.getUrl() + endpoint, data, config)
                .then(response => {
                    if (response.data) {
                        if (response.data.code && response.data.code == -200) {
                            wrongTokenCallback()
                        } else if (response.data.code && response.data.code == -201) {
                            ApiService.onApiError("-201", endpoint)
                            failureCallback("-201")
                        } else if (response.data.error) {
                            ApiService.onApiError(response.data.error, endpoint)
                            failureCallback(response.data.error)
                        } else {
                            successCallback(response.data)
                        }
                    } else {
                        ApiService.onApiError(response, endpoint)
                        failureCallback(response)
                    }
                })
                .catch(error => {
                    if (endpoint.includes(Api.endpoint.user.registerDemoAccount) || endpoint.includes(Api.endpoint.user.registerLiveAccount)) {
                        if (error.response && error.response.data && error.response.data.error) {
                            failureCallback(error.response.data.error)
                        } else {
                            failureCallback(error)
                        }
                    } else {
                        ApiService.handleNetworkError(
                            error,
                            endpoint,
                            retryCount,
                            maxRetries,
                            this.callPostRequest.bind(this),
                            [endpoint, data, successCallback, failureCallback, wrongTokenCallback],
                            failureCallback,
                            true
                        );
                    }
                })
        }
    },
    callPostRequestWithCustomTime: async function (
        endpoint,
        data,
        timeoutInMilisecond = Api.timeOut,
        successCallback,
        failureCallback,
        wrongTokenCallback) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            ApiService.onApiError(-1, endpoint)
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for custom timeout requests
            const retryCount = 0;
            const config = {
                headers: {
                    Authorization: `Bearer ${AppStorage.appStorage.accessToken}`,
                },
                timeout: timeoutInMilisecond,
                signal: ApiService.loadCustomAbortSignal(timeoutInMilisecond)
            };
            axios.post(Api.getUrl() + endpoint, data, config)
                .then(response => {
                    if (response.data) {
                        if (response.data.code && response.data.code == -200) {
                            wrongTokenCallback()
                        } else if (response.data.code && response.data.code == -201) {
                            ApiService.onApiError("-201", endpoint)
                            failureCallback("-201")
                        } else if (response.data.error) {
                            ApiService.onApiError(response.data.error, endpoint)
                            failureCallback(response.data.error)
                        } else {
                            successCallback(response.data)
                        }
                    } else {
                        ApiService.onApiError(response, endpoint)
                        failureCallback(response)
                    }
                })
                .catch(error => {
                    if (endpoint.includes(Api.endpoint.user.registerDemoAccount) || endpoint.includes(Api.endpoint.user.registerLiveAccount)) {
                        if (error.response && error.response.data && error.response.data.error) {
                            failureCallback(error.response.data.error)
                        } else {
                            failureCallback(error)
                        }
                    } else {
                        ApiService.handleNetworkError(
                            error,
                            endpoint,
                            retryCount,
                            maxRetries,
                            this.callPostRequestWithCustomTime.bind(this),
                            [endpoint, data, timeoutInMilisecond, successCallback, failureCallback, wrongTokenCallback],
                            failureCallback,
                            true
                        );
                    }
                })
        }
    },
    callPutRequest: async function (endpoint, data, successCallback, failureCallback, wrongTokenCallback, showMessage = true) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        console.log("accessToken", AppStorage.appStorage.accessToken)
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            if (showMessage) {
                ApiService.onApiError(-1, endpoint)
            }
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for PUT requests
            const retryCount = 0;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${AppStorage.appStorage.accessToken}`
                },
                timeout: Api.timeOut,
                signal: ApiService.loadAbortSignal()
            };
            axios.put(Api.getUrl() + endpoint, data, config)
                .then(response => {
                    if (response.data && response.data.code && response.data.code == -200) {
                        wrongTokenCallback()
                    } else if (response.data.code && response.data.code == -201) {
                        ApiService.onApiError("-201", endpoint)
                        failureCallback("-201")
                    } else {
                        successCallback(response.data)
                    }
                })
                .catch(error => {
                    ApiService.handleNetworkError(
                        error,
                        endpoint,
                        retryCount,
                        maxRetries,
                        this.callPutRequest.bind(this),
                        [endpoint, data, successCallback, failureCallback, wrongTokenCallback, showMessage],
                        failureCallback,
                        showMessage
                    );
                })
        }
    },
    callGetRequest: function (endpoint, data, successCallback, failureCallback, wrongTokenCallback, showMessage = true) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            if (showMessage) {
                ApiService.onApiError(-1, endpoint)
            }
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for token-based GET requests
            const retryCount = 0;
            const config = {
                headers: {
                    Authorization: `Bearer ${AppStorage.appStorage.accessToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: Api.timeOut,
                signal: ApiService.loadAbortSignal()
            };
            const getRequestUri = this.generateGetUrl(endpoint, data)
            axios.get(getRequestUri, config)
                .then(response => {
                    if (response.data && response.data.code && response.data.code == -200) {
                        wrongTokenCallback()
                    } else if (response.data.code && response.data.code == -201) {
                        ApiService.onApiError("-201", endpoint)
                        failureCallback("-201")
                    } else {
                        successCallback(response.data)
                    }
                })
                .catch(error => {
                    ApiService.handleNetworkError(
                        error,
                        endpoint,
                        retryCount,
                        maxRetries,
                        this.callGetRequest.bind(this),
                        [endpoint, data, successCallback, failureCallback, wrongTokenCallback, showMessage],
                        failureCallback,
                        showMessage
                    );
                })
        }
    },
    callGetRequestWithBody: function (endpoint, data, successCallback, failureCallback, wrongTokenCallback) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            ApiService.onApiError(-1, endpoint)
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for GET requests with body
            const retryCount = 0;
            const config = {
                headers: {
                    Authorization: `Bearer ${AppStorage.appStorage.accessToken}`,
                    'Content-Type': 'application/json'
                },
                timeout: Api.timeOut,
                signal: ApiService.loadAbortSignal(),
                params: data
            };
            const getRequestUri = this.generateGetUrl(endpoint, {})
            axios.get(getRequestUri, config)
                .then(response => {
                    if (response.data && response.data.code && response.data.code == -200) {
                        wrongTokenCallback()
                    } else if (response.data.code && response.data.code == -201) {
                        ApiService.onApiError("-201", endpoint)
                        failureCallback("-201")
                    } else {
                        successCallback(response.data)
                    }
                })
                .catch(error => {
                    ApiService.handleNetworkError(
                        error,
                        endpoint,
                        retryCount,
                        maxRetries,
                        this.callGetRequestWithBody.bind(this),
                        [endpoint, data, successCallback, failureCallback, wrongTokenCallback],
                        failureCallback,
                        true
                    );
                })
        }
    },
    callDeleteRequest: function (endpoint, data, successCallback, failureCallback, wrongTokenCallback) {
        console.log("endpoint", Api.getUrl() + endpoint)
        console.log("data", JSON.stringify(data))
        if (AppStorage.appStorage.accessToken == null || AppStorage.appStorage.accessToken == undefined || AppStorage.appStorage.accessToken === "") {
            ApiService.onApiError(-1, endpoint)
            failureCallback("")
        } else {
            const maxRetries = 0; // No retry for DELETE requests
            const retryCount = 0;
            const config = {
                headers: { Authorization: `Bearer ${AppStorage.appStorage.accessToken}` },
                timeout: Api.timeOut,
                signal: ApiService.loadAbortSignal()
            };
            const getRequestUri = this.generateGetUrl(endpoint, data)
            axios.delete(getRequestUri, config)
                .then(response => {
                    if (response.data && response.data.code && response.data.code == -200) {
                        wrongTokenCallback()
                    } else if (response.data.code && response.data.code == -201) {
                        ApiService.onApiError("-201", endpoint)
                        failureCallback("-201")
                    } else {
                        successCallback(response.data)
                    }
                })
                .catch(error => {
                    ApiService.handleNetworkError(
                        error,
                        endpoint,
                        retryCount,
                        maxRetries,
                        this.callDeleteRequest.bind(this),
                        [endpoint, data, successCallback, failureCallback, wrongTokenCallback],
                        failureCallback,
                        true
                    );
                })
        }
    },
    onApiError: function (error, endpoint = "not mention") {
        console.log("error ", endpoint)
        if (error == "-201") {
            console.log("error -201", translate("errors.accountDisabled"))
            return
        }
        if (error.message == "canceled") {
            // AppStorage.showMessage(translate("errors.networkTimeout"))
            console.log(endpoint + " -- " + translate("errors.networkTimeout"))
        } else {
            if (error.data != null && error.data.msg) {
                this.showShortToast(error.data.msg)
            } else if (error.msg) {
                this.showShortToast(error.msg)
            } else if (error.message) {
                this.showShortToast(error.message)
            } else {
                console.log(endpoint + " -2- " + translate("errors.networkDelay"))
                AppStorage.showMessage(translate("errors.networkDelay"))
            }
        }

    },
    isApiResponseSuccess(response) {
        return parseInt(response.status) === 200
    },
    isApiCodeSuccess(responseData) {
        return parseInt(responseData.Code) === 200
    },
    isSessionWrong(responseData) {
        return parseInt(responseData.rc) === 201
    },
    loadAbortSignal: function () {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
            controller.abort();
        }, Api.timeOut || 500);
        controller.signal.addEventListener("abort", () => {
            clearTimeout(timeoutId);
        });
        return controller.signal;
    },
    loadCustomAbortSignal: function (customTimeoutInMilisecond = Api.timeOut) {
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => {
            abortController.abort();
        }, customTimeoutInMilisecond || 500);
        abortController.signal.addEventListener("abort", () => {
            clearTimeout(timeoutId);
        });
        return abortController.signal;
    }
}