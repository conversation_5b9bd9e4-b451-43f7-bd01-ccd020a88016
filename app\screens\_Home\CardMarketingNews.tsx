import { Helper } from "app/utils/helper"
import { Text } from "../../components"
import { translate } from "../../i18n"
import { COLOR_WHITE, TEXT_H4, TEXT_SECTION, TEXT_SMALL, TEXT_SMALL_0 } from "../../theme/baseStyle"
import { Dimen } from "../../theme/dimen"
import { AI_CENTER, BOLD, COLUMN, FLEX, JC_CENTER, MARGIN_DF_LEFT, MARGIN_DF_TOP, MARGIN_S_DF_RIGHT, MARGIN_S_DF_TOP, ROW, W_100P } from "../../theme/mStyle"
import * as React from "react"
import { Platform, TouchableOpacity, View, ViewStyle } from "react-native"
import WebView from "react-native-webview"
import moment from "moment"
import { useEffect, useState } from "react"
import { MIcon } from "app/components/MIcon"
import { colors } from "app/theme"
import { AppStorage } from "app/utils/appStorage"
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';


const CardNewsContainer: ViewStyle = {
    ...COLUMN,
    width: Dimen.screenWidth - 2*Dimen.padding.base,
    minHeight: Dimen.screenWidth * 0.3,
    padding: Dimen.padding.sm,
    backgroundColor: 'white',
    borderRadius: Dimen.borderRadiusLarge,
    marginHorizontal: Dimen.padding.base,
    marginVertical: Dimen.padding.ssm,
}

const NewsDateContainer: ViewStyle = {
    borderRadius: Dimen.borderRadiusLarge,
    width: Dimen.screenWidth * 0.17,
    height: Dimen.screenWidth * 0.17,
    backgroundColor: colors.mine.gray,
    ...COLUMN,
    ...AI_CENTER,
    ...JC_CENTER,
}

const TextDate: TextStyle = { 
    fontSize: Dimen.fontSize.xl,
    lineHeight: Dimen.lineHeight.md,
    fontWeight: Platform.OS === 'ios' ? '900' : 'bold',
    color: colors.mine.white,
  }
  
  const TextMonth: TextStyle = { 
    ...TEXT_SMALL,
    marginStart: 0,
    lineHeight: Dimen.lineHeight.base,
    marginTop: -4,
    marginBottom: 0,
    color: colors.mine.white,
    /* letterSpacing: -3 */
  }

const NewsContentContainer: ViewStyle = {
    ...COLUMN,
    ...FLEX,
    marginLeft: Dimen.padding.sm,
}

export function CardMarketingNews (props: any) {
    const {
        containerStyle = {},
        showDetails = false,
        data = {
            id: 1,
            type: 1
        },
        onPress = () => {
            console.log("CardMarketingNews pressed")
        },
        showMoreInline = false
    } = props
    const [showFullContent, setShowFullContent] = useState(false)

    const onPressFindMore = () => {
        if (!AppStorage.isNetworkConnected()) {
            return
          }
        if (showMoreInline) {
            setShowFullContent(!showFullContent)
        } else {
            if (onPress) {
                onPress()
            }
        }
    }

    useEffect(() => {
        moment.updateLocale('zh-hk', {
            months : Helper.getMonthNames()
          })
    },[])

    return (
        <View
            style={[CardNewsContainer, containerStyle]}>
            <View style={[ROW, W_100P]}>
                <View style={NewsDateContainer}>
                    <Text style={TextDate} text={moment(data.publishtime ? data.publishtime : "").get("date") + ""} />
                    {
                        AppStorage.appStorage.setting.language == 'EN' && 
                        <Text 
                            style={TextMonth} 
                            text={Helper.getCurrentMonthShortEng(data.publishtime ? data.publishtime : "")} />
                    }
                    {
                        AppStorage.appStorage.setting.language != 'EN' && 
                        <Text 
                            style={TextMonth} 
                            text={Helper.getCurrentMonthShort(data.publishtime ? data.publishtime : "") + translate("common.month")} />
                    }
                    
                </View>
                <View style={NewsContentContainer}>
                    <View style={ROW}>
                        <Text numberOfLines={1} style={[TEXT_SECTION, FLEX]} text={data.title} />
                        <Text style={[TEXT_SMALL_0, MARGIN_DF_LEFT]} text={moment(data.publishtime).format("HH:mm")} />
                    </View>
                    {!showDetails && 
                        <View style={COLUMN}>
                            {!showMoreInline && <Text style={[TEXT_SMALL_0, MARGIN_DF_TOP]} text={Helper.removeHtmlTag(data.content)}/>}
                            {!showFullContent && showMoreInline && <Text numberOfLines={5} style={[TEXT_SMALL_0, MARGIN_DF_TOP]} text={Helper.removeHtmlTag(data.content)}/>}
                            {showFullContent && showMoreInline && <Text style={[TEXT_SMALL_0, MARGIN_DF_TOP]} text={Helper.removeHtmlTag(data.content)}/>}
                            {
                                Helper.removeHtmlTag(data.content).length > 300 &&
                                <TouchableOpacity 
                                    onPress={onPressFindMore}
                                    style={[ROW, AI_CENTER, MARGIN_S_DF_TOP]}>
                                    <Text 
                                        style={[TEXT_SMALL_0, BOLD, MARGIN_S_DF_RIGHT]} 
                                        text={showFullContent ? translate("trendPage.readLess"): translate("trendPage.readMore")} />
                                    <MIcon name={showFullContent ? "up" : "down"} size={Dimen.iconSize.sm}/>
                                </TouchableOpacity>
                            }
                        </View>}                
                </View>
            </View>
            {
                showDetails &&                    
                <WebView
                    style={{width: Dimen.screenWidth - 3*Dimen.padding.base, height: Dimen.screenHeight * 0.7}}
                    originWhitelist={['*']}
                    scalesPageToFit={true}
                    allowsJavaScript={true}
                    zoomable={true}
                    injectedJavaScript={`const meta = document.createElement('meta'); meta.setAttribute('content', 'width=width, initial-scale=1, maximum-scale=2, user-scalable=4.0'); meta.setAttribute('name', 'viewport'); document.getElementsByTagName('head')[0].appendChild(meta); `}
                    source={{ html: data.content}}
                />
            }
        </View>
    )
}