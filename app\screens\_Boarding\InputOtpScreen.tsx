import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { BTN_CONTAINER, BTN_GRAY_CONTAINER, BTN_GREEN_CONTAINER, BTN_RED_CONTAINER, FSIZE_13, FSIZE_15, FSIZE_17, FSIZE_24, INVISIBLE, TEXT_SCREEN_TITLE, TEXT_SMALL, TEXT_SMALL_0, VISIBLE } from "../../theme/baseStyle"
import { BOLD, DISABLED, ENABLED, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_TOP, MARGIN_S_DF_RIGHT, MARGIN_S_DF_TOP, ROW_CENTER, W_100P } from "../../theme/mStyle"
import { Keyboard, TextInput, TextStyle, View } from "react-native"
import { colors, typography } from "app/theme"
import { createUser, getMyAccount, getUserWithoutBanner, requestLogin, requestRegisterPortalUser, userLogin } from "app/api/model"
import { ApiService } from "app/api/api"
import { AppStorage } from "app/utils/appStorage"
import { Helper } from "app/utils/helper"
import i18n from "i18n-js"

interface InputOtpScreenProps extends AppStackScreenProps<"InputOtp"> {}

const TextInputOtp: TextStyle = {
    flex: 1,
    marginHorizontal: Dimen.padding.ssm,
    fontFamily: typography.primary.medium,
    fontSize: Dimen.fontSize.base,
    color: colors.mine.text,
    padding: Dimen.padding.sm,
    backgroundColor: colors.mine.bgGrey,
    borderRadius: Dimen.borderRadiusLarge,
    textAlign: "center",
    height: 10 + (Dimen.screenWidth - 12 * Dimen.padding.ssm - 2*Dimen.padding.base) / 6,
}

const TextInputOtpCurrent: TextStyle = {
  borderColor: "#ED6B75",
  borderWidth: 1,
}

export const InputOtpScreen: FC<InputOtpScreenProps> = observer(function InputOtpScreen(_props) {
  
  const [selectPhoneCountryCode, setSelectPhoneCountryCode] = useState("")
  const [phone, setPhone] = useState("")
  const [userName, setUserName] = useState("")
  const [showResultState, setShowResultState] = useState(1)
  const [errorText, setErrorText] = useState("")
  const [countDownSecond, setCountDownSecond] = useState(60)
  const [currentFocus, setCurrentFocus] = useState(0)
  const [code1, setCode1] = useState("")
  const [code2, setCode2] = useState("")
  const [code3, setCode3] = useState("")
  const [code4, setCode4] = useState("")
  const [code5, setCode5] = useState("")
  const [code6, setCode6] = useState("")
  const tpRef1 = useRef(null)
  const tpRef2 = useRef(null)
  const tpRef3 = useRef(null)
  const tpRef4 = useRef(null)
  const tpRef5 = useRef(null)
  const tpRef6 = useRef(null)

  const timerRef = useRef(null)
  const countDownSecondRef = useRef(countDownSecond);


  const prepareData = () => {    
    if (_props.route.params) {
      if (_props.route.params?.phoneCountryCode) {
        setSelectPhoneCountryCode(_props.route.params?.phoneCountryCode)
      }
      if (_props.route.params?.phone) {
        setPhone(_props.route.params?.phone)
      }
      if (_props.route.params?.userName) {
        setUserName(_props.route.params?.userName)
      } else {
        setUserName("")
      }
    } else {
      setUserName("")
    }
  }

  const codeInput = (text: string, pos: number) => {
    if (text == "") {
      // back
      if (pos == 1) {
        setCode1(text)
      } else if (pos == 2) {
        setCode2(text)
        tpRef1.current.focus()
      } else if (pos == 3) {
        setCode3(text)
        tpRef2.current.focus()
      } else if (pos == 4) {
        setCode4(text)
        tpRef3.current.focus()
      } else if (pos == 5) {
        setCode5(text)
        tpRef4.current.focus()
      } else if (pos == 6) {
        setCode6(text)
        tpRef5.current.focus()
      }
    } else {
      if (pos == 1) {
        setCode1(text)
        tpRef2.current.focus()
      } else if (pos == 2) {
        setCode2(text)
        tpRef3.current.focus()
      } else if (pos == 3) {
        setCode3(text)
        tpRef4.current.focus()
      } else if (pos == 4) {
        setCode4(text)
        tpRef5.current.focus()
      } else if (pos == 5) {
        setCode5(text)
        tpRef6.current.focus()
      } else if (pos == 6) {
        setCode6(text)
      }
    }   
  }

  const isSubmitEnable = () => {
    if (code1 != "" && code2 != "" && code3 != "" && code4 != "" && code5 != "" && code6 != "") {
      return true
    }
    return false
  }

  const handleFocus = (index: number) => {
    setCurrentFocus(index); // Update the current focus index
  };

  const autoLoginAfterRegistration = async (data) => {
    console.log("autoLoginAfterRegistration", JSON.stringify(data))
    console.log("autoLoginAfterRegistration id", data.id)
    if (data.accessToken != null && data.accessToken != "") {
      console.log("autoLoginAfterRegistration accessToken", data.accessToken)
      await AppStorage.setUserDevice({
        id: data.id,
        clientId: data.clientId
      })
      await AppStorage.updateUserId(data.id)
      await AppStorage.setAccessToken(data.accessToken)
      await AppStorage.setRefreshToken(data.refreshToken)                
      loadUserInfoBeforeGoHome(data.id)
    }
  }

  const loadUserInfoBeforeGoHome =  async (id) => {
    console.log("loadUserInfoBeforeGoHome")
    getUserWithoutBanner(
      id,
      (data) => {
        AppStorage.hideLoading()
        console.log("user data:", data)
        let isSubscribed = false
        AppStorage.setUserDevice(data.User)
        if (Helper.isLiveAccount(data.User)) {
          AppStorage.setUserOtherInfo(data?.OA)
          if (data?.OA) {
            if (data?.OA?.data && data?.OA?.data.subscribe_state == 1) {
              isSubscribed = true
            } else {
              isSubscribed = false                
            }
          }
        }
        if (Helper.isDemoUser(data.User)) {
          AppStorage.setUserOtherInfo(data?.Demo)
        }
        AppStorage.updateSetting({
          screenOn: AppStorage.appStorage.setting.screenOn,
          emailSubscription: isSubscribed,
          displayMode: data.User?.settings.redGreen,
          language: data.User?.settings.language,
          clearCache: AppStorage.appStorage.setting.clearCache,
        })
        i18n.locale = data.User?.settings.language.toLowerCase()
        _props.navigation.reset({
          index: 0,
          routes: [{ name: "BottomTab" }],
        })
      }, 
      (error) => {
        console.log(error)
        ApiService.showMessageBox(error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const verifyOtp = async () => {
    Keyboard.dismiss()
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (isSubmitEnable()) {
      const otp = code1 + code2 + code3 + code4 + code5 + code6
      console.log("verifyOtp: " + otp)
      if (userName == "") {
        userLogin(
          selectPhoneCountryCode,
          phone,
          otp,
          "_",
          (response) => {
            setErrorText("")
            if (response.id != null && response.id != undefined && response.id != 0) {
              loadUserInfoBeforeGoHome(response.id)
              // getUserWithoutBanner(
              //   response.id,
              //   (data) => {
              //     AppStorage.hideLoading()
              //     console.log("user data:", data)
              //     let isSubscribed = false
              //     AppStorage.setUserDevice(data.User)
              //     if (Helper.isLiveAccount(data.User)) {
              //       AppStorage.setUserOtherInfo(data?.OA)
              //       if (data?.OA) {
              //         if (data?.OA?.data.subscribe_state == 1) {
              //           isSubscribed = true
              //         } else {
              //           isSubscribed = false                
              //         }
              //       }
              //     }
              //     if (Helper.isDemoUser(data.User)) {
              //       AppStorage.setUserOtherInfo(data?.Demo)
              //     }
              //     AppStorage.updateSetting({
              //       screenOn: AppStorage.appStorage.setting.screenOn,
              //       emailSubscription: isSubscribed,
              //       displayMode: data.User?.settings.redGreen,
              //       language: data.User?.settings.language,
              //       clearCache: AppStorage.appStorage.setting.clearCache,
              //     })
              //     i18n.locale = data.User?.settings.language.toLowerCase()
              //   }, 
              //   (error) => {
              //     console.log(error)
              //     ApiService.showMessageBox(error)
              //   },
              //   () => {
              //     _props.navigation.navigate("Logout")
              //   }
              // )
            } else {
              getMyAccountInfo()
            }
          },
          (responseMessage) => {
            console.log("userLogin responseMessage", responseMessage)
            setErrorText(responseMessage)
            clearOtpCode()
          },
          (error) => {
            console.log("userLogin error", error)
            if (error && error.Code == -202) {
              ApiService.showShortToast(translate("errors.accountDisabled"))
            }else{
              ApiService.showMessageBox(error)
            }
          }
        )
      } else {
        createUser(
          selectPhoneCountryCode,
          phone,
          otp,          
          userName,
          (response) => {
            setErrorText("")
            console.log("createUser success", JSON.stringify(response))
            autoLoginAfterRegistration(response)
            // ApiService.showMessageBoxAndCustomTextBack(
            //   translate("boarding.registerSuccess"),
            //   translate("boarding.returnHome"),
            //   () => {
            //     _props.navigation.reset({
            //       index: 0,
            //       routes: [{ name: "BottomTab" }],
            //     })
            //   }
            // )
          },
          (response) => {
            setErrorText(translate("errors.phoneExits"))
            console.log("phoneExits", response)
            clearOtpCode()
          },
          (error) => {      
            setErrorText(translate("errors.phoneVerificationIncorrect"))      
            console.log("createUser error", JSON.stringify(error))
            clearOtpCode()
          }
        )
      }
    }
  }

  const clearOtpCode = () => {
    setCode1("")
    setCode2("")
    setCode3("")
    setCode4("")
    setCode5("")
    setCode6("")
    tpRef1.current.focus()
  }

  const getMyAccountInfo = () => {
    getMyAccount(
      (response) => {
        console.log("getMyAccount success", JSON.stringify(response))                
        if (response && response.id) {
          getUserWithoutBanner(
            response.id,
            (data) => {
              AppStorage.hideLoading()
              console.log("user data:", data)
              AppStorage.setUserDevice(data.User)
              let isSubscribed = false
              if (Helper.isLiveAccount(data.User)) {
                AppStorage.setUserOtherInfo(data?.OA)
                if (data?.OA) {
                  if (data?.OA?.data && data?.OA?.data.subscribe_state == 1) {
                    isSubscribed = true
                  } else {
                    isSubscribed = false                
                  }
                }
              }
              if (Helper.isDemoUser(data.User)) {
                AppStorage.setUserOtherInfo(data?.Demo)
              }    
              AppStorage.updateSetting({
                screenOn: AppStorage.appStorage.setting.screenOn,
                emailSubscription: isSubscribed,
                displayMode: data.User?.settings.redGreen,
                language: data.User?.settings.language,
                clearCache: AppStorage.appStorage.setting.clearCache,
              })
              i18n.locale = data.User?.settings.language.toLowerCase()
              _props.navigation.reset({
                index: 0,
                routes: [{ name: "BottomTab" }],
              })              
            }, 
            (error) => {
              console.log(error)
              ApiService.showMessageBox(error)
            },
            () => {
              _props.navigation.navigate("Logout")
            }
          )
        }
      },
      (error) => {
        ApiService.showMessageBox(error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  const startCountdown = () => {
    if (timerRef.current == null) {
      timerRef.current = setInterval(() => {
        // console.log("countDownSecond: " + countDownSecondRef.current);
        if (countDownSecondRef.current > 0) {
          setCountDownSecond(countDownSecondRef.current - 1);
        } else {
          setShowResultState(0)
          setErrorText("")
          setCountDownSecond(61);
          clearInterval(timerRef.current);
          timerRef.current = null;
          countDownSecondRef.current = 60;
        }
      }, 1000)
    }
  }

  const clearTimer = () => {
    if (timerRef.current != null) {
      clearInterval(timerRef.current)
      setCountDownSecond(60)
      timerRef.current = null
    }
  }

  const requestOtpAgain = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (countDownSecond >= 60) {
      if (phone === "" || selectPhoneCountryCode === "") {
        ApiService.showMessageBox(translate("errors.enterPhone"))
      } else {
        console.log("requestOtpAgain")
        setCode1("")
        setCode2("")
        setCode3("")
        setCode4("")
        setCode5("")
        setCode6("")
        setShowResultState(0)
        setErrorText("")
        // startCountdown()
        if (userName == "") {
          requestLogin(selectPhoneCountryCode, phone,
            () => {
              setShowResultState(1)
              startCountdown()
            },
            () => {
              startCountdown()
            },
            (error) => {
              ApiService.showMessageBox(error)
            }
          )
        } else {
          requestRegisterPortalUser(
            selectPhoneCountryCode,         
            phone, 
            (response) => {
              console.log("requestRegisterPortalUser: " + JSON.stringify(response))
              setShowResultState(1)
              startCountdown()     
            },
            (error) => {
              console.log("requestRegisterPortalUser error: ", error)
            }
          )
        }
      }
    }
  }

  useEffect(() => {
    prepareData()
    startCountdown()
    setTimeout(() => {
      if (tpRef1.current) {
        tpRef1.current.focus()
      }
    }, 1000)
    return () => {
      clearTimer()
    }
  }, [])

  const handleOTP = (text: string) => {
    if (text.length >= 6) {
      codeInput(text[0], 1)
      codeInput(text[1], 2)
      codeInput(text[2], 3)
      codeInput(text[3], 4)
      codeInput(text[4], 5)
      codeInput(text[5], 6)
      return true
    } else {
      return false
    }
  }

  useEffect(() => {
    countDownSecondRef.current = countDownSecond;
  }, [countDownSecond]);

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{ padding: Dimen.padding.base, height: "100%" }}
      KeyboardAvoidingViewProps={{ behavior: "padding" }}
      safeAreaEdges={["top", "bottom"]}
    >
      <MIcon 
        onPress={() => {_props.navigation.goBack()}}
        name="closeBlack" 
        size={Dimen.iconSize.md} />
      <Text 
        onPress={() => {
          // getMyAccountInfo()
        }}
        style={[FSIZE_24, BOLD, MARGIN_DF_TOP]} text={translate("boarding.enterVerifyCode")} />
      <View style={[ROW_CENTER, W_100P, MARGIN_S_DF_TOP]}>
        <Text style={FSIZE_15} text={translate("boarding.sentTo") + " " + selectPhoneCountryCode + phone} />
        <View style={FLEX}></View>
        <Text style={[TEXT_SMALL, MARGIN_S_DF_RIGHT, countDownSecond >= 60 ? INVISIBLE : VISIBLE]} text={countDownSecond + "s"} />
        <Text 
          onPress={() => {requestOtpAgain()}}
          style={[BTN_GRAY_CONTAINER, countDownSecond == 61 ? ENABLED : DISABLED, FSIZE_13, {backgroundColor: "#888888"}]}
          text={translate("common.resend")} />
      </View>
      {showResultState == 1 && errorText == "" && <Text style={[BTN_GREEN_CONTAINER, FSIZE_13, MARGIN_DF_TOP]} text={translate("boarding.newCodeSent")} /> }
      <View style={[ROW_CENTER, W_100P, MARGIN_DF_BOTTOM, MARGIN_DF_TOP]}>
        <TextInput 
          allowFontScaling={false}
          ref={tpRef1}
          value={code1}          
          keyboardType="number-pad"          
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 1)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 1)
            }
          }}
          onKeyPress={(e) => {
            console.log("e: " + e.nativeEvent.key)
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 1)
            }
          }}
          onFocus={() => handleFocus(1)}
          style={[TextInputOtp, currentFocus == 1 ? TextInputOtpCurrent : ""]} />
        <TextInput 
          allowFontScaling={false}
          ref={tpRef2}
          value={code2}
          keyboardType="number-pad"
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 2)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 2)
            }
          }}
          onKeyPress={(e) => {
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 2)
            }
          }}
          onFocus={() => handleFocus(2)}
          style={[TextInputOtp, currentFocus == 2 ? TextInputOtpCurrent : ""]} />
        <TextInput 
          allowFontScaling={false}
          ref={tpRef3}
          value={code3}
          keyboardType="number-pad"
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 3)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 3)
            }
          }}
          onKeyPress={(e) => {
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 3)
            }
          }}
          onFocus={() => handleFocus(3)}
          style={[TextInputOtp, currentFocus == 3 ? TextInputOtpCurrent : ""]} />
        <TextInput 
          allowFontScaling={false}
          ref={tpRef4}
          value={code4}
          keyboardType="number-pad"
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 4)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 4)
            }
          }}
          onKeyPress={(e) => {
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 4)
            }
          }}
          onFocus={() => handleFocus(4)}
          style={[TextInputOtp, currentFocus == 4 ? TextInputOtpCurrent : ""]} />
        <TextInput 
          allowFontScaling={false}
          ref={tpRef5}
          value={code5}
          keyboardType="number-pad"
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 5)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 5)
            }
          }}
          onKeyPress={(e) => {
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 5)
            }
          }}
          onFocus={() => handleFocus(5)}
          style={[TextInputOtp, currentFocus == 5 ? TextInputOtpCurrent : ""]} />
        <TextInput 
          allowFontScaling={false}
          ref={tpRef6}
          value={code6}
          keyboardType="number-pad"
          onChangeText={(text) => {
            console.log("text: " + text)
            const inputVal = text.replace(/[^0-9]/g, "")
            if (handleOTP(inputVal)) {
              return
            }
            if (inputVal.length == 1) {
              codeInput(inputVal, 6)
            } else if (inputVal.length > 0) {
              codeInput(inputVal[inputVal.length-1], 6)
            }
            Keyboard.dismiss();
          }}
          onKeyPress={(e) => {
            if (e.nativeEvent.key == "Backspace") {
              codeInput("", 6)
            }
          }}
          onFocus={() => handleFocus(6)}
          style={[TextInputOtp, currentFocus == 6 ? TextInputOtpCurrent : ""]} />
      </View>
      {errorText != "" && <Text style={[BTN_RED_CONTAINER, FSIZE_13, MARGIN_DF_TOP]} text={errorText} />}
      <View style={FLEX}></View>
      <Text 
        onPress={() => {verifyOtp()}}
        style={[BTN_CONTAINER, isSubmitEnable() ? ENABLED : DISABLED, FSIZE_17, {marginHorizontal: 0}]} 
        text={translate("common.submit")} />     
    </Screen>
  )
})
