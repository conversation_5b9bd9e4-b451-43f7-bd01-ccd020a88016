/**
 * The app navigator (formerly "AppNavigator" and "MainNavigator") is used for the primary
 * navigation flows of your app.
 * Generally speaking, it will contain an auth flow (registration, login, forgot password)
 * and a "main" flow which the user will use once logged in.
 */
import {
  DarkTheme,
  DefaultTheme,
  NavigationContainer,
} from "@react-navigation/native"
import { createNativeStackNavigator, NativeStackScreenProps } from "@react-navigation/native-stack"
import React from "react"
import { useColorScheme } from "react-native"
import * as Screens from "app/screens"
import Config from "../config"
import { navigationRef, useBackButtonHandler } from "./navigationUtilities"
import { colors } from "app/theme"
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { BottomMenu } from "./BottomMenu"
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs"
import { TopMenu } from "./TopMenu"
import { InformationTopMenu } from "./InformationTopMenu"
/**
 * This type allows TypeScript to know what routes are defined in this navigator
 * as well as what properties (if any) they might take when navigating to them.
 *
 * If no params are allowed, pass through `undefined`. Generally speaking, we
 * recommend using your MobX-State-Tree store(s) to keep application state
 * rather than passing state through navigation params.
 *
 * For more information, see this documentation:
 *   https://reactnavigation.org/docs/params/
 *   https://reactnavigation.org/docs/typescript#type-checking-the-navigator
 *   https://reactnavigation.org/docs/typescript/#organizing-types
 */
export type AppStackParamList = {
  Welcome: undefined
  // 🔥 Your screens go here
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
  Login: undefined
  BottomTab: undefined
  MainStack: undefined
  Home: undefined
  Trend: undefined
  Trade: undefined
  Information: undefined
  Account: undefined
  TodayTopics: undefined
  LatestNews: undefined
  LatestNotification: undefined
  NewsDetails: undefined
  InputOtp: undefined
  InputName: undefined
  Webview: undefined
  MarketNews: undefined
  NewPriceReminder: undefined
  PriceReminderHistory: undefined
  Calendar: undefined
  PersonalInformation: undefined
  BankInformation: undefined
  TransactionHistory: undefined
  ExchangeRate: undefined
  ChooseLanguage: undefined
  WebviewInformation: undefined
  MajorEvent: undefined
  MajorEventDetails: undefined
  SelectCountry: undefined
  RealAccountRegistration: undefined
  InvestRelatedVideo: undefined
  DeleteAccountConfirm: undefined
  Logout: undefined
  DemoAccountRegistration: undefined
  AllInformation: undefined
  InformationNotification: undefined
  InformationEvent: undefined
  InformationTransaction: undefined
  InformationSystem: undefined
  Splash: undefined
  SearchPost: undefined
}

/**
 * This is a list of all the route names that will exit the app if the back button
 * is pressed while in that screen. Only affects Android.
 */
const exitRoutes = Config.exitRoutes

export type AppStackScreenProps<T extends keyof AppStackParamList> = NativeStackScreenProps<
  AppStackParamList,
  T
>

// Documentation: https://reactnavigation.org/docs/stack-navigator/
const Stack = createNativeStackNavigator<AppStackParamList>()
const Tab = createBottomTabNavigator<AppStackParamList>()
const MaterialTab = createMaterialTopTabNavigator<AppStackParamList>();
const InformationMaterialTab = createMaterialTopTabNavigator<AppStackParamList>();

const TopTab = function () {
  return (
    <MaterialTab.Navigator
      screenOptions={{
        headerShown: false,
        navigationBarColor: colors.background,
        swipeEnabled: false,
        gestureEnabled: false,
        lazy: true,

      }}
      tabBar={(props) => <TopMenu {...props} />}
    >
      <MaterialTab.Screen name="MarketNews" component={Screens.MarketNewsScreen} />
      <MaterialTab.Screen name="Calendar" component={Screens.Calendarcreen} />
      <MaterialTab.Screen name="LatestNotification" component={Screens.LatestNotificationScreen} />
      <MaterialTab.Screen name="TodayTopics" component={Screens.TodayTopicsScreen} />
      <MaterialTab.Screen name="LatestNews" component={Screens.LatestNewsScreen} />
    </MaterialTab.Navigator>
  )
}

const InformationTopTab = function () {
  return (
    <InformationMaterialTab.Navigator
      screenOptions={{
        headerShown: false,
        swipeEnabled: false,
        gestureEnabled: false,
        lazy: true,
      }}
      // screenOptions={{headerShown: false}}
      tabBar={(props) => <InformationTopMenu {...props} />}
    >
      <InformationMaterialTab.Screen name="AllInformation" component={Screens.AllInformationScreen} />
      <InformationMaterialTab.Screen name="InformationNotification" component={Screens.InformationNotificationScreen} />
      <InformationMaterialTab.Screen name="InformationEvent" component={Screens.InformationEventScreen} />
      <InformationMaterialTab.Screen name="InformationTransaction" component={Screens.InformationTransactionScreen} />
      <InformationMaterialTab.Screen name="InformationSystem" component={Screens.InformationSystemScreen} />
      {/* <InformationMaterialTab.Screen name="LatestNews" component={Screens.LatestNewsScreen} />
        <InformationMaterialTab.Screen name="LatestNotification" component={Screens.LatestNotificationScreen} />
        <InformationMaterialTab.Screen name="MarketNews" component={Screens.MarketNewsScreen} />
        <InformationMaterialTab.Screen name="Calendar" component={Screens.Calendarcreen} /> */}
    </InformationMaterialTab.Navigator>
  )
}

const BottomTab = function () {
  return (
    <Tab.Navigator
      screenOptions={{ headerShown: false, lazy: true, swipeEnabled: false, gestureEnabled: false }}
      tabBar={(props) => <BottomMenu {...props} />}
    >
      <Tab.Screen name="MainStack" component={MainStack} />
    </Tab.Navigator>
  )
}

const MainStack = function () {
  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false, gestureEnabled: false }}
      initialRouteName={"Home"}>
      <Stack.Screen name="Home" component={Screens.HomeScreen} />
      <Stack.Screen name="Trend" component={TopTab} />
      <Stack.Screen name="Trade" component={Screens.TradeScreen} />
      <Stack.Screen name="Information" component={InformationTopTab} />
      <Stack.Screen name="NewsDetails" component={Screens.NewsDetailsScreen} />
    </Stack.Navigator>
  )
}

const AppStack = function AppStack() {
  return (
    <Stack.Navigator
      screenOptions={{ headerShown: false, navigationBarColor: colors.background, gestureEnabled: false }}
      initialRouteName={"Splash"}>
      <Stack.Screen name="Splash" component={Screens.SplashScreen} />
      <Stack.Screen name="BottomTab" component={BottomTab} />
      <Stack.Screen name="InputOtp" options={{gestureEnabled: false}} component={Screens.InputOtpScreen} />
      <Stack.Screen name="InputName" component={Screens.InputNameScreen} />
      <Stack.Screen name="Webview" component={Screens.WebviewScreen} />
      <Stack.Screen name="NewPriceReminder" component={Screens.NewPriceReminderScreen} />
      <Stack.Screen name="PriceReminderHistory" component={Screens.PriceReminderHistoryScreen} />
      <Stack.Screen name="PersonalInformation" component={Screens.PersonalInformationScreen} />
      <Stack.Screen name="BankInformation" component={Screens.BankInformationScreen} />
      <Stack.Screen name="TransactionHistory" component={Screens.TransactionHistoryScreen} />
      <Stack.Screen name="ExchangeRate" component={Screens.ExchangeRateScreen} />
      <Stack.Screen name="ChooseLanguage" component={Screens.ChooseLanguageScreen} />
      <Stack.Screen name="WebviewInformation" component={Screens.WebviewInformationScreen} />
      <Stack.Screen name="MajorEvent" component={Screens.MajorEventScreen} />
      <Stack.Screen name="MajorEventDetails" component={Screens.MajorEventDetailsScreen} />
      <Stack.Screen name="SelectCountry" component={Screens.SelectCountryScreen} />
      <Stack.Screen name="RealAccountRegistration" component={Screens.RealAccountRegistrationScreen} />
      <Stack.Screen name="InvestRelatedVideo" component={Screens.InvestRelatedVideoScreen} />
      <Stack.Screen name="DeleteAccountConfirm" component={Screens.DeleteAccountConfirmScreen} />
      <Stack.Screen name="Logout" component={Screens.LogoutScreen} />
      <Stack.Screen name="DemoAccountRegistration" component={Screens.DemoAccountRegistrationScreen} />
      <Stack.Screen name="SearchPost" component={Screens.SearchPostScreen} />
      <Stack.Screen name="Login" component={Screens.LoginScreen} />
      <Stack.Screen name="Account" component={Screens.AccountScreen} />
    </Stack.Navigator>
  )
}

export interface NavigationProps
  extends Partial<React.ComponentProps<typeof NavigationContainer>> { }

export const AppNavigator = function (props: NavigationProps) {
  const colorScheme = useColorScheme()
  useBackButtonHandler((routeName) => exitRoutes.includes(routeName))

  return (
    <NavigationContainer
      ref={navigationRef}
      theme={colorScheme === "dark" ? DarkTheme : DefaultTheme}
      {...props}>
      <AppStack />
    </NavigationContainer>
  )
}
