<manifest xmlns:android="http://schemas.android.com/apk/res/android" xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>
  <uses-permission android:name="com.google.android.gms.permission.AD_ID" tools:node="remove"/>

  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW"/>
      <data android:scheme="https"/>
    </intent>
  </queries>
  <application
      android:name=".MainApplication"
      android:configChanges="keyboard|keyboardHidden|orientation|screenSize|uiMode"
      android:label="@string/app_name"
      android:icon="@mipmap/prod"
      android:roundIcon="@mipmap/prod_round"
      android:allowBackup="false"
      android:theme="@style/BootTheme">
    <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:enabled="true"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:screenOrientation="portrait"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.MAIN"/>
       <!-- <category android:name="android.intent.category.LAUNCHER"/> -->
      </intent-filter>      
    </activity>
    <activity-alias
      android:name=".MainActivityuat"
      android:enabled="false"
      android:icon="@mipmap/uat"
      android:roundIcon="@mipmap/uat_round"
      android:targetActivity=".MainActivity" 
      android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="sinosounduat"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="sinosound"/>
      </intent-filter>
    </activity-alias>
    <activity-alias
        android:name=".MainActivityprod"
        android:enabled="true"
        android:icon="@mipmap/prod"
        android:roundIcon="@mipmap/prod_round"
        android:targetActivity=".MainActivity"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="sinosounduat"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW"/>
        <category android:name="android.intent.category.DEFAULT"/>
        <category android:name="android.intent.category.BROWSABLE"/>
        <data android:scheme="sinosound"/>
      </intent-filter>
    </activity-alias>

    <meta-data android:name="com.alibaba.app.appkey" android:value="334487989"/>
    <meta-data android:name="com.alibaba.app.appsecret" android:value="bf9dcb5631724a648f0cb7112a122079"/>
    <receiver android:name=".IconChangeReceiver"
        android:exported="true">
      <intent-filter>
        <action android:name=".CHANGE_ICON" />
      </intent-filter>
    </receiver>
    <receiver
        android:name=".MessageReceiver"
        android:exported="false">
      <intent-filter>
        <action android:name="com.alibaba.push2.action.NOTIFICATION_OPENED" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.alibaba.push2.action.NOTIFICATION_REMOVED" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.alibaba.sdk.android.push.RECEIVE" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.taobao.accs.intent.action.COMMAND" />
      </intent-filter>
      <intent-filter>
        <action android:name="com.taobao.taobao.intent.action.COMMAND" />
      </intent-filter>
      <intent-filter>
        <action android:name="org.agoo.android.intent.action.RECEIVE" />
      </intent-filter>
    </receiver>

  </application>
</manifest>
