/* eslint-disable import/first */
/**
 * Welcome to the main entry point of the app. In this file, we'll
 * be kicking off our app.
 *
 * Most of this file is boilerplate and you shouldn't need to modify
 * it very often. But take some time to look through and understand
 * what is going on here.
 *
 * The app navigation resides in ./app/navigators, so head over there
 * if you're interested in adding screens and navigators.
 */
if (__DEV__) {
  // Load Reactotron configuration in development. We don't want to
  // include this in our production bundle, so we are using `if (__DEV__)`
  // to only execute this in development.
  require("./devtools/ReactotronConfig.ts")
}
import "./utils/ignoreWarnings"
import { useFonts } from "expo-font"
import React from "react"
import { initialWindowMetrics, SafeAreaProvider } from "react-native-safe-area-context"
import * as Linking from "expo-linking"
import { useInitialRootStore } from "./models"
import { AppNavigator, useNavigationPersistence } from "./navigators"
import { ErrorBoundary } from "./screens/ErrorScreen/ErrorBoundary"
import * as storage from "./utils/storage"
import { colors, customFontsToLoad } from "./theme"
import { LoadingBox } from "./components/LoadingBox"
import { Modal } from "./components/Modals"
import { AppStorage } from "./utils/appStorage"
import { Observer } from "mobx-react-lite"
import KeepAwake from 'react-native-keep-awake';
import Toast from "react-native-toast-message"
import { AppLinkStorage } from "./utils/appLinkStorage"
import i18n from "i18n-js"
import { Dimensions, ImageStyle, ScrollView, View, ViewStyle, NativeModules } from "react-native"
import { COLUMN, JC_CENTER, MARGIN_DF_TOP, MARGIN_S_DF_LEFT, PADDING_S_DF, ROW_CENTER, TEXT_CENTER, W_30P } from "./theme/mStyle"
import { Dimen } from "./theme/dimen"
import { Text } from "./components"
import { BTN_CONTAINER, FSIZE_13, TEXT_SMALL } from "./theme/baseStyle"
import { AutoHeightImage } from "./components/AutoHeightImage"
import { MIcon } from "./components/MIcon"
import { Helper } from "./utils/helper"
import { Dialog } from "react-native-simple-dialogs"
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"

// Web linking configuration
const prefix = Linking.createURL("/")
const config = {
  screens: {
    MainStack: {
      screens: {
        Home: "home",
      },
    }
  },
}

interface AppProps {
  hideSplashScreen: () => Promise<void>
}

/**
 * This is the root component of our app.
 */
function App(props: AppProps) {
  const { hideSplashScreen } = props
  const {
    initialNavigationState,
    onNavigationStateChange,
    isRestored: isNavigationStateRestored,
  } = useNavigationPersistence(storage, NAVIGATION_PERSISTENCE_KEY)

  const [areFontsLoaded] = useFonts(customFontsToLoad)
  const { rehydrated } = useInitialRootStore(() => {
    // This runs after the root store has been initialized and rehydrated.

    // If your initialization scripts run very fast, it's good to show the splash screen for just a bit longer to prevent flicker.
    // Slightly delaying splash screen hiding for better UX; can be customized or removed as needed,
    // Note: (vanilla Android) The splash-screen will not appear if you launch your app via the terminal or Android Studio. Kill the app and launch it normally by tapping on the launcher icon. https://stackoverflow.com/a/69831106
    // Note: (vanilla iOS) You might notice the splash-screen logo change size. This happens in debug/development mode. Try building the app for release.
    setTimeout(hideSplashScreen, 100)
  })

  // Before we show the app, we have to wait for our state to be ready.
  // In the meantime, don't render anything. This will be the background
  // color set in native by rootView's background color.
  // In iOS: application:didFinishLaunchingWithOptions:
  // In Android: https://stackoverflow.com/a/45838109/204044
  // You can replace with your own loading component if you wish.
  if (!rehydrated || !isNavigationStateRestored || !areFontsLoaded) return null

  const linking = {
    prefixes: [prefix, "sinosounduat://", "https://sinosounduat.com/"],
    config,
    async getInitialURL() {
      // First, you would need to get the initial URL from your third-party integration
      // The exact usage depend on the third-party SDK you use
      // For example, to get the initial URL for Firebase Dynamic Links:
      
      // As a fallback, you may want to do the default deep link handling
      // deep link when the app is opened by the link
      const url = await Linking.getInitialURL();      
      console.log("getInitialURL " + url)
      const latestNotification = await NativeModules.AliyunPushExtensionModule.getLatestNotification();
      let notificationDeeplink;
      if (latestNotification) {
        try {
          const { extra } = latestNotification;
          notificationDeeplink = (extra ? JSON.parse(extra) : latestNotification).deeplink;
          if (notificationDeeplink) {
            NativeModules.AliyunPushExtensionModule.clearLatestNotification();
          }
        } catch (error) {
          console.error(error);
        }
      }
      handlingDeepLink(url || notificationDeeplink)
      return url;
    },
    subscribe(listener) {     
      // Listen to incoming links from deep linking, app is foreground or still alive
      const linkingSubscription = Linking.addEventListener('url', ({ url }) => {
        console.log("deeplink: " + url, AppLinkStorage.appLinkStorage.navigation)
        if (AppLinkStorage.appLinkStorage.navigation != null) {
          console.log("deeplink inside: " + url)
          AppLinkStorage.handleDeepLinkInsideApp(url, AppLinkStorage.appLinkStorage.navigation)
        } else {
          handlingDeepLink(url)
        }
        listener(url);
      });
  
      return () => {
        linkingSubscription.remove();
      };
    },
  }

  const handlingDeepLink = async (url: string) => {
    await AppLinkStorage.handleDeepLink(url)
  }

  const renderLoadingBox = () => {
    return (
      <LoadingBox visible={AppStorage.appStorage.showLoading} />
    )
  }

  const renderMessageBox = () => {
    return (
      <Dialog
        visible={AppStorage.appStorage.showMessage}
        dialogStyle={MessageDialogStyle}
        onTouchOutside={() => AppStorage.hideMessage()}>
        <View style={[PADDING_S_DF, COLUMN]}>
          <Text style={[TEXT_SMALL, TEXT_CENTER]} text={AppStorage.appStorage.globalMessage} />
          <View style={[ROW_CENTER, JC_CENTER, MARGIN_DF_TOP]}>
            <Text
              onPress={() => {
                AppStorage.hideMessage()
              }}
              style={[BTN_CONTAINER, MARGIN_S_DF_LEFT, W_30P]}
              tx="common.ok" />
          </View>
        </View>
      </Dialog>
    )
  }

  const getPopupImageUrl = (popupData) => {
    if (popupData != null && popupData.medium && popupData.medium.s3ObjKey && popupData.medium.s3ObjKey.Location) {
      return popupData.medium.s3ObjKey.Location
    } else {
      return ""
    }
  }

  const renderPopup = () => {
    if (!AppStorage.appStorage.popupDataIsShown || AppStorage.appStorage.popupData == null) {
      return <></>
    }
    return (
      <View style={BoxBackdrop}>
          <View style={BoxContainer}>
            <View style={BoxContent}>
              {                
                AppStorage.appStorage.popupData.imageOrText == 1 &&
                <ScrollView 
                  nestedScrollEnabled={true}                  
                  style={{maxHeight: 0.6 * Dimen.screenHeight}}
                  showsVerticalScrollIndicator={false}>
                  <View>
                    <Text style={FSIZE_13} text={Helper.getValue(AppStorage.appStorage.popupData, "content").trim()} />
                  </View>
                </ScrollView>
              }
              {
                AppStorage.appStorage.popupData.imageOrText == 0 && 
                <AutoHeightImage
                  source={{ uri: getPopupImageUrl(AppStorage.appStorage.popupData) }}
                  style={HomePopupImage}
                  imageWidth={Dimen.screenWidth - 4 * Dimen.padding.base - 20}
                  resizeMode="cover" />
              }
            </View>
            <MIcon
              onPress={() => {
                AppStorage.hidePopup()
              }}
              name="closeWhite"
              size={Dimen.iconSize.lg}
              style={MARGIN_DF_TOP} />
          </View>
        </View>
    )
  }
  

  const keepWake = () => {
    if (AppStorage.appStorage.setting.screenOn) {
      return (
        <KeepAwake />
      )
    } else {
      return (<></>)
    }
  }
  i18n.defaultLocale = AppStorage.appStorage.setting.language.toLowerCase() ? AppStorage.appStorage.setting.language.toLowerCase() : "sc"
  i18n.locale = AppStorage.appStorage.setting.language.toLowerCase() ? AppStorage.appStorage.setting.language.toLowerCase() : "sc"
  i18n.fallbacks = true;
  console.log("default language: " + AppStorage.appStorage.setting.language.toLowerCase())


  // otherwise, we're ready to render the app
  return (
    <SafeAreaProvider initialMetrics={initialWindowMetrics}>
      <ErrorBoundary catchErrors={"always"}>
        <AppNavigator          
          linking={linking}
          initialState={initialNavigationState}
          onStateChange={onNavigationStateChange}
        />
         <Observer>
            {renderLoadingBox}
        </Observer>
        <Observer>
            {renderPopup}
        </Observer>
        <Observer>
            {renderMessageBox}
        </Observer>
        <Observer>
            {keepWake}
        </Observer> 
        <Toast />
        <Modal />
      </ErrorBoundary>
    </SafeAreaProvider>
  )
}

const MessageDialogStyle: ViewStyle = {
  borderRadius: Dimen.borderRadiusLarge,
  backgroundColor: "white"
}

const BoxBackdrop: ViewStyle = {
  width: Dimen.screenWidth,
  height: Dimensions.get('screen').height,
  position: "absolute",
  top: 0,
  left: 0,
  zIndex: 999999,
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: colors.mine.modalBg,
}

const BoxContainer: ViewStyle = {
  flexDirection: "column",
  alignItems: "center",
  alignSelf: "center",
}

const BoxContent: ViewStyle = {
  width: Dimen.screenWidth - 4 * Dimen.padding.base,
  minHeight: 150,
  backgroundColor: colors.palette.white,
  borderRadius: 10,
  padding: 10
}

const HomePopupImage: ImageStyle = {
  width: "100%"
}

export default App
