import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "app/components/MIcon"
import { Dimen } from "app/theme/dimen"
import { CardNews } from "../_Home/CardNews"
import { CardMarketingNews } from "../_Home/CardMarketingNews"
import { AppStorage } from "app/utils/appStorage"
import { getPostById, searchPosts } from "app/api/model"
import { translate } from "app/i18n"
import { TextInput, View } from "react-native"
import { AI_CENTER, AS_CENTER, CENTER, COLUMN, FLEX, JC_CENTER, MARGIN_S_DF_LEFT, ROW, TEXT_CENTER, W_100P } from "app/theme/mStyle"
import { CONT_INPUT, CONT_INPUT_2, FSIZE_12, INVISIBLE, TEXT_CONTENT, VISIBLE } from "app/theme/baseStyle"
import { FlatList } from "react-native-gesture-handler"
import { BackNavComponent } from "app/components/BackNavComponent"
import { colors } from "app/theme"

interface SearchPostScreenProps extends AppStackScreenProps<"SearchPost"> {}

export const SearchPostScreen: FC<SearchPostScreenProps> = observer(function SearchPostScreen(_props) {
  const [postType, setPostType] = useState(0)
  const [posts, setPosts] = useState([])
  const [searchString, setSearchString] = useState("")
  
  const getPageTitle = (mPostType: number) => {
    switch (mPostType) {
      case 5:
        return translate("searchPage.todayNewsTitle")
      case 6:
        return translate("searchPage.latestNewsTitle")
      default:
        return ""
    }
  }

  const startSearch = (searchContent = "") => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    if (searchContent != "") {
      AppStorage.showLoading()
      searchPosts(
        searchContent,
        postType,
        (data) => {
          AppStorage.hideLoading()
          if (data.rows) {
            setPosts(JSON.parse(JSON.stringify(data.rows)))
          }
        },
        (error) => {
          AppStorage.hideLoading()
          setPosts([])
          console.log("error: " + error)
        }
      )
    } else {
      setPosts([])
    }
  }

  useEffect(() => {
    if (_props.route.params && _props.route.params.postType) {
      console.log("SearchPostScreen: " + _props.route.params.postType)
      setPostType(_props.route.params.postType)
    }    
  }, [_props.route])

  useEffect(() => {
    if (searchString != "") {
      startSearch(searchString)
    } else {
      setPosts([])
    }
  }, [searchString])

  return (
    <Screen
      preset="fixed"      
      contentContainerStyle={FLEX}
      safeAreaEdges={["top", "bottom"]}>
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        isTitleCenter={false}
        title={getPageTitle(postType)} /> 
      <View style={[CONT_INPUT_2, ROW, AI_CENTER, {marginTop: 0}]}>
        <TextInput 
          allowFontScaling={false}
          value={searchString}
          onChangeText={(text) => setSearchString(text)}
          style={[TEXT_CONTENT, FLEX, FSIZE_12, {lineHeight: 16}]}           
          placeholder={translate("searchPage.searchPlaceholder")}
          placeholderTextColor={colors.mine.placeHolderText}
        />
        <MIcon 
          onPress={() => setSearchString("")}
          containerStyle={[searchString != "" ? VISIBLE : INVISIBLE, MARGIN_S_DF_LEFT]}
          name="closeSolid" 
          size={Dimen.iconSize.base} />
      </View>
      {
        posts.length > 0 && 
        <FlatList 
          data={posts}                  
          nestedScrollEnabled={true}
          keyExtractor={item => item.id.toString()}
          extraData={posts}
          ListFooterComponent={<View style={{ height: 100 }} />}                    
          renderItem={({item}) => {
            return (
              <View style={{width: Dimen.screenWidth - 2*Dimen.padding.base}}>
                <CardNews
                  key={item.id}
                  onPress={() => {
                    if (!AppStorage.isNetworkConnected()) {
                      return
                    }
                    _props.navigation.navigate("NewsDetails", { postId: item.id })
                  }}
                  data={item} />
              </View>
            )
          }}
        />
      }
      {
        posts.length == 0 &&
        <View style={[W_100P, FLEX, COLUMN, JC_CENTER]}>
          <Text 
            text={translate("searchPage.noSearchFound")} 
            style={[TEXT_CONTENT, TEXT_CENTER, FSIZE_12]} />
        </View>
      }
    </Screen>
  )
})
