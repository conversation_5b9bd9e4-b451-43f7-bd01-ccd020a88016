import React, { useState, useEffect, useRef } from "react";
import { View, ViewStyle, AppState } from "react-native"
import ReconnectingWebSocket from 'reconnecting-websocket'
import { useIsFocused } from "@react-navigation/native"
import { CardPrice } from "./CardPrice"
import { translate } from "../../i18n"
import { Dimen } from "../../theme/dimen"
import { ROW, JC_SPACE_BETWEEN } from "../../theme/mStyle"
import { getCurrentPrice } from "../../api/model"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage";

const CardPriceContainer: ViewStyle = {
  ...ROW,
  ...JC_SPACE_BETWEEN,
  width: Dimen.screenWidth - 2 * Dimen.padding.base,
  marginHorizontal: Dimen.padding.base,
  marginTop: 0
}

export function CardPrices(_props: any) {
  const {containerStyle} = _props;
  const ws = useRef<ReconnectingWebSocket>();
  const isScreenFocus = useIsFocused()
  const [goldYtd, setGoldYtd] = useState(undefined);
  const [silverYtd, setSilverYtd] = useState(undefined);
  const [idxYtd, setIdxYtd] = useState(undefined);
  const [goldPrice, setGoldPrice] = useState(0);
  const [silverPrice, setSilverPrice] = useState(0);
  const [idxPrice, setIdxPrice] = useState(0);
  const lastPriceUpdate = useRef({
    GOLD: new Date(),
    SILVER: new Date(),
    USDINDEX: new Date(),
  });
  const pricesAssuranceInt = useRef<NodeJS.Timeout>();

  const startWs = () => {
    if (ws.current) {
      ws.current.close();
    }
    ws.current = new ReconnectingWebSocket(Helper.getAppConfig().WS_URL + '/sockets/price/update', [], {
      minReconnectionDelay: 1000,
      startClosed: true,
    });
    ws.current.reconnect();
    ws.current.addEventListener('message', e => {

      try {
        const { item, value } = JSON.parse(String(e.data));
        if (!['GOLD', 'SILVER', 'USDINDEX'].includes(item)) {
          return;
        }
        if (!lastPriceUpdate.current[item] || new Date().getTime() - lastPriceUpdate.current[item].getTime() < 300) {
          lastPriceUpdate.current[item] = new Date();
          return;
        }
        lastPriceUpdate.current[item] = new Date();

        let fn;
        if (item === 'GOLD') {
          fn = setGoldPrice;
        } else if (item === 'SILVER') {
          fn = setSilverPrice;
        } else if (item === 'USDINDEX') {
          fn = setIdxPrice;
        }

        if (fn) {
          fn(value);
        }
      } catch (error) {
        console.log(error);
      }
    });
  }

  const loadPrices = () => {
    getCurrentPrice(
      "gold",
      rsp => {
        setGoldYtd(rsp.data);
      },
      () => {
        // console.log("error")
        // AppStorage.showMessage(errorMsg)
      }
    )
    getCurrentPrice(
      "silver",
      rsp => {
        setSilverYtd(rsp.data);
      },
      () => {
        // console.log("error")
        // AppStorage.showMessage(errorMsg)
      }
    )
    getCurrentPrice(
      "usdindex",
      rsp => {
        setIdxYtd(rsp.data);
      },
      () => {
        // console.log("error")
        // AppStorage.showMessage(errorMsg)
      }
    )
  }

  const pricesAssuranceIntInit = () => {
    return setInterval(() => {
      if (!goldYtd || !silverYtd || !idxYtd) {
        loadPrices();
      } else {
        clearInterval(pricesAssuranceInt.current);
      }
    }, 2000);
  }

  useEffect(() => {
    startWs();
    loadPrices();
    pricesAssuranceInt.current = pricesAssuranceIntInit();

    const subscription = AppState.addEventListener("change", nextAppState => {
      ws.current.close();
      if (nextAppState === "active" && isScreenFocus) {
        loadPrices();
        startWs();
      }
    });

    return () => {
      subscription.remove();
      ws.current.close();
      clearInterval(pricesAssuranceInt.current);
    }
  }, [])

  useEffect(() => {
    clearInterval(pricesAssuranceInt.current);
    pricesAssuranceInt.current = pricesAssuranceIntInit();
    return () => {
      clearInterval(pricesAssuranceInt.current);
    }
  }, [goldYtd, silverYtd, idxYtd]);

  useEffect(() => {
    ws.current.close();
    if (isScreenFocus) {
      startWs();
    }
  }, [isScreenFocus])

  return <View style={[CardPriceContainer, containerStyle]}>
    <CardPrice symbol={translate("newPriceReminderScreen.londonGold")} ytd={goldYtd} price={goldPrice} />
    <CardPrice symbol={translate("newPriceReminderScreen.londonSilver")} ytd={silverYtd} price={silverPrice} />
    <CardPrice symbol={translate("newPriceReminderScreen.dollarIndex")} ytd={idxYtd} price={idxPrice} />
  </View>
}