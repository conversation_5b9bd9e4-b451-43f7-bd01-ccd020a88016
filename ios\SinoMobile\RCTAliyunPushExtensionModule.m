#import "RCTAliyunPushExtensionModule.h"
#import <React/RCTLog.h>

static NSDictionary *lastNotification;

@implementation RCTAliyunPushExtensionModule

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(getLatestNotification:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  resolve(lastNotification);
}

+ (void)setLatestNotification:(NSDictionary *)dict
{
  lastNotification = dict;
}

RCT_EXPORT_METHOD(clearLatestNotification:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  lastNotification = nil;
  resolve(nil);
}

@end
