import { translate } from 'app/i18n'
import moment from 'moment'
import { AppStorage } from './appStorage'
import { Api, ApiService } from 'app/api/api'
import { countryFulllist } from 'app/api/countryfulllist'
import { getCsUrl, getLinkSinoSound, getRegLinkSinoSound, getGraphLinkSinoSound, getClientLinkSinoSound } from 'app/api/model'
import { Linking } from 'react-native'
import DeviceInfo from 'react-native-device-info'
import { AppConfig } from 'app/config/appConfig'
import { LocaleConfig } from 'react-native-calendars'
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';

export const Helper = {
    getValue: function (jsonObj, key) {
        if (jsonObj) {
            const keyStr = AppStorage.appStorage.setting.language === "EN" ? key : key + AppStorage.appStorage.setting.language;

            if (jsonObj[keyStr] != undefined && jsonObj[keyStr] != null) {
                return jsonObj[keyStr]
            }
            if (jsonObj[key + "SC"] != undefined && jsonObj[key + "SC"] != null) {
                return jsonObj[key + "SC"]
            }
            if (jsonObj[key + "TC"] != undefined && jsonObj[key + "TC"] != null) {
                return jsonObj[key + "TC"]
            }
            if (jsonObj[key] != undefined && jsonObj[key] != null) {
                return jsonObj[key]
            }
            return ""
        } else {
            return ""
        }
    },
    getLangParam: function () {
        //console.log('AppStorage.appStorage.setting.language', AppStorage.appStorage.setting.language)

        if (AppStorage.appStorage.setting.language === "EN") {
            return "en"
        } else if (AppStorage.appStorage.setting.language === "TC") {
            return "zh"
        }
        return "cn"
    },
    dateFormateList: {
        dateApi: "YYYY-MM-DD",
    },
    updateMomentLocale: function (language) {
        if (language == "TC") {
            moment.updateLocale("zh-hk", {
                months: Helper.getMonthNames()
            });
        } else if (language == "SC") {
            moment.updateLocale("zh-cn", {
                months: Helper.getMonthNames()
            });
        } else {
            moment.updateLocale('en', {
                months: [
                    'January', 'February', 'March', 'April', 'May', 'June',
                    'July', 'August', 'September', 'October', 'November', 'December'
                ],
                monthsShort: [
                    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
                ],
                weekdays: [
                    'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
                ],
                weekdaysShort: [
                    'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
                ],
                weekdaysMin: [
                    'Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'
                ]
            });
            //moment.locale("en");
        }
    },
    updateCalendarLocale: function () {
        LocaleConfig.locales[AppStorage.appStorage.setting.language] = {
            monthNames: [
                translate("common.jan"),
                translate("common.feb"),
                translate("common.mar"),
                translate("common.apr"),
                translate("common.may"),
                translate("common.jun"),
                translate("common.jul"),
                translate("common.aug"),
                translate("common.sep"),
                translate("common.oct"),
                translate("common.nov"),
                translate("common.dec")
            ],
            monthNamesShort: [
                translate("common.janShort"),
                translate("common.febShort"),
                translate("common.marShort"),
                translate("common.aprShort"),
                translate("common.mayShort"),
                translate("common.junShort"),
                translate("common.julShort"),
                translate("common.augShort"),
                translate("common.sepShort"),
                translate("common.octShort"),
                translate("common.novShort"),
                translate("common.decShort")
            ],
            dayNames: [
                translate("common.sun"),
                translate("common.mon"),
                translate("common.tue"),
                translate("common.wed"),
                translate("common.thu"),
                translate("common.fri"),
                translate("common.sat")
            ],
            dayNamesShort: [
                translate("common.sun"),
                translate("common.mon"),
                translate("common.tue"),
                translate("common.wed"),
                translate("common.thu"),
                translate("common.fri"),
                translate("common.sat")
            ],
            today: translate("common.today")
        };
        LocaleConfig.defaultLocale = AppStorage.appStorage.setting.language;
    },
    getMonthNames: function () {
        return [
            'common.jan',
            'common.feb',
            'common.mar',
            'common.apr',
            'common.may',
            'common.jun',
            'common.jul',
            'common.aug',
            'common.sep',
            'common.oct',
            'common.nov',
            'common.dec'
        ]
    },
    getDayNames: function () {
        return [
            'common.sun',
            'common.mon',
            'common.tue',
            'common.wed',
            'common.thu',
            'common.fri',
            'common.sat'
        ]
    },
    isCurrentDate: function (date) {
        return moment(date).isSame(moment(), 'day')
    },
    formatDate: function (date, dateFormat = "YYYY-MM-DD HH:mm") {
        return moment(date).format(dateFormat)
    },
    getCurrentDate: function () {
        return moment().format("DD")
    },
    getCurrentMonth: function (dateString = "") {
        if (dateString == "")
            return moment().format("MMMM")
        return moment(dateString).format("MMMM")
    },
    getCurrentMonthShort: function (dateString = "") {
        moment.locale("zh-cn");
        if (dateString == "")
            return moment().format("M")
        return moment(dateString).format("M")
    },
    getCurrentMonthShortEng: function (dateString = "") {
        moment.updateLocale('en', {
            months: [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ],
            monthsShort: [
                'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
            ],
            weekdays: [
                'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
            ],
            weekdaysShort: [
                'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
            ],
            weekdaysMin: [
                'Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'
            ]
        });
        const month = dateString == "" ? moment().locale("en").format("MMM") : moment(dateString).locale("en").format("MMM");
        /* console.log('month:', month) */
        // Add dot only for specific months
        const monthsWithDot = ["Jan", "Feb", "Mar", "Apr", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        return monthsWithDot.includes(month) ? month + "." : month;
    },
    timeInPast: function (dateString) {
        return moment(dateString).startOf('day').isBefore(moment().startOf('day'))
    },
    getUserTypeName: function (type) {
        if (type == "Demo") {
            return translate("account.userTypeDemo")
        } else if (type == "Live") {
            return translate("account.userTypeLive")
        } else {
            return ""
        }
    },
    getUserFullPhone: function (userInfo) {
        if (userInfo && userInfo.areaCode && userInfo.mobile) {
            return "+" + userInfo.areaCode + userInfo.mobile
        } else {
            return ""
        }
    },
    getAccountStatus: function (demoAccountInfo) {
        if (demoAccountInfo?.data?.account_state == 1) {
            return translate("account.enabled")
        } else if (demoAccountInfo?.data?.account_state == 2) {
            return translate("account.disabled")
        } else {
            return ""
        }
    },
    formatNumberValue: function (value) {
        if (typeof (value) == "string") {
            return value.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        } else {
            const valueNo = parseFloat(value)
            return valueNo.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
    },
    removeHtmlTag: function (html) {
        const regex = /(<([^>]+)>)/ig;
        const result = html.replace(regex, '');
        return result
    },
    loadCsUrlAndGo: function (navigation: any) {
        AppStorage.showLoading()
        getCsUrl(
            (response) => {
                AppStorage.hideLoading()
                if (response && response.value) {
                    navigation.navigate("WebviewInformation", {
                        title: "",
                        webUrl: response.value,
                        showBackText: true
                    })
                }
            },
            (error) => {
                AppStorage.hideLoading()
                ApiService.showMessageBox(error)
            }
        )
    },

    loadWebViewLinkSinoSound: function (navigation: any, clientId: any, page: string) {
        AppStorage.showLoading()
        getLinkSinoSound(
            clientId,
            Helper.getLangParam(),
            Api.getUrl(), //Helper.getAppConfig().API_URL,
            page,
            (response) => {
                AppStorage.hideLoading()
                if (response && response.data) {
                    navigation.navigate("WebviewInformation", {
                        title: "",
                        webUrl: response.data.url
                    })
                }
            },
            (error) => {
                AppStorage.hideLoading()
                ApiService.showMessageBox(error)
            },
            () => {
                // Handle wrong token here if needed
                AppStorage.hideLoading()
                ApiService.showMessageBox(translate("errors.wrongToken"))
            }
        )
    },

    loadClientLinkSinoSound: function (navigation: any, area_code: string, phone: string) {
        AppStorage.showLoading()
        getClientLinkSinoSound(
            area_code,
            phone,
            Helper.getLangParam(),
            Api.getUrl(), //Helper.getAppConfig().API_URL,
            (response) => {
                AppStorage.hideLoading()
                if (response && response.data) {
                    navigation.navigate("WebviewInformation", {
                        title: "",
                        webUrl: response.data.url
                    })
                }
            },
            (error) => {
                AppStorage.hideLoading()
                ApiService.showMessageBox(error)
            },
            () => {
                // Handle wrong token here if needed
                AppStorage.hideLoading()
                ApiService.showMessageBox(translate("errors.wrongToken"))
            }
        )
    },
    loadRegLinkSinoSound: function (navigation: any, area_code: string, phone: string) {
        AppStorage.showLoading()
        getRegLinkSinoSound(
            area_code,
            phone,
            Helper.getLangParam(),
            Api.getUrl(), //Helper.getAppConfig().API_URL,
            (response) => {
                AppStorage.hideLoading()
                if (response && response.data) {
                    navigation.navigate("WebviewInformation", {
                        title: "",
                        webUrl: response.data.url
                    })
                }
            },
            (error) => {
                AppStorage.hideLoading()
                ApiService.showMessageBox(error)
            },
            () => {
                // Handle wrong token here if needed
                AppStorage.hideLoading()
                ApiService.showMessageBox(translate("errors.wrongToken"))
            }
        )
    },
    loadGraphLinkSinoSoundURL: function (color: any) {
        AppStorage.showLoading()
        getGraphLinkSinoSound(
            color,
            Helper.getLangParam(),
            Api.getUrl(), //Helper.getAppConfig().API_URL,
            (response) => {
                AppStorage.hideLoading()
                if (response && response.data) {
                    return response.data
                }
            },
            (error) => {
                AppStorage.hideLoading()
                ApiService.showMessageBox(error)
            }
        )
    },

    isLoggeIn: function () {
        if (AppStorage.appStorage.accessToken != null && AppStorage.appStorage.accessToken != undefined && AppStorage.appStorage.accessToken != "") {
            return true
        } else {
            return false
        }
    },
    isPortalUser: function (userInfo = AppStorage.appStorage.userDevice) {
        if (userInfo?.userType == "Portal User" && Helper.isLoggeIn()) {
            return true
        } else {
            return false
        }
    },
    isDemoUser: function (userInfo = AppStorage.appStorage.userDevice) {
        if (userInfo?.userType == "Demo" && Helper.isLoggeIn()) {
            return true
        } else {
            return false
        }
    },
    isLiveAccount: function (userInfo = AppStorage.appStorage.userDevice) {
        if (userInfo?.userType == "Live" && Helper.isLoggeIn()) {
            return true
        } else {
            return false
        }
    },
    isDisplayModeRed: function () {
        if (AppStorage.appStorage.setting.displayMode == Api.displayModes.red) {
            return true
        } else {
            return false
        }
    },
    formatNumberBalance(num) {
        if (num >= **********) {
            return (num / **********).toFixed(2).replace(/\.0$/, '') + 'B';
        }
        if (num >= 1000000) {
            return (num / 1000000).toFixed(2).replace(/\.0$/, '') + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(2).replace(/\.0$/, '') + 'K';
        }
        return Helper.formatNumberValue(num);
    },
    capitalizeFirstLetter: function (string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    },
    updateGraphLink: async () => {
        try {
            const graphUrl = await new Promise<string>((resolve, reject) => {
                getGraphLinkSinoSound(
                    Helper.isDisplayModeRed() ? '2' : '1',
                    Helper.getLangParam(),
                    Api.getUrl(),
                    (response) => {
                        if (response?.data) {
                            resolve(response.data);
                        } else {
                            reject(new Error("Invalid response format"));
                        }
                    },
                    (error) => {
                        reject(error);
                    }
                );
            });
            AppStorage.setGraphURL(graphUrl);
        } catch (error) {
            console.error('Error setting graph URL:', error);
            // fall back graph url
            AppStorage.setGraphURL(Api.getUrl() + "/56CF3B/plugin/?/app/price/index?color_style=2");
        }
    },
    getCompanyProfileUrl: function () {
        const getCompanyProfileUrl = Api.getWebPortalUrl() + "/" + AppStorage.appStorage.setting.language.toLowerCase() + "/introduction" + "?domain=" + Api.getUrl() + "/files/"
        console.log("getCompanyProfileUrl:", getCompanyProfileUrl)
        return getCompanyProfileUrl
    },
    getJournalUrl: function () {
        const journalUrl = Api.getWebPortalUrl() + "/" + AppStorage.appStorage.setting.language.toLowerCase() + "/goldoutlook" + "?domain=" + Api.getUrl() + "/files/"
        console.log("journalUrl:", journalUrl)
        return journalUrl
    },
    getCountryFlagUrl: function (countryName) {
        let flagUrl = ""
        if (countryName == "欧元区") {
            return "https://flagcdn.com/w80/eu.png"
        }
        if (countryName == "中国台湾") {
            return "https://www.gold2u.com/statics/sino/img/flag/%E4%B8%AD%E5%9B%BD%E5%8F%B0%E6%B9%BE.png"
        }
        countryFulllist.map((item) => {
            if (item.chinese == countryName) {
                flagUrl = "https://flagcdn.com/w80/" + item.abbr.toLowerCase() + ".png"
            }
        })
        return flagUrl
    },
    formatBase64ImageForApi: function (base64Image, fileName) {
        if (base64Image.includes("data:image/")) {
            return base64Image
        } else {
            const fileType = fileName.split('.')[fileName.split('.').length - 1]
            return "data:image/" + fileType + ";base64," + base64Image
        }
    },
    openLink: function (url) {
        console.log("openLink: " + url)
        if (url != null && url != "") {
            Linking.openURL(url)
        }
    },
    getAppConfig: () => {
        if (DeviceInfo.getBundleId() == AppConfig.uat.bundleId) {
            return AppConfig.uat
        } else {
            return AppConfig.prod
        }
    },
    isEmailString: function (email) {
        const re = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        return email.match(re)
    }
}