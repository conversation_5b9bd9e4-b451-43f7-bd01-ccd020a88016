// import { Text } from "app/components"
import { Text } from "../components"
// import { Dimen } from "app/theme/dimen"
// import { COLUMN, ROW } from "app/theme/mStyle"
import { Dimen } from "../theme/dimen"
import { COLUMN, ROW } from "../theme/mStyle"
import * as React from "react"
import { useEffect, useState } from "react"
import { TextStyle, TouchableOpacity, View, ScrollView, ViewStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
// import { colors } from "app/theme"
import { colors } from "../theme"
import { Mheader } from "./Mheader"
// import { translate } from "app/i18n"
import { translate } from "../i18n"
import { AppStorage } from "app/utils/appStorage"
import { FSIZE_15 } from "../theme/baseStyle"
import { observer } from "mobx-react-lite"


const MenuMainContent: ViewStyle = {
    ...ROW,
    paddingHorizontal: Dimen.padding.base,
    justifyContent: 'space-between',
    backgroundColor: 'white',
    paddingBottom: 10
}

const MenuItem: ViewStyle = {
    ...COLUMN,
    alignItems: 'center',
    justifyContent: 'center',
}

const MenuItemEN: ViewStyle = {
    ...MenuItem,
    paddingRight: 15
}

const CurrentLine: ViewStyle = {
    width: 30,
    height: 5,
    marginTop: 5,
    borderRadius: 50,
    backgroundColor: colors.mine.primary,
}
const HiddenLine: ViewStyle = {
    width: 30,
    height: 5,
    marginTop: 5,
    borderRadius: 50,
    backgroundColor: colors.palette.white,
}

const MenuText: TextStyle = {
    textAlign: 'center',
}

export const InformationTopMenu = observer(function InformationTopMenu(props: any) {
    const insets = useSafeAreaInsets()

    const switchTab = (index: number) => {
        AppStorage.setCurrentInformationScreenIndex(index)
        if (index === 1) {
            props.navigation.navigate("AllInformation")
        } else if (index === 2) {
            props.navigation.navigate("InformationNotification")
        } else if (index === 3) {
            props.navigation.navigate("InformationEvent")
        } else if (index === 4) {
            props.navigation.navigate("InformationTransaction")
            // AppStorage.setScreenNameNavigateAuto("")
        } else if (index === 5) {
            props.navigation.navigate("InformationSystem")
            // AppStorage.setScreenNameNavigateAuto("")
        }
    }


    let menuItems = (
        <>
            <TouchableOpacity
                onPress={() => switchTab(1)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="information.all" />
                <View style={AppStorage.appStorage.currentInformationScreenIndex == 1 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(2)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="information.notification" />
                <View style={AppStorage.appStorage.currentInformationScreenIndex == 2 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(3)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="information.eventNotificaiont" />
                <View style={AppStorage.appStorage.currentInformationScreenIndex == 3 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(4)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="information.transactionNotice" />
                <View style={AppStorage.appStorage.currentInformationScreenIndex == 4 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(5)}
                style={MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="information.systemNotification" />
                <View style={AppStorage.appStorage.currentInformationScreenIndex == 5 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
        </>
    );
    if (AppStorage.appStorage.setting.language == 'EN') {
        menuItems = (<>
            <ScrollView horizontal={true}>
                {menuItems}
            </ScrollView>
        </>
        )
    }

    return (
        <View style={COLUMN}>
            <Mheader
                containerStyle={{ paddingTop: insets.top }}
                leftTitle={translate("information.title")}
                showSearchIcon={false}
                navigator={props.navigation} />
            <View style={MenuMainContent} >
                {menuItems}
            </View>
        </View>
    )
})


