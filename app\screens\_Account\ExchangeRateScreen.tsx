import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { TEXT_SCREEN_TITLE, TEXT_SMALL_0} from "../../theme/baseStyle"
import { COLUMN, FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_DF_TOP, MARGIN_S_DF_BOTTOM, ROW_CENTER, SHADOW, W_100P } from "../../theme/mStyle"
import { AppStorage } from "app/utils/appStorage"
import { getExchangeRate } from "app/api/model"
import { colors } from "app/theme"
import { TextStyle, View, ViewStyle } from "react-native"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

const CardRateContainer: ViewStyle = {
  ...SHADOW,
  ...ROW_CENTER,
  width: Dimen.screenWidth - 2*Dimen.padding.base,
  marginHorizontal: Dimen.padding.base,
  marginTop: Dimen.padding.base,
  minHeight: Dimen.screenWidth * 0.2,
  padding: Dimen.padding.base,
  backgroundColor: 'white',
  borderRadius: Dimen.borderRadius,
}

const TextExchangeTypeGreen: TextStyle = {
  width: 30,
  height: 30,
  lineHeight: 30,
  borderRadius: 15,
  backgroundColor: "#13AA8F",
  color: 'white',
  textAlign: 'center',
  textAlignVertical: "center",
  marginHorizontal: Dimen.padding.sm,
  ...TEXT_SMALL_0,
  overflow: 'hidden',
}

const TextExchangeTypeRed: TextStyle = {
  width: 30,
  height: 30,
  lineHeight: 30,
  borderRadius: 15,
  backgroundColor: "#ED6B75",
  color: 'white',
  textAlign: 'center',
  textAlignVertical: "center",
  marginHorizontal: Dimen.padding.sm,
  ...TEXT_SMALL_0,
  overflow: 'hidden',
}

interface ExchangeRateScreenProps extends AppStackScreenProps<"ExchangeRate"> {}
export const ExchangeRateScreen: FC<ExchangeRateScreenProps> = observer(function ExchangeRateScreen(_props) {
  
  const [data, setData] = useState(null)

  const prepareData = () => {
    AppStorage.showLoading()
    getExchangeRate(      
      (res) => {
        AppStorage.hideLoading()
        setData(res.data)
      },
      (err) => {
        AppStorage.hideLoading()
        console.log("Error: " + err)
      }
    )
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    // console.log("- " + JSON.stringify(AppStorage.appStorage.userOtherInfo))
    prepareData()
  }, [])

  return (
    <Screen
      preset="fixed"
      contentContainerStyle={{backgroundColor: colors.mine.bgGrey, height: Dimen.screenHeight }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        isTitleCenter={false}
        title={translate("account.todayExchangeRate")} />    
      <View style={CardRateContainer}>          
          <MIcon name="flagUsa" size={Dimen.iconSize.xxl} />
          <View style={[FLEX, COLUMN, MARGIN_DF_LEFT, MARGIN_DF_RIGHT]}>
              <View style={[ROW_CENTER, MARGIN_S_DF_BOTTOM]}>
                  <Text style={TEXT_SMALL_0}  text="USD 1" />
                  <Text style={TextExchangeTypeGreen} text={translate("account.exchangeRateDeposite")} />
                  <Text style={TEXT_SMALL_0}  text={"RMB " + (data?.rate_sell ? data?.rate_sell : "")} />
              </View>
              <View style={ROW_CENTER}>
                  <Text style={TEXT_SMALL_0}  text="USD 1" />
                  <Text style={TextExchangeTypeRed} text={translate("account.exchangeRatePick")} />
                  <Text style={TEXT_SMALL_0}  text={"RMB " +(data?.rate_buy ? data?.rate_buy : "")} />
              </View>
          </View>          
          <MIcon name="flagChina" size={Dimen.iconSize.xxl} />
      </View>
      <View style={CardRateContainer}>
          <MIcon name="flagUsa" size={Dimen.iconSize.xxl} />
          <View style={[FLEX, COLUMN, MARGIN_DF_LEFT, MARGIN_DF_RIGHT]}>
              <View style={[ROW_CENTER, MARGIN_S_DF_BOTTOM]}>
                  <Text style={TEXT_SMALL_0}  text="USD 1" />
                  <Text style={TextExchangeTypeGreen} text={"="} />
                  <Text style={TEXT_SMALL_0}  text={"HKD "+ (data?.usdhkd ? data?.usdhkd : "")} />
              </View>              
          </View>
          <MIcon name="flagHongKong" size={Dimen.iconSize.xxl} />
      </View>
    </Screen>
  )
})