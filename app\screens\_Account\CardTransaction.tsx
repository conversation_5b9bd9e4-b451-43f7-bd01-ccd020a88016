import { COLOR_88, F<PERSON>ZE_12, FSIZE_13, FSIZE_15, ROW_INFO, TEXT_CONTENT, TEXT_SECTION, TEXT_SMALL_0 } from "app/theme/baseStyle"
import { Text } from "../../components"
import { Dimen } from "../../theme/dimen"
import { AI_START, COLUMN, FLEX, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_S_DF_TOP, ROW, W_100P } from "../../theme/mStyle"
import * as React from "react"
import { TextStyle, View } from "react-native"
import { MIcon } from "app/components/MIcon"
import { translate } from "app/i18n"
import { colors } from "app/theme"

const TransactionStatusFinish: TextStyle = {
    ...FSIZE_13,
    backgroundColor: "#13AA8F",
    color: colors.mine.white,
    paddingVertical: 1,
    paddingHorizontal: 12,
    alignSelf: "flex-start",
    borderRadius: Dimen.borderRadiusLarge,
    overflow: "hidden",
}

const TransactionStatusProcess: TextStyle = {
    ...FSIZE_13,
    backgroundColor: colors.mine.primary,
    color: colors.mine.white,
    paddingVertical: 1,
    paddingHorizontal: 12,
    alignSelf: "flex-start",
    borderRadius: Dimen.borderRadiusLarge,
    overflow: "hidden",
}

const TransactionStatusCancel: TextStyle = {
    ...FSIZE_13,
    backgroundColor: "#EC8181",
    color: colors.mine.white,
    paddingVertical: 1,
    paddingHorizontal: 12,
    alignSelf: "flex-start",
    borderRadius: Dimen.borderRadiusLarge,
    overflow: "hidden",
}

export function CardTransaction (props: any) {
    const {
        containerStyle = {},
        data = null,
        type = 1
    } = props
    const statuses = [translate("account.transactionProcessing"), translate("account.transactionFinish"), translate("account.transactionCancel")]

    const getStatusStyle = (status) => {
        switch (status) {
            case 0:
                return TransactionStatusProcess
            case 1:
                return TransactionStatusFinish
            case 2:
                return TransactionStatusCancel
            default:
                return TransactionStatusProcess
        }
    }

    return (
        <View style={[ROW_INFO, AI_START, containerStyle]}>
           <MIcon name={data.ticket_type == 1 ? "deposit" : "wthdrawal"} size={Dimen.iconSize.xl} containerStyle={[MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_S_DF_TOP]} />
           <View style={[COLUMN, FLEX]}>
                <View style={[ROW, W_100P]}>
                    <View style={[COLUMN,FLEX]}>                    
                        {data.ticket_type == 1 && <Text style={[FSIZE_15, {marginTop: 2}]} text={translate("account.transactionDeposite") + data.ticket_number} />}
                        {data.ticket_type == 2 && <Text style={[FSIZE_15, {marginTop: 2}]} text={translate("account.transactionWithdraw") + data.ticket_number} />}
                        <Text style={[FSIZE_12, COLOR_88, {marginTop: -3}]} text={data.ticket_time} />
                    </View>
                    <Text style={getStatusStyle(data.ticket_state)} text={statuses[data.ticket_state]}/>
                </View>
                <View style={[ROW, W_100P, MARGIN_S_DF_TOP]}>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_13, COLOR_88]} tx="account.transAmount" />
                        <Text style={[FSIZE_13, COLOR_88]} text={data.application_amount ? data.application_amount :  "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_13, COLOR_88]} tx="account.handleFee" />
                        <Text style={[FSIZE_13, COLOR_88]} text={data.handling_fee_amount ? data.handling_fee_amount :  "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_13, COLOR_88]} tx="account.currency" />
                        <Text style={[FSIZE_13, COLOR_88]} text={data.currency_type_name ? data.currency_type_name :  "-"} />
                    </View>
                    <View style={[FLEX, COLUMN]}>
                        <Text style={[FSIZE_13, COLOR_88]} tx="account.exchangeRate" />
                        <Text style={[FSIZE_13, COLOR_88]} text={data.exchange_rate ? data.exchange_rate :  "-"} />
                    </View>
                </View>
           </View>
        </View>
    )
}