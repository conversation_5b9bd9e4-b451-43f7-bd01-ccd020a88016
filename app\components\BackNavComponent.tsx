import React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native";
import { Text } from "./Text";
import { TEXT_SCREEN_TITLE } from "app/theme/baseStyle";
import { colors } from "app/theme";
import { AI_CENTER, FLEX, MARGIN_S_DF_LEFT, ROW, TEXT_CENTER } from "app/theme/mStyle";
import { MIcon } from "./MIcon";
import { Dimen } from "app/theme/dimen";
import { translate } from "app/i18n";


const baseContainer: ViewStyle = {
    ...ROW,
    ...AI_CENTER,
}

const hasPaddingStyle: ViewStyle = {
    padding: Dimen.padding.base
}
const iconWrapper: ViewStyle = {
    /* backgroundColor: colors.mine.red, */
    paddingHorizontal: Dimen.padding.ssm,
    marginHorizontal: -(Dimen.padding.ssm),
    paddingVertical: Dimen.padding.ssm,
    marginVertical: -(Dimen.padding.ssm)
}

export function BackNavComponent(props: any) {
    const {
        onBackButtonPressed,
        hasPadding = true,
        title = "",
        isTitleCenter = true,
        containerStyle = "",
        showBackText = false,
    } = props

    const btnBackPressed = () => {
        if (onBackButtonPressed) {
            onBackButtonPressed()
        }
    }

    return (
        <View style={[baseContainer, hasPadding ? hasPaddingStyle : "", containerStyle]}>

            <TouchableOpacity style={[iconWrapper, ROW, AI_CENTER]}
                onPress={() => { btnBackPressed() }}>
                <MIcon
                    name="back"
                    size={Dimen.iconSize.base}
                />
                {showBackText && <Text style={[TEXT_SCREEN_TITLE, MARGIN_S_DF_LEFT]} text={translate("common.back")} />}
            </TouchableOpacity>
            {
                title !== "" && <Text style={[TEXT_SCREEN_TITLE, isTitleCenter ? TEXT_CENTER : MARGIN_S_DF_LEFT, FLEX]} text={title} />
            }
            {isTitleCenter && title !== "" && <View style={{ width: Dimen.iconSize.base }}>

            </View>}
        </View>
    )
}