import { observer } from "mobx-react-lite"
import React, { FC } from "react"

import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { <PERSON>header } from "../../navigators/Mheader"

interface TrendScreenProps extends AppStackScreenProps<"Trend"> {}

export const TrendScreen: FC<TrendScreenProps> = observer(function TrendScreen(_props) {
  
  


  return (
    <Screen
      preset="auto"
      safeAreaEdges={["top", "bottom"]}>
      <Mheader navigator={_props.navigation}/>
    </Screen>
  )
})
