import { action, makeObservable, observable, runInAction } from "mobx"
import { load, remove, save } from "./storage"
import { storedUserList } from "../api/userlist"
import { translate } from "app/i18n"
import { Helper } from "./helper"
import { Api } from "../api/api"

interface SettingProps {
  screenOn: boolean,
  emailSubscription: boolean,
  displayMode: string,
  language: string,
  clearCache: boolean,
}

interface IAppStorage {
  accessToken: string,
  userId: number,
  refreshToken: string,
  userDevice: any,
  userOtherInfo: any,
  setting: SettingProps,
  showLoading: boolean,
  showMessage: boolean,
  storedUsers: any,
  screenNameNavigateAuto: string,
  currentTrendScreenIndex: number,
  currentInformationScreenIndex: number,
  inboxBatchCount: number,
  popupData: any,
  popupDataIsShown: boolean,
  popupShownCount: number,
  iconChanged: boolean,
  notificationPromptShown: boolean,
  currentScreen: string,
  showNotificationPrompt: boolean,
  pIp: string,
  networkStatus: boolean,
  globalMessage: string,
  secureDomains: string[],
  serviceDomains: string[],
  graphURL: string,
  awaitingUserRetry: boolean // Flag to prevent infinite "no internet" message boxes
}

class AppStorageImpl {
  appStorage: IAppStorage = {
    accessToken: "",
    refreshToken: "",
    userId: 0,
    userDevice: null,
    userOtherInfo: null,
    showLoading: false,
    showMessage: false,
    screenNameNavigateAuto: "",
    currentTrendScreenIndex: 1,
    currentInformationScreenIndex: 1,
    setting: {
      screenOn: false,
      emailSubscription: false,
      displayMode: "Red",
      language: "SC",
      clearCache: true,
    },
    storedUsers: storedUserList,
    inboxBatchCount: 0,
    popupData: null,
    popupDataIsShown: false,
    iconChanged: false,
    notificationPromptShown: false,
    currentScreen: "",
    popupShownCount: 0,
    showNotificationPrompt: false,
    pIp: "",
    networkStatus: true,
    globalMessage: "",
    secureDomains: [],
    serviceDomains: [],
    graphURL: "",
    awaitingUserRetry: false
  }

  constructor() {
    makeObservable(this, {
      appStorage: observable,
      setAccessToken: action,
      setRefreshToken: action,
      setUserDevice: action,
      setUserOtherInfo: action,
      updateSetting: action,
      updateUserId: action,
      showLoading: action,
      showMessage: action,
      isLoadingShow: action,
      hideLoading: action,
      hidePreload: action,
      switchUser: action,
      switchUserByName: action,
      switchUserById: action,
      addCreatedLoggedInUser: action,
      setCurrentTrendScreenIndex: action,
      setCurrentInformationScreenIndex: action,
      setInboxBatchCount: action,
      setPopupDataAndShow: action,
      hidePopup: action,
      setIconChanged: action,
      isIconChanged: action,
      setNotificationPromptShown: action,
      setCurrentScreen: action,
      updatePopupShownCount: action,
      resetPopupShownCount: action,
      updateNetworkStatus: action,
      updateIp: action,
      getPublicIp: action,
      setSecureDomains: action,
      setServiceDomains: action,
      getSecureDomains: action,
      getServiceDomains: action,
      getGraphURL: action,
      setGraphURL: action,
      setShowNotificationPrompt: action,
      setShowMessage: action,
      isAwaitingUserRetry: action,
      setAwaitingUserRetry: action,
    })
  }

  isAwaitingUserRetry(): boolean {
    return this.appStorage.awaitingUserRetry;
  }

  setAwaitingUserRetry(isAwaiting: boolean) {
    console.log("setAwaitingUserRetry: " + isAwaiting);
    this.appStorage.awaitingUserRetry = isAwaiting;
    this.saveToStorage();
  }

  updateIp(ip: string) {
    console.log("updateIp: " + ip)
    this.appStorage.pIp = ip
  }

  getPublicIp() {
    //return "0.0.0.0"
    return this.appStorage.pIp
  }

  setServiceDomains(domains: string[]) {
    console.log('Updating service domains:');
    console.log('Old domains:', this.appStorage.serviceDomains);
    console.log('New domains:', domains);
    this.appStorage.serviceDomains = domains;
    this.saveToStorage();
  }

  setSecureDomains(domains: string[]) {
    console.log('Updating secure domains:');
    console.log('Old domains:', this.appStorage.secureDomains);
    console.log('New domains:', domains);
    this.appStorage.secureDomains = domains;
    this.saveToStorage();
  }

  getSecureDomains(): string[] {
    return this.appStorage.secureDomains || [];
  }

  getServiceDomains(): string[] {
    return this.appStorage.serviceDomains || []
  }

  getGraphURL(): string {
    return this.appStorage.graphURL || ""
  }

  setGraphURL(graphURL: string) {
    console.log("stored graphURL: " + graphURL)
    this.appStorage.graphURL = graphURL
  }

  updateNetworkStatus(status: boolean) {
    if (status != null) {
      this.appStorage.networkStatus = status
    }
    if (status) {
      this.hideMessage()
    }
  }

  isNetworkConnected() {
    if (!this.appStorage.networkStatus) {
      this.showMessage()
    }
    return this.appStorage.networkStatus
  }

  resetPopupShownCount() {
    this.appStorage.popupShownCount = 0
  }


  updatePopupShownCount() {
    this.appStorage.popupShownCount += 1
  }

  setCurrentScreen(screen: string) {
    this.appStorage.currentScreen = screen
  }

  setShowNotificationPrompt(show: boolean) {
    this.appStorage.showNotificationPrompt = show;
  }

  setNotificationPromptShown(prompt: boolean) {
    this.appStorage.notificationPromptShown = prompt;
  }

  isIconChanged() {
    if (this.appStorage.iconChanged) {
      return this.appStorage.iconChanged
    } else {
      return false;
    }
  }

  setIconChanged(iconChanged: boolean) {
    this.appStorage.iconChanged = iconChanged
    this.saveToStorage()
  }

  setPopupDataAndShow(data: any) {
    this.appStorage.popupDataIsShown = true
    this.appStorage.popupData = data
  }

  hidePopup() {
    this.appStorage.popupDataIsShown = false
    this.appStorage.popupData = null
  }

  setInboxBatchCount(count: number) {
    this.appStorage.inboxBatchCount = count
  }

  setCurrentTrendScreenIndex(index: number) {
    this.appStorage.currentTrendScreenIndex = index
  }

  setCurrentInformationScreenIndex(index: number) {
    this.appStorage.currentInformationScreenIndex = index
  }

  setScreenNameNavigateAuto(screenName: string) {
    this.appStorage.screenNameNavigateAuto = screenName
  }

  getScreenNameNavigateAuto() {
    return this.appStorage.screenNameNavigateAuto
  }

  async addLoggedInUser(userDevice) {
    let canAdd = true
    this.appStorage.storedUsers.map((user: any) => {
      if (user.id == userDevice.id) {
        canAdd = false
      }
    })
    console.log("canAdd " + canAdd)
    if (canAdd) {
      runInAction(() => {
        this.appStorage.storedUsers.unshift(userDevice)
      })
      await this.saveToStorage()
    }
  }

  async addCreatedLoggedInUser(userDevice) {
    let userIdAdded = false
    runInAction(() => {
      this.appStorage.storedUsers.forEach((user: any, index: number) => {
        if (user.id == userDevice.id) {
          userIdAdded = true
          this.appStorage.storedUsers[index] = userDevice
          this.appStorage.storedUsers[index].token = ""
          this.appStorage.storedUsers[index].type = "created"
        }
      })
      if (!userIdAdded) {
        this.appStorage.storedUsers.unshift(userDevice)
        this.appStorage.storedUsers[0].token = ""
        this.appStorage.storedUsers[0].type = "created"
      }
    })
    await this.saveToStorage()
  }

  async switchUser(accessToken: string, userId: number, callback: () => void) {
    this.appStorage.accessToken = accessToken
    this.appStorage.userId = userId
    await this.saveToStorage()
    callback()
  }

  async switchUserByName(userName: string, callback: () => void) {
    const user = this.appStorage.storedUsers.find((user: any) => user.name == userName)
    if (user) {
      this.appStorage.accessToken = user.token
      this.appStorage.userId = user.id
      await this.saveToStorage()
      callback()
    }
  }

  async switchUserById(userId: number, callback: () => void) {
    const user = storedUserList.find((user: any) => user.id == userId)
    if (user) {
      this.appStorage.storedUsers = storedUserList
      if (user) {
        console.log("here 1 " + JSON.stringify(user))
        this.appStorage.accessToken = user.token
        this.appStorage.userId = user.id
        await this.saveToStorage()
        // callback()
      }
    } else {
      console.log("here 2")
      const checkUser = this.appStorage.storedUsers.find((user: any) => user.id == userId)
      if (checkUser) {
        this.appStorage.accessToken = checkUser.token
        this.appStorage.userId = checkUser.id
        await this.saveToStorage()
        // callback()
      }
    }
    callback()
  }

  showLoading() {
    this.appStorage.showLoading = true
  }

  setShowMessage(show: boolean, message: string = "") {
    if (message) {
      this.appStorage.globalMessage = message
    }
    this.appStorage.showMessage = show
  }

  showMessage(message: string = translate("errors.noInternet")) {
    console.log("showMessage " + message)
    this.setShowMessage(true, message)
  }

  hideMessage() {
    this.setShowMessage(false)
  }

  hidePreload(tabIndex = -1) {
    if (this.appStorage.showLoading) {
      this.appStorage.showLoading = false
    }
    console.log("hidePreload tabIndex: " + tabIndex)
    // if (tabIndex > -1) {
    //   this.appStorage.currentTrendScreenIndex = tabIndex
    // }
  }

  isLoadingShow() {
    return this.appStorage.showLoading
  }

  hideLoading() {
    this.appStorage.showLoading = false
  }

  async updateUserId(userId: number) {
    this.appStorage.userDevice.id = userId
    this.appStorage.userId = userId
    await this.saveToStorage()
  }

  async updateSettingFromUserInfo(userSetting) {
    runInAction(() => {
      this.appStorage.setting.language = userSetting.language
      this.appStorage.setting.displayMode = Helper.capitalizeFirstLetter(userSetting.redGreen)
    })
    console.log("updateSettingFromUserInfo " + this.appStorage.setting.displayMode)
    await this.saveToStorage()
  }

  async updateSetting(setting: SettingProps) {
    this.appStorage.setting = setting
    await this.saveToStorage()
  }

  async updateScreenOn(screenOn: boolean) {
    this.appStorage.setting.screenOn = screenOn
    await this.saveToStorage()
  }

  async updateEmailSubscription(emailSubscription: boolean) {
    this.appStorage.setting.emailSubscription = emailSubscription
    await this.saveToStorage()
  }

  async updateDisplayMode(displayMode: string) {
    runInAction(() => {
      this.appStorage.setting.displayMode = displayMode
    })
    await this.saveToStorage()
  }

  async updateLanguage(language: string) {
    this.appStorage.setting.language = language
    await this.saveToStorage()
  }

  getLanguageName() {
    if (this.appStorage.setting.language == "SC") {
      return translate("account.langSimplifiedChinese")
    } else if (this.appStorage.setting.language == "TC") {
      return translate("account.langTraditionChinese")
    } else if (this.appStorage.setting.language == "EN") {
      return translate("account.langEnglish")
    } else {
      return translate("account.langSimplifiedChinese")
    }
  }

  async updateClearCache(clearCache: boolean) {
    this.appStorage.setting.clearCache = clearCache
    await this.saveToStorage()
  }

  async setUserDevice(userDevice: any) {
    this.appStorage.userDevice = userDevice
    await this.saveToStorage()
  }

  async setUserOtherInfo(userOtherInfo: any) {
    this.appStorage.userOtherInfo = userOtherInfo
    await this.saveToStorage()
  }

  async setAccessToken(token: string) {
    this.appStorage.accessToken = token
    await this.saveToStorage()
  }

  async setRefreshToken(token: string) {
    this.appStorage.refreshToken = token
    await this.saveToStorage()
  }

  async prepareForStart(callback: any) {
    this.initializeFromAsyncStorage().then(() => {
      callback()
    })
  }

  async initializeFromAsyncStorage() {
    const result = await load("appStorage")
    if (result && typeof result === 'object') {
      const loadedData = result as IAppStorage
      runInAction(() => {
        this.appStorage = {
          ...this.appStorage, 
          ...loadedData,
          setting: {
            ...this.appStorage.setting, 
            ...(loadedData.setting || {})
          },
          currentTrendScreenIndex: 1, 
          secureDomains: loadedData.secureDomains || this.appStorage.secureDomains,
          serviceDomains: loadedData.serviceDomains || this.appStorage.serviceDomains,
          graphURL: loadedData.graphURL || this.appStorage.graphURL
        }
        
        if (!this.appStorage.setting) {
          this.updateSetting({
            screenOn: false,
            emailSubscription: false,
            displayMode: Api.displayModes.red,
            language: "SC",
            clearCache: true,
          })
        }
      })
    }
  }

  async saveToStorage() {
    save("appStorage", this.appStorage).then(() => {
      console.log("save appStorage successfully")
    })
  }

  async saveToStorageWithCallback(callback: any) {
    save("appStorage", this.appStorage).then(() => {
      console.log("save appStorage successfully")
      callback()
    })
  }

  reset(callback: any = null) {
    remove("appStorage").then(() => {
      runInAction(() => {
        this.appStorage = {
          accessToken: "",
          refreshToken: "",
          userId: 0,
          userDevice: null,
          userOtherInfo: null,
          setting: {
            screenOn: this.appStorage.setting.screenOn,
            emailSubscription: this.appStorage.setting.emailSubscription,
            displayMode: Api.displayModes.red,
            language: this.appStorage.setting.language,
            clearCache: true,
          },
          showLoading: false,
          showMessage: false,
          storedUsers: storedUserList,
          screenNameNavigateAuto: "",
          currentTrendScreenIndex: 1,
          currentInformationScreenIndex: 1,
          inboxBatchCount: 0,
          popupData: null,
          popupDataIsShown: false,
          popupShownCount: 0,
          iconChanged: false,
          notificationPromptShown: false,
          currentScreen: "",
          showNotificationPrompt: false,
          pIp: "",
          networkStatus: true,
          globalMessage: "",
          secureDomains: this.appStorage.secureDomains,
          serviceDomains: this.appStorage.serviceDomains,
          graphURL: this.appStorage.graphURL,
          awaitingUserRetry: false
        }
      })
      if (callback) {
        callback()
      }
    })
  }
}

const AppStorage = new AppStorageImpl()
AppStorage.initializeFromAsyncStorage()
export { AppStorage }