import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { ROW_INFO, TEXT_CONTENT, TEXT_SCREEN_TITLE } from "../../theme/baseStyle"
import { FLEX, MARGIN_DF_TOP, MARGIN_S_DF_RIGHT, ROW_CENTER, TEXT_CENTER, TEXT_COLOR_GRAY, W_100P } from "../../theme/mStyle"
import { View } from "react-native"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useNetInfo } from "@react-native-community/netinfo"

interface BankInformationScreenProps extends AppStackScreenProps<"BankInformation"> {}
export const BankInformationScreen: FC<BankInformationScreenProps> = observer(function BankInformationScreen(_props) {
  
  const prepareData = () => {
    console.log(JSON.stringify(AppStorage.appStorage.userDevice))
  }

  const getContent = (key: string) => {
    if (AppStorage.appStorage.userOtherInfo) {
      if (Helper.isLiveAccount()) {
        if (key == "bank_name") {
          return AppStorage.appStorage.userOtherInfo.data.bank_name
        } else if (key == "bank_branch") {
          return AppStorage.appStorage.userOtherInfo.data.bank_branch
        } else if (key == "bank_username") {
          return AppStorage.appStorage.userOtherInfo.data.bank_username
        } else if (key == "bank_account") {
          return AppStorage.appStorage.userOtherInfo.data.bank_account
        }  
      } else {
        return "-"
      }
    } else {
      return "-"
    }
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    prepareData()
  }, [])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false} 
        title={translate("account.bankInfo")} />
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.bankName")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("bank_name")} />
      </View>
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.bankBranchName")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("bank_branch")} />
      </View>   
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.accountHolderName")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("bank_username")} />
      </View>   
      <View style={ROW_INFO}>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={translate("account.bankCardNumber")} />
        <View style={FLEX}/>
        <Text style={[TEXT_CONTENT, MARGIN_DF_TOP, TEXT_COLOR_GRAY]} text={getContent("bank_account")} />
      </View>      
    </Screen>
  )
})