{"config": {"emas.appKey": "334487989", "emas.appSecret": "bf9dcb5631724a648f0cb7112a122079", "emas.packageName": "com.sinosound.mobile2", "hotfix.idSecret": "334487989-1", "hotfix.rsaSecret": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCooxsNdlILY4jkQrCGIMTmJ+y57P/wWx+CwhvCS13qje6JfnvSg/hUoA+tDGrI99l+PuxcC4ze6p6p9o3q9s4/4eoedNie7GJYwejrdT/BIcF4D5U3dvz8kYpD22AZqHjCgPCIMskqfD3EiEFrR8jIkxcvVcGF/UsGedbYetBzp1bIvfqDjPfAo4HrgVxkz0n0CaPmXk07ZRQ6H5M9yKWW06Tm3Dh8c+Ux1fSFSRei75ZSsRUOjo10eNyxeAcdqEtnBLegoTfmHTRhdKyGSkjr5QArAN+epzVgjHuaeSIXPYS7+v+14D5I72gPo4MwTzOGCiv3Z03fCXmZw1MDFCZpAgMBAAECggEBAJtPjKy7WhiPFRZUrUr6hx0JjCY4NgdhhQXiS7qHuvk4uoX67YMWc/FMcOOJWhe43KA+x2SGZrkvD+2s20uY/p8rK3ndV1wWjQT9ocyo8Yyn0A0JHP42Ig1i5RpVwo4e8QkBw4VMcKUmQ6TrcISjWjIP9fxsWI1UmHJgaBR8F9tKtOzBSB8x6i0GQ40TsweqMifotolyxQbSDCN00go0Rwj66f8adKoEIbszZoaDtQxpmgaBzsjOnok7F42ghx0woVELkDte0FVEftejSPFXQ3WxckbjAkAin50lejMsdRIPZIr4iv30sJKF6SoEUjxyUEjAIcn+nHpMPR5ROB56R/kCgYEA4I3IHJ0hl4gCtaWJr3tSstLLU4Fn5fxkwiWlYlT3VhX+VKzu4jDlXzfrCCD4m+V2XebahOf6b35YswLl4XPyb0uT5GoVnZ/SH9jxo6OUXKE7hNx0T1WEj5FFC3SWqhnCN1nzKok9o3BsnfQ7atlJ4kZC2k4HLKBqsse+MZlVS/8CgYEAwEC4JI8h0P62+CYTXNInbLLnrZoROUEo6Af4PxRoskd5K4EnkmstS6S2YfIcTK1wPD8Z2dzon+rWMttvhqfR4EYJhorTP7rMx9Cho01nyQiNZ2wDOTstdKuWbpNvqZykFaq5D/Ud+9lXFFBvLVTWoBmYmjSO6yV9yjG0JOaXrZcCgYEAngYo1Toe8axde8dK5Zs7z2xYeiOPImJD5fzoLhLtHD/OA3jPg441XJTVaZsKWWtZqStUjENUEjIv6YoYjFg0O+5L3WpqW8qk7JSXODldpvIwGIerskjUSNRKao1ZmcsWyi0a2sxJ/EKZrW66ga74N6yKT2ap1dXuQHIgfncRd7ECgYAF4/XAaQJIPQWuJLyDI41Br1mZ4BaXAoAWW+gnRqDOlZII2zQzMEV7A9jyt1BWqRm/Ps+z9SjLfY6CkGaiYZH0pTC1hptiYH7VwHazYjPD3Rc4C/8tSbri0D8TtEC/maUNZOW2r3Pzu30Hik9dvDO3RBGA6AtY+RUZlNxIJSiQkQKBgD00UfpbUnL6YwMsVfH7LdrwuoBNQkL77cA95uBHdyyHgMvpPc01UOoUnXDqRW4ysYKDComYd3PrnZ2XjCWipO3SG3/rKFYjs2WoXR66tkoh/v9vaIqlagLJdyezivYkPA2J22o9Qu82JW596rHCaVemwSD4mIi1m8BowpMq4l/j", "httpdns.accountId": "180678", "httpdns.secretKey": "5fa0380e2df06e01645ac4c6481319c9", "appmonitor.tlog.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDU7QDtCL9KTdtq+eCZCJgkiS9wyUQ8y+cGkcuWnZXwfHfTaoV7rGlM8lgRYsCYW2HuEioSG9fu35ErLeF6rDqjmnzxY20uYwmhGaxiuxHEleED8R8bVp5kxa2I6xJQMcHml+PL4FGCGBQO59Eevtv7CAcsnCphBcOCJup3f+w7awIDAQAB", "appmonitor.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDU7QDtCL9KTdtq+eCZCJgkiS9wyUQ8y+cGkcuWnZXwfHfTaoV7rGlM8lgRYsCYW2HuEioSG9fu35ErLeF6rDqjmnzxY20uYwmhGaxiuxHEleED8R8bVp5kxa2I6xJQMcHml+PL4FGCGBQO59Eevtv7CAcsnCphBcOCJup3f+w7awIDAQAB"}, "services": {"hotfix_service": {"status": 0, "version": "3.4.1"}, "ha-adapter_service": {"status": 0, "version": "*******-open"}, "feedback_service": {"status": 0, "version": "3.4.2"}, "tlog_service": {"status": 0, "version": "*******-open"}, "httpdns_service": {"status": 0, "version": "2.3.5"}, "apm_service": {"status": 0, "version": "1.1.4.0-open"}, "man_service": {"status": 0, "version": "1.2.7"}, "cps_service": {"status": 1, "version": "3.8.8.1"}}, "use_maven": true, "proguard_keeplist": "\n#httpdns\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#cps\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n-keepclasseswithmembernames class ** {\nnative <methods>;\n}\n-keepattributes Signature\n-keep class sun.misc.Unsafe { *; }\n-keep class com.alipay.** {*;}\n-dontwarn com.alipay.**\n-keep class anet.**{*;}\n-keep class org.android.spdy.**{*;}\n-keep class org.android.agoo.**{*;}\n-dontwarn anet.**\n-dontwarn org.android.spdy.**\n-dontwarn org.android.agoo.**\n\n#hotfix\n#基线包使用，生成mapping.txt\n-printmapping mapping.txt\n#生成的mapping.txt在app/buidl/outputs/mapping/release路径下，移动到/app路径下\n#修复后的项目使用，保证混淆结果一致\n#-applymapping mapping.txt\n#hotfix\n-keep class com.taobao.sophix.**{*;}\n-keep class com.ta.utdid2.device.**{*;}\n#防止inline\n-dontoptimize\n\n#man\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#feedback\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n"}