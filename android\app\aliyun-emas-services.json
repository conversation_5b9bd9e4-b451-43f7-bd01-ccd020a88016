{"config": {"emas.appKey": "335417057", "emas.appSecret": "2f01a429684c4234a757238621109323", "emas.packageName": "com.sinosound.mobile2", "hotfix.idSecret": "335417057-1", "hotfix.rsaSecret": "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCrt/6dnHMT6ZNLI4lq6Q2+SolNZFdfypBfHGKwJ5VrqttSbIQyzmVo4sxY+WtaAamm7lt2PoJq2xMRGyaB637Gnj/q9Kp4KE7uR/Em0/OhCe3Zgm2/1XYZ62EvY0WLW6YBxVlJ2dRv1O/lA4IZkUfxdxLd5rUG9MW3NxBbuPqvhRZt4iwsT/X2zt4n9IEvyLkzWzBEQ4M7t3/xKTjElmbwt/ALPaV6nbaXYqW31ubId9RJk8eQyjVNAmk1mT0X1rXVHO0psYNLX31qXxGVxWS/KBIQAMMCM64cjZHLqgTHmAmGME/Y83V7PqxXmgpvHlyJ63rmXXtk9JbIPkAewwg9AgMBAAECggEAFTaFnbLWBH+2UnHxZ+Oibv+GNqlOoebELvuytVAuhT52GZiOPDiDDpH17IfjWxsZeiW35sOSGOE3WcWg3CvbXW3vN3dCF9Xi6ky43bMRRbOUTmpuZGRaUplhSBlbXfvUZiObBCSEucv0VrY4PDBWUt4C1qbJdjKGujtzKeGQFl6GT9QdUU4ZNAQ4NziUNo85O6N+WUZKmFEK+k6vIO0nS11z1LvPGXy0rtCIkN4943YS0npevUIAmfg6Dsb8FwWmB0fhzLX+jpCEHw4ul2G5z56YRwfvA2jdb3nniqN1NZ8/NFr+Whi1CC+btfmooKBlsB9gpsjC5NWhsJqgLyvdgQKBgQDt2BZ+4araXRC9nQnmuoKl/RPqBIZduYv4EKKp8S8rArVkwayRCvDJlO26du8MkQCnHLLl21poIaDjP47HdzPkCdpkLEfQib+ytrLEKSmXmqMLq0E+GFcXinWCZ+rNjeHE3USu1KExM4HPQu6ZfoyHPJzz54bTDvmFiKSf7EujVwKBgQC407H2/F71FUVUSqNpaQXUIiUPtfq/Wth0SviZdm4OofhzcIjdH12Nh9tjnssNxH6RFC09CmjJP9hKCj67gjmt+FqIVrdBSLyLeWscrd0HSCy+X66h5hIwnX+Nums8gOgz2axNYK7hFwWYGQ2is5KlOOjPp4VnJaXdck86s5poiwKBgHrMv8ig4SK/PD56DzumfoAPK7H+xhUsEJ1ykVmm4hCV5GvyJxP4Dxw1HYzRjVjyoOCnS6Kr34C2JChQj3pjDFKn168ywZ1lEanG9IOdMRFKUPZcZGIYFRddcJB08OFSaQfIqoaN1+/cRSBq0WBE22+TlwHDP3RHXgNY6o57ZQ4fAoGAYn1UiUPEqh8Sma9IUwSG+ZrUWwHjQ5L6Y0YMIWLtlNiU15cxJ3vuU1wA+Z4FoCmENHUnAi8I1a0y6Yl6Elt7twvv2lUzRBOpfT1PabHwZU/9n9jG6zj5mnRjIzgiqtQaaH7nBTivF/03muxGytucGnUwAm/Hc8apE081jxdkAa8CgYBHWQjUvAcapdejG35tOARaIEHRw3+GGerXGUsg586v9TgIyFtuVaN1AI1IDtukhEeSNoy0/pwloE+oVlIp83iU/1kUYS5rQGbAze/r8SxJJa4uk7itZ7pOy8JoP5g7yLCqI65Duz/DYQybk5wvrzUNmTnc6bnUGvt81iJYuW57hw==", "httpdns.accountId": "156971", "httpdns.secretKey": "d6f2f59813a8ce549f22bf386b51f142", "appmonitor.tlog.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC15Z2UVOw1F+Effs9NAp/waolJWXS89lgSHVyjDgnJTKK1tSr97CNPTdvwIDzTwVBqibGh5stcokrgPCGqPaTiQ3GCG/9Y+OYSKy+kAW74blRkYdwBcErCMVh3Vgq07cPs5PJiD2aQ1Fz8CCVzyuWjjrIibMZlVbuqE1w//7fZQQIDAQAB", "appmonitor.rsaSecret": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC15Z2UVOw1F+Effs9NAp/waolJWXS89lgSHVyjDgnJTKK1tSr97CNPTdvwIDzTwVBqibGh5stcokrgPCGqPaTiQ3GCG/9Y+OYSKy+kAW74blRkYdwBcErCMVh3Vgq07cPs5PJiD2aQ1Fz8CCVzyuWjjrIibMZlVbuqE1w//7fZQQIDAQAB"}, "services": {"hotfix_service": {"status": 0, "version": "3.4.1"}, "ha-adapter_service": {"status": 0, "version": "*******-open"}, "feedback_service": {"status": 0, "version": "3.4.2"}, "tlog_service": {"status": 0, "version": "*******-open"}, "httpdns_service": {"status": 0, "version": "2.3.5"}, "apm_service": {"status": 0, "version": "1.1.4.0-open"}, "man_service": {"status": 0, "version": "1.2.7"}, "cps_service": {"status": 1, "version": "3.8.8.1"}}, "use_maven": true, "proguard_keeplist": "\n#httpdns\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#cps\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n-keepclasseswithmembernames class ** {\nnative <methods>;\n}\n-keepattributes Signature\n-keep class sun.misc.Unsafe { *; }\n-keep class com.alipay.** {*;}\n-dontwarn com.alipay.**\n-keep class anet.**{*;}\n-keep class org.android.spdy.**{*;}\n-keep class org.android.agoo.**{*;}\n-dontwarn anet.**\n-dontwarn org.android.spdy.**\n-dontwarn org.android.agoo.**\n\n#hotfix\n#基线包使用，生成mapping.txt\n-printmapping mapping.txt\n#生成的mapping.txt在app/buidl/outputs/mapping/release路径下，移动到/app路径下\n#修复后的项目使用，保证混淆结果一致\n#-applymapping mapping.txt\n#hotfix\n-keep class com.taobao.sophix.**{*;}\n-keep class com.ta.utdid2.device.**{*;}\n#防止inline\n-dontoptimize\n\n#man\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n\n#feedback\n-keep class com.taobao.** {*;}\n-keep class com.alibaba.** {*;}\n-keep class com.ta.**{*;}\n-keep class com.ut.**{*;}\n-dontwarn com.taobao.**\n-dontwarn com.alibaba.**\n-dontwarn com.ta.**\n-dontwarn com.ut.**\n"}