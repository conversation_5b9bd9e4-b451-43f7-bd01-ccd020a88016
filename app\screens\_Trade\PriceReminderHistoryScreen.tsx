import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { Screen, Text } from "../../components"
import { AppStackScreenProps } from "../../navigators"
import { MIcon } from "../../components/MIcon"
import { Dimen } from "../../theme/dimen"
import { translate } from "../../i18n"
import { TEXT_SCREEN_TITLE, TEXT_SMALL} from "../../theme/baseStyle"
import { AI_CENTER, FLEX, MARGIN_DF_BOTTOM, MARGIN_DF_LEFT, MARGIN_DF_TOP, ROW, TEXT_CENTER, W_100P, } from "../../theme/mStyle"
import { FlatList, TouchableOpacity } from "react-native"
import { ApiService } from "app/api/api"
import { getPriceReminders } from "app/api/model"
import { Helper } from "app/utils/helper"
import { AppStorage } from "app/utils/appStorage"
import { CardPriceReminder } from "./CardPriceReminder"
import moment from "moment"
import { BackNavComponent } from "app/components/BackNavComponent"
import { useIsFocused } from "@react-navigation/native"
import { useNetInfo } from "@react-native-community/netinfo"


interface PriceReminderHistoryScreenProps extends AppStackScreenProps<"PriceReminderHistory"> {}
export const PriceReminderHistoryScreen: FC<PriceReminderHistoryScreenProps> = observer(function PriceReminderHistoryScreen(_props) {

  const [reminders, setReminders] = useState([])
  const isScreenFocused = useIsFocused()

  const prepareData = () => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    AppStorage.showLoading()
    getPriceReminders(
      "inactive",
      (response) => {
        AppStorage.hideLoading()
        console.log("loadPriceReminderList: ", response)
        setReminders(response.results)
      },
      (error) => {
        AppStorage.hideLoading()
        ApiService.showShortToast(error)
        console.log("loadPriceReminderList error", error)
      },
      () => {
        _props.navigation.navigate("Logout")
      }
    )
  }

  // useEffect(() => {
  //   prepareData()
  // }, [])

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {      
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    if (isScreenFocused) {
      prepareData()
    }
  }, [isScreenFocused])

  return (
    <Screen
      preset="auto"
      contentContainerStyle={{ padding: Dimen.padding.base }}
      safeAreaEdges={["top", "bottom"]}
    >
      <BackNavComponent 
        onBackButtonPressed={() => {
          _props.navigation.goBack()
        }} 
        hasPadding={false}
        isTitleCenter={false}
        containerStyle={MARGIN_DF_BOTTOM}
        title={translate("tradePage.promptRecord")} />   
      <FlatList
          style={{width: Dimen.screenWidth}}
          data={reminders}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({item}) => {
            return (
              <CardPriceReminder 
                onClicked={() => {
                  _props.navigation.navigate("NewPriceReminder", {
                    onGoBack: () => {
                      console.log("onGoBack")
                      prepareData()
                    },
                    reminderObj: item
                  })
                }}
                data={item} />
            )
          }}/>
      <Text 
      style={[TEXT_SMALL, TEXT_CENTER, MARGIN_DF_TOP]} 
      text={"-"+ translate("tradePage.showPromtRecordTo") + " " + moment().add(-1, "month").format(Helper.dateFormateList.dateApi) + "-"} />
    </Screen>
  )
})
