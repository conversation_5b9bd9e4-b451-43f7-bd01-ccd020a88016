// import { Text } from "app/components"
import { Text } from "../components"
// import { Dimen } from "app/theme/dimen"
// import { COLUMN, ROW } from "app/theme/mStyle"
import { Dimen } from "../theme/dimen"
import { COLUMN, ROW } from "../theme/mStyle"
import * as React from "react"
import { useEffect, useState } from "react"
import { TextStyle, TouchableOpacity, View, ScrollView, ViewStyle, Platform } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
// import { colors } from "app/theme"
import { colors } from "../theme"
import { Mheader } from "./Mheader"
// import { translate } from "app/i18n"
import { translate } from "../i18n"
import { AppStorage } from "app/utils/appStorage"
import { StatusBar } from "expo-status-bar"
import { FSIZE_15 } from "../theme/baseStyle"
import { observer } from "mobx-react-lite"


const MenuMainContent: ViewStyle = {
    ...ROW,
    paddingHorizontal: Dimen.padding.base,
    justifyContent: 'space-between',
    backgroundColor: 'white',
}

const MenuItem: ViewStyle = {
    ...COLUMN,
    alignItems: 'center',
    justifyContent: 'center',
}

const MenuItemEN: ViewStyle = {
    ...MenuItem,
    paddingRight: 15
}

const CurrentLine: ViewStyle = {
    width: 30,
    height: 5,
    marginTop: 1,
    marginBottom: 5,
    borderRadius: 50,
    backgroundColor: colors.mine.primary,
}
const HiddenLine: ViewStyle = {
    width: 30,
    height: 5,
    marginTop: 1,
    marginBottom: 5,
    borderRadius: 50,
    backgroundColor: colors.palette.white,
}

const MenuText: TextStyle = {
    fontSize: Platform.OS === "ios" ? 15 : 14,
    textAlign: 'center',
}

export const TopMenuIndex = {
    marketNews: 1,
    calendar: 2,
    latestNotification: 3,
    todayTopics: 4,
    latestNews: 5,
}

export const TopMenu = observer(function TopMenu(props: any) {
    const insets = useSafeAreaInsets()

    const switchTab = (index: number) => {
        AppStorage.setCurrentTrendScreenIndex(index)
        if (index === 1) {
            props.navigation.navigate("MarketNews")
            AppStorage.setScreenNameNavigateAuto("")
        } else if (index === 2) {
            props.navigation.navigate("Calendar")
            AppStorage.setScreenNameNavigateAuto("")
        } else if (index === 3) {
            props.navigation.navigate("LatestNotification")
        } else if (index === 4) {
            props.navigation.navigate("TodayTopics")
        } else if (index === 5) {
            props.navigation.navigate("LatestNews")
        }
    }

    let menuItems = (
        <>
            <TouchableOpacity
                onPress={() => switchTab(1)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="trendPage.news" />
                <View style={AppStorage.appStorage.currentTrendScreenIndex == 1 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(2)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="trendPage.calendar" />
                <View style={AppStorage.appStorage.currentTrendScreenIndex == 2 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(3)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="homePage.latestNotification" />
                <View style={AppStorage.appStorage.currentTrendScreenIndex == 3 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(4)}
                style={AppStorage.appStorage.setting.language == 'EN' ? MenuItemEN : MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="homePage.todayTopics" />
                <View style={AppStorage.appStorage.currentTrendScreenIndex == 4 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
            <TouchableOpacity
                onPress={() => switchTab(5)}
                style={MenuItem}>
                <Text style={[MenuText, FSIZE_15]} tx="homePage.latestNews" />
                <View style={AppStorage.appStorage.currentTrendScreenIndex == 5 ? CurrentLine : HiddenLine}></View>
            </TouchableOpacity>
        </>
    );
    if (AppStorage.appStorage.setting.language == 'EN') {
        menuItems = (<>
            <ScrollView horizontal={true}>
                {menuItems}
            </ScrollView>
        </>
        )
    }

    const getPostType = () => {
        if (AppStorage.appStorage.currentTrendScreenIndex == 4) {
            return 5
        } else if (AppStorage.appStorage.currentTrendScreenIndex == 5) {
            return 6
        } else {
            return 0
        }
    }

    return (
        <View style={COLUMN}>
            <Mheader
                containerStyle={{ paddingTop: insets.top }}
                leftTitle={translate("homePage.trend")}
                showSearchIcon={AppStorage.appStorage.currentTrendScreenIndex == 4 || AppStorage.appStorage.currentTrendScreenIndex == 5}
                onSearchIconClicked={() => {
                    props.navigation.navigate("SearchPost", { postType: getPostType() })
                }}
                navigator={props.navigation} />
            <View style={MenuMainContent} >
                {menuItems}
            </View>
        </View >
    )
})


